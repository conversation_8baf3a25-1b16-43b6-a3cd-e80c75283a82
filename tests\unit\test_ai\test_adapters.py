"""
AI适配器测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from src.core.ai.base import (
    AIProvider, GenerationType, GenerationContext, GenerationOptions,
    AuthenticationError, RateLimitError, AIServiceError
)


class TestOpenAIAdapter:
    """测试OpenAI适配器"""
    
    @pytest.fixture
    def adapter(self):
        """创建OpenAI适配器实例"""
        from src.core.ai.adapters.openai_adapter import OpenAIAdapter
        return OpenAIAdapter(api_key="test-key", model="gpt-4")
    
    def test_init(self, adapter):
        """测试初始化"""
        assert adapter.provider == AIProvider.OPENAI
        assert adapter.api_key == "test-key"
        assert adapter.model == "gpt-4"
        assert adapter._default_base_url == "https://api.openai.com/v1"
    
    def test_init_without_openai_library(self):
        """测试没有OpenAI库时的初始化"""
        with patch('src.core.ai.adapters.openai_adapter.OPENAI_AVAILABLE', False):
            from src.core.ai.adapters.openai_adapter import OpenAIAdapter
            
            with pytest.raises(ImportError, match="OpenAI库未安装"):
                OpenAIAdapter(api_key="test-key")
    
    @pytest.mark.asyncio
    async def test_initialize(self, adapter):
        """测试初始化客户端"""
        with patch('src.core.ai.adapters.openai_adapter.AsyncOpenAI') as mock_client:
            await adapter.initialize()
            
            mock_client.assert_called_once_with(
                api_key="test-key",
                base_url="https://api.openai.com/v1"
            )
            assert adapter._client is not None
    
    @pytest.mark.asyncio
    async def test_generate_content_success(self, adapter):
        """测试成功生成内容"""
        # 模拟OpenAI响应
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "生成的内容"
        mock_response.choices[0].finish_reason = "stop"
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 20
        mock_response.usage.total_tokens = 30
        mock_response.created = 1234567890
        mock_response.id = "test-response-id"
        
        mock_client = AsyncMock()
        mock_client.chat.completions.create.return_value = mock_response
        adapter._client = mock_client
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        options = GenerationOptions()
        
        result = await adapter.generate_content("测试提示", context, options)
        
        assert result.content == "生成的内容"
        assert result.provider == AIProvider.OPENAI
        assert result.model == "gpt-4"
        assert result.usage["total_tokens"] == 30
        assert result.finish_reason == "stop"
        assert result.metadata["response_id"] == "test-response-id"
    
    @pytest.mark.asyncio
    async def test_generate_content_stream(self, adapter):
        """测试流式生成内容"""
        # 模拟流式响应
        mock_chunks = [
            MagicMock(choices=[MagicMock(delta=MagicMock(content="第一"), finish_reason=None)]),
            MagicMock(choices=[MagicMock(delta=MagicMock(content="部分"), finish_reason=None)]),
            MagicMock(choices=[MagicMock(delta=MagicMock(content="内容"), finish_reason="stop")])
        ]
        
        async def mock_stream():
            for chunk in mock_chunks:
                yield chunk
        
        mock_client = AsyncMock()
        mock_client.chat.completions.create.return_value = mock_stream()
        adapter._client = mock_client
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        options = GenerationOptions()
        
        chunks = []
        async for chunk in adapter.generate_content_stream("测试提示", context, options):
            chunks.append(chunk)
        
        assert len(chunks) == 3
        assert chunks[0].delta == "第一"
        assert chunks[0].content == "第一"
        assert chunks[1].delta == "部分"
        assert chunks[1].content == "第一部分"
        assert chunks[2].delta == "内容"
        assert chunks[2].content == "第一部分内容"
        assert chunks[2].finish_reason == "stop"
    
    @pytest.mark.asyncio
    async def test_validate_api_key_success(self, adapter):
        """测试API密钥验证成功"""
        mock_client = AsyncMock()
        mock_client.chat.completions.create.return_value = MagicMock()
        adapter._client = mock_client
        
        result = await adapter.validate_api_key()
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_api_key_failure(self, adapter):
        """测试API密钥验证失败"""
        mock_client = AsyncMock()
        mock_client.chat.completions.create.side_effect = Exception("Invalid API key")
        adapter._client = mock_client

        result = await adapter.validate_api_key()
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, adapter):
        """测试获取可用模型"""
        mock_models = MagicMock()
        mock_models.data = [
            MagicMock(id="gpt-4"),
            MagicMock(id="gpt-3.5-turbo"),
            MagicMock(id="text-davinci-003")  # 不包含gpt，应该被过滤
        ]
        
        mock_client = AsyncMock()
        mock_client.models.list.return_value = mock_models
        adapter._client = mock_client
        
        models = await adapter.get_available_models()
        assert "gpt-4" in models
        assert "gpt-3.5-turbo" in models
        assert "text-davinci-003" not in models
    
    @pytest.mark.asyncio
    async def test_estimate_tokens(self, adapter):
        """测试token估算"""
        # 测试英文文本
        english_text = "Hello world"
        tokens = await adapter.estimate_tokens(english_text)
        assert tokens == len(english_text) // 4

        # 测试中文文本
        chinese_text = "你好世界"
        tokens = await adapter.estimate_tokens(chinese_text)
        assert tokens == len(chinese_text) // 1.5

        # 测试混合文本
        mixed_text = "Hello 世界"
        tokens = await adapter.estimate_tokens(mixed_text)
        expected = 6 // 4 + 2 // 1.5  # "Hello " + "世界"
        assert tokens == int(expected)
    
    def test_build_messages(self, adapter):
        """测试构建消息"""
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        messages = adapter._build_messages("测试提示", context)
        
        assert len(messages) >= 1
        assert messages[-1]["role"] == "user"
        assert messages[-1]["content"] == "测试提示"
        
        # 检查是否有系统消息
        if len(messages) > 1:
            assert messages[0]["role"] == "system"
    
    def test_build_system_prompt(self, adapter):
        """测试构建系统提示词"""
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        prompt = adapter._build_system_prompt(context)
        
        assert "小说创作助手" in prompt
        assert "角色描述" in prompt
    
    def test_handle_error_authentication(self, adapter):
        """测试处理认证错误"""
        # 创建一个简单的异常来测试错误处理
        original_error = Exception("Invalid API key")
        handled_error = adapter._handle_error(original_error)

        # 由于没有实际的OpenAI库，这里只测试基本的错误处理
        assert isinstance(handled_error, Exception)

    def test_handle_error_rate_limit(self, adapter):
        """测试处理速率限制错误"""
        # 创建一个简单的异常来测试错误处理
        original_error = Exception("Rate limit exceeded")
        handled_error = adapter._handle_error(original_error)

        # 由于没有实际的OpenAI库，这里只测试基本的错误处理
        assert isinstance(handled_error, Exception)


class TestAnthropicAdapter:
    """测试Anthropic适配器"""
    
    @pytest.fixture
    def adapter(self):
        """创建Anthropic适配器实例"""
        from src.core.ai.adapters.anthropic_adapter import AnthropicAdapter
        return AnthropicAdapter(api_key="test-key", model="claude-3-haiku-20240307")
    
    def test_init(self, adapter):
        """测试初始化"""
        assert adapter.provider == AIProvider.ANTHROPIC
        assert adapter.api_key == "test-key"
        assert adapter.model == "claude-3-haiku-20240307"
        assert adapter._default_base_url == "https://api.anthropic.com"
    
    def test_init_without_anthropic_library(self):
        """测试没有Anthropic库时的初始化"""
        with patch('src.core.ai.adapters.anthropic_adapter.ANTHROPIC_AVAILABLE', False):
            from src.core.ai.adapters.anthropic_adapter import AnthropicAdapter
            
            with pytest.raises(ImportError, match="Anthropic库未安装"):
                AnthropicAdapter(api_key="test-key")
    
    @pytest.mark.asyncio
    async def test_generate_content_success(self, adapter):
        """测试成功生成内容"""
        # 模拟Anthropic响应
        mock_response = MagicMock()
        mock_response.content = [MagicMock(text="生成的内容")]
        mock_response.usage.input_tokens = 10
        mock_response.usage.output_tokens = 20
        mock_response.stop_reason = "end_turn"
        mock_response.id = "test-response-id"
        
        mock_client = AsyncMock()
        mock_client.messages.create.return_value = mock_response
        adapter._client = mock_client
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        options = GenerationOptions()
        
        result = await adapter.generate_content("测试提示", context, options)
        
        assert result.content == "生成的内容"
        assert result.provider == AIProvider.ANTHROPIC
        assert result.model == "claude-3-haiku-20240307"
        assert result.usage["total_tokens"] == 30
        assert result.finish_reason == "end_turn"
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, adapter):
        """测试获取可用模型"""
        models = await adapter.get_available_models()

        # 应该返回已知的Claude模型
        assert "claude-3-opus-20240229" in models
        assert "claude-3-sonnet-20240229" in models
        assert "claude-3-haiku-20240307" in models
    
    def test_build_system_prompt(self, adapter):
        """测试构建系统提示词"""
        context = GenerationContext(content_type=GenerationType.SCENE_DESCRIPTION)
        prompt = adapter._build_system_prompt(context)
        
        assert "小说创作助手" in prompt
        assert "中文回答" in prompt
        assert "场景描述" in prompt


class TestDeepSeekAdapter:
    """测试DeepSeek适配器"""
    
    @pytest.fixture
    def adapter(self):
        """创建DeepSeek适配器实例"""
        from src.core.ai.adapters.deepseek_adapter import DeepSeekAdapter
        return DeepSeekAdapter(api_key="test-key", model="deepseek-chat")
    
    def test_init(self, adapter):
        """测试初始化"""
        assert adapter.provider == AIProvider.DEEPSEEK
        assert adapter.api_key == "test-key"
        assert adapter.model == "deepseek-chat"
        assert adapter._default_base_url == "https://api.deepseek.com/v1"
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, adapter):
        """测试获取可用模型"""
        models = await adapter.get_available_models()

        assert "deepseek-chat" in models
        assert "deepseek-coder" in models
        assert "deepseek-reasoner" in models
    
    def test_build_system_prompt(self, adapter):
        """测试构建系统提示词"""
        context = GenerationContext(content_type=GenerationType.DIALOGUE)
        prompt = adapter._build_system_prompt(context)
        
        assert "DeepSeek" in prompt
        assert "对话" in prompt


class TestZhipuAdapter:
    """测试智谱AI适配器"""
    
    @pytest.fixture
    def adapter(self):
        """创建智谱AI适配器实例"""
        from src.core.ai.adapters.zhipu_adapter import ZhipuAdapter
        return ZhipuAdapter(api_key="test-key", model="glm-4")
    
    def test_init(self, adapter):
        """测试初始化"""
        assert adapter.provider == AIProvider.ZHIPU
        assert adapter.api_key == "test-key"
        assert adapter.model == "glm-4"
        assert adapter._default_base_url == "https://open.bigmodel.cn/api/paas/v4"
    
    @pytest.mark.asyncio
    async def test_get_available_models(self, adapter):
        """测试获取可用模型"""
        models = await adapter.get_available_models()

        assert "glm-4" in models
        assert "glm-4-air" in models
        assert "glm-4-flash" in models
        assert "glm-3-turbo" in models
    
    def test_build_system_prompt(self, adapter):
        """测试构建系统提示词"""
        context = GenerationContext(content_type=GenerationType.CONTENT_OPTIMIZATION)
        prompt = adapter._build_system_prompt(context)
        
        assert "智谱AI" in prompt
        assert "GLM" in prompt
        assert "优化" in prompt
