"""
存储管理器测试模块
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from uuid import uuid4

from src.core.storage.manager import StorageManager, StorageError
from src.core.models.project import WritingProject
from src.core.models.character import Character
from src.core.models.scene import Scene
from src.core.models.event import Event


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def storage_manager(temp_dir):
    """创建存储管理器实例"""
    return StorageManager(temp_dir)


@pytest.fixture
def sample_project():
    """创建示例项目"""
    return WritingProject(
        name="测试项目",
        author="测试作者",
        description="测试描述"
    )


@pytest.fixture
def sample_character():
    """创建示例角色"""
    return Character(
        name="测试角色",
        full_name="测试角色全名"
    )


class TestStorageManager:
    """存储管理器测试类"""
    
    def test_init_default(self, temp_dir):
        """测试默认初始化"""
        manager = StorageManager(temp_dir)
        
        assert manager.base_path == Path(temp_dir)
        assert manager.enable_cache is True
        assert manager.enable_backup is True
        assert manager.enable_version_control is True
        assert manager.enable_validation is True
        
    def test_init_custom_options(self, temp_dir):
        """测试自定义选项初始化"""
        manager = StorageManager(
            temp_dir,
            enable_cache=False,
            enable_backup=False,
            enable_version_control=False,
            enable_validation=False
        )
        
        assert manager.enable_cache is False
        assert manager.enable_backup is False
        assert manager.enable_version_control is False
        assert manager.enable_validation is False
        
    def test_save_project(self, storage_manager, sample_project):
        """测试保存项目"""
        project_path = storage_manager.save_project(sample_project)
        
        assert project_path is not None
        assert Path(project_path).exists()
        
    def test_load_project(self, storage_manager, sample_project):
        """测试加载项目"""
        # 先保存项目
        project_path = storage_manager.save_project(sample_project)
        
        # 再加载项目
        loaded_project = storage_manager.load_project(project_path)
        
        assert loaded_project is not None
        assert loaded_project.name == sample_project.name
        assert loaded_project.author == sample_project.author
        
    def test_save_character(self, storage_manager, sample_project, sample_character):
        """测试保存角色"""
        # 先保存项目
        project_path = storage_manager.save_project(sample_project)
        
        # 保存角色
        character_path = storage_manager.save_character(sample_project.id, sample_character)
        
        assert character_path is not None
        assert Path(character_path).exists()
        
    def test_load_character(self, storage_manager, sample_project, sample_character):
        """测试加载角色"""
        # 先保存项目和角色
        storage_manager.save_project(sample_project)
        character_path = storage_manager.save_character(sample_project.id, sample_character)
        
        # 加载角色
        loaded_character = storage_manager.load_character(sample_project.id, sample_character.id)
        
        assert loaded_character is not None
        assert loaded_character.name == sample_character.name
        
    def test_list_projects(self, storage_manager, sample_project):
        """测试列出项目"""
        # 保存项目
        storage_manager.save_project(sample_project)
        
        # 列出项目
        projects = storage_manager.list_projects()
        
        assert len(projects) >= 1
        assert any(p.name == sample_project.name for p in projects)
        
    def test_delete_project(self, storage_manager, sample_project):
        """测试删除项目"""
        # 保存项目
        project_path = storage_manager.save_project(sample_project)
        
        # 删除项目
        storage_manager.delete_project(sample_project.id)
        
        # 验证项目已删除
        assert not Path(project_path).exists()
        
    def test_backup_project(self, storage_manager, sample_project):
        """测试备份项目"""
        # 保存项目
        storage_manager.save_project(sample_project)
        
        # 备份项目
        backup_path = storage_manager.backup_project(sample_project.id)
        
        assert backup_path is not None
        assert Path(backup_path).exists()
        
    def test_restore_project(self, storage_manager, sample_project):
        """测试恢复项目"""
        # 保存和备份项目
        storage_manager.save_project(sample_project)
        backup_path = storage_manager.backup_project(sample_project.id)
        
        # 删除项目
        storage_manager.delete_project(sample_project.id)
        
        # 恢复项目
        restored_path = storage_manager.restore_project(backup_path)
        
        assert restored_path is not None
        assert Path(restored_path).exists()
        
    def test_get_project_statistics(self, storage_manager, sample_project):
        """测试获取项目统计"""
        # 保存项目
        storage_manager.save_project(sample_project)
        
        # 获取统计
        stats = storage_manager.get_project_statistics(sample_project.id)
        
        assert stats is not None
        assert 'character_count' in stats
        assert 'scene_count' in stats
        assert 'event_count' in stats
        
    def test_validate_project_integrity(self, storage_manager, sample_project):
        """测试验证项目完整性"""
        # 保存项目
        storage_manager.save_project(sample_project)
        
        # 验证完整性
        is_valid = storage_manager.validate_project_integrity(sample_project.id)
        
        assert is_valid is True
        
    def test_cache_operations(self, storage_manager, sample_project):
        """测试缓存操作"""
        if not storage_manager.enable_cache:
            pytest.skip("缓存未启用")
            
        # 保存项目（应该被缓存）
        storage_manager.save_project(sample_project)
        
        # 从缓存加载
        cached_project = storage_manager.load_project_from_cache(sample_project.id)
        
        # 注意：这个测试可能需要根据实际缓存实现调整
        # assert cached_project is not None
        
    def test_error_handling_invalid_path(self, temp_dir):
        """测试无效路径错误处理"""
        invalid_path = "/invalid/path/that/does/not/exist"
        
        with pytest.raises(StorageError):
            StorageManager(invalid_path)
            
    def test_error_handling_load_nonexistent_project(self, storage_manager):
        """测试加载不存在项目的错误处理"""
        nonexistent_id = uuid4()
        
        with pytest.raises(StorageError):
            storage_manager.load_project(f"/nonexistent/{nonexistent_id}")
            
    @patch('src.core.storage.manager.FileStorage')
    def test_file_storage_integration(self, mock_file_storage, temp_dir):
        """测试文件存储集成"""
        mock_instance = Mock()
        mock_file_storage.return_value = mock_instance
        
        manager = StorageManager(temp_dir)
        
        # 验证文件存储被正确初始化
        mock_file_storage.assert_called_once()
        
    @patch('src.core.storage.manager.CacheManager')
    def test_cache_manager_integration(self, mock_cache_manager, temp_dir):
        """测试缓存管理器集成"""
        mock_instance = Mock()
        mock_cache_manager.return_value = mock_instance
        
        manager = StorageManager(temp_dir, enable_cache=True)
        
        # 验证缓存管理器被正确初始化
        mock_cache_manager.assert_called_once()
        
    @patch('src.core.storage.manager.BackupManager')
    def test_backup_manager_integration(self, mock_backup_manager, temp_dir):
        """测试备份管理器集成"""
        mock_instance = Mock()
        mock_backup_manager.return_value = mock_instance
        
        manager = StorageManager(temp_dir, enable_backup=True)
        
        # 验证备份管理器被正确初始化
        mock_backup_manager.assert_called_once()
        
    @patch('src.core.storage.manager.VersionControlManager')
    def test_version_control_integration(self, mock_version_control, temp_dir):
        """测试版本控制集成"""
        mock_instance = Mock()
        mock_version_control.return_value = mock_instance
        
        manager = StorageManager(temp_dir, enable_version_control=True)
        
        # 验证版本控制管理器被正确初始化
        mock_version_control.assert_called_once()
        
    def test_concurrent_access(self, storage_manager, sample_project):
        """测试并发访问"""
        import threading
        import time
        
        results = []
        errors = []
        
        def save_project():
            try:
                path = storage_manager.save_project(sample_project)
                results.append(path)
            except Exception as e:
                errors.append(e)
                
        # 创建多个线程同时保存项目
        threads = []
        for i in range(5):
            thread = threading.Thread(target=save_project)
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证结果
        assert len(errors) == 0, f"并发访问出现错误: {errors}"
        assert len(results) > 0, "没有成功保存任何项目"
