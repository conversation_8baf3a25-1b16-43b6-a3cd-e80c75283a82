openapi: 3.0.0
info:
  title: 笔落App项目管理API
  version: 1.0.0
  description: 小说项目管理接口规范

servers:
  - url: http://localhost:8000/api
    description: 本地开发服务器

paths:
  /projects:
    get:
      summary: 获取项目列表
      responses:
        '200':
          description: 项目列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProjectSummary'
    post:
      summary: 创建新项目
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectCreateRequest'
      responses:
        '201':
          description: 项目创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetail'

  /projects/{projectId}:
    get:
      summary: 获取项目详情
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 项目详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetail'
    put:
      summary: 更新项目信息
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectUpdateRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetail'
    delete:
      summary: 删除项目
      parameters:
        - name: projectId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: 删除成功

components:
  schemas:
    ProjectSummary:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          maxLength: 100
        author:
          type: string
          maxLength: 50
        project_type:
          type: string
          enum: [玄幻, 都市, 科幻, 武侠, 历史, 其他]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
        - id
        - name
        - author
        - project_type
        - created_at

    ProjectCreateRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        author:
          type: string
          maxLength: 50
        project_type:
          type: string
          enum: [玄幻, 都市, 科幻, 武侠, 历史, 其他]
        description:
          type: string
          maxLength: 500
      required:
        - name
        - author
        - project_type

    ProjectUpdateRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        description:
          type: string
          maxLength: 500
        cover_image:
          type: string

    ProjectDetail:
      allOf:
        - $ref: '#/components/schemas/ProjectSummary'
        - type: object
          properties:
            description:
              type: string
            cover_image:
              type: string
            version:
              type: string
            settings:
              type: object
          required:
            - description
            - version