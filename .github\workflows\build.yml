# 笔落App构建和发布工作流
name: Build and Release

on:
  push:
    tags:
      - 'v*'
  release:
    types: [published]

jobs:
  # 构建Python包
  build-package:
    name: 构建Python包
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 安装构建依赖
      run: |
        python -m pip install --upgrade pip
        pip install build twine
        
    - name: 构建包
      run: |
        python -m build
        
    - name: 检查包
      run: |
        twine check dist/*
        
    - name: 上传构建产物
      uses: actions/upload-artifact@v3
      with:
        name: python-package
        path: dist/

  # 构建可执行文件
  build-executable:
    name: 构建可执行文件
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 安装系统依赖 (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y xvfb libxkbcommon-x11-0
        
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
        
    - name: 构建可执行文件 (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        pyinstaller --onefile --windowed --name=BambooFall-Windows --icon=resources/icons/app.ico src/main.py
        
    - name: 构建可执行文件 (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        pyinstaller --onefile --windowed --name=BambooFall-macOS --icon=resources/icons/app.icns src/main.py
        
    - name: 构建可执行文件 (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        xvfb-run -a pyinstaller --onefile --name=BambooFall-Linux src/main.py
        
    - name: 上传可执行文件
      uses: actions/upload-artifact@v3
      with:
        name: executable-${{ matrix.os }}
        path: dist/

  # 发布到PyPI
  publish-pypi:
    name: 发布到PyPI
    needs: [build-package]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    
    steps:
    - name: 下载构建产物
      uses: actions/download-artifact@v3
      with:
        name: python-package
        path: dist/
        
    - name: 发布到PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}

  # 发布到GitHub Releases
  publish-github:
    name: 发布到GitHub Releases
    needs: [build-package, build-executable]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    
    steps:
    - name: 下载所有构建产物
      uses: actions/download-artifact@v3
      
    - name: 创建发布包
      run: |
        mkdir release-assets
        cp python-package/* release-assets/
        cp executable-*/* release-assets/
        
    - name: 上传到GitHub Releases
      uses: softprops/action-gh-release@v1
      with:
        files: release-assets/*
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
