"""字段验证规则测试"""

import pytest
import re
from datetime import datetime, timedelta
from uuid import uuid4, UUID
from enum import Enum

from src.core.validation.rules import (
    StringValidationRule, NumericValidationRule, DateTimeValidationRule,
    ListValidationRule, EnumValidationRule, UUIDValidationRule,
    EmailValidationRule, URLValidationRule, FilePathValidationRule,
    CustomValidationRule
)
from src.core.validation.validators import ValidationContext
from src.core.models.character import Character


class TestEnum(Enum):
    """测试枚举"""
    VALUE1 = "value1"
    VALUE2 = "value2"
    VALUE3 = "value3"


class TestStringValidationRule:
    """测试字符串验证规则"""
    
    def test_init_default(self):
        """测试默认初始化"""
        rule = StringValidationRule("test_rule")
        
        assert rule.name == "test_rule"
        assert rule.min_length is None
        assert rule.max_length is None
        assert rule.pattern is None
        assert rule.required is False
        assert rule.strip_whitespace is True
    
    def test_init_with_params(self):
        """测试带参数初始化"""
        pattern = re.compile(r'^[a-zA-Z]+$')
        rule = StringValidationRule(
            "test_rule",
            min_length=2,
            max_length=10,
            pattern=pattern,
            required=True,
            strip_whitespace=False
        )
        
        assert rule.min_length == 2
        assert rule.max_length == 10
        assert rule.pattern == pattern
        assert rule.required is True
        assert rule.strip_whitespace is False
    
    def test_validate_none_not_required(self):
        """测试None值且非必需"""
        rule = StringValidationRule("test_rule", required=False)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        result = rule.validate(None, context)
        assert result is True
    
    def test_validate_none_required(self):
        """测试None值且必需"""
        rule = StringValidationRule("test_rule", required=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        result = rule.validate(None, context)
        assert result is False
    
    def test_validate_empty_string_required(self):
        """测试空字符串且必需"""
        rule = StringValidationRule("test_rule", required=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        result = rule.validate("", context)
        assert result is False
        
        result = rule.validate("   ", context)
        assert result is False
    
    def test_validate_min_length(self):
        """测试最小长度验证"""
        rule = StringValidationRule("test_rule", min_length=3)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate("ab", context) is False
        assert rule.validate("abc", context) is True
        assert rule.validate("abcd", context) is True
    
    def test_validate_max_length(self):
        """测试最大长度验证"""
        rule = StringValidationRule("test_rule", max_length=5)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate("abc", context) is True
        assert rule.validate("abcde", context) is True
        assert rule.validate("abcdef", context) is False
    
    def test_validate_pattern(self):
        """测试模式验证"""
        pattern = re.compile(r'^[a-zA-Z]+$')
        rule = StringValidationRule("test_rule", pattern=pattern)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate("abc", context) is True
        assert rule.validate("ABC", context) is True
        assert rule.validate("abc123", context) is False
        assert rule.validate("123", context) is False
    
    def test_validate_forbidden_chars(self):
        """测试禁止字符验证"""
        rule = StringValidationRule("test_rule", forbidden_chars="<>")
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate("abc", context) is True
        assert rule.validate("a<b", context) is False
        assert rule.validate("a>b", context) is False
    
    def test_get_error_message(self):
        """测试错误消息"""
        rule = StringValidationRule("test_rule", min_length=3, max_length=10, required=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert "不能为空" in rule.get_error_message(None, context)
        assert "不能少于3个字符" in rule.get_error_message("ab", context)
        assert "不能超过10个字符" in rule.get_error_message("a" * 11, context)


class TestNumericValidationRule:
    """测试数值验证规则"""
    
    def test_validate_integer(self):
        """测试整数验证"""
        rule = NumericValidationRule("test_rule", integer_only=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(123, context) is True
        assert rule.validate("123", context) is True
        assert rule.validate(123.5, context) is False
        assert rule.validate("123.5", context) is False
    
    def test_validate_range(self):
        """测试范围验证"""
        rule = NumericValidationRule("test_rule", min_value=0, max_value=100)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(-1, context) is False
        assert rule.validate(0, context) is True
        assert rule.validate(50, context) is True
        assert rule.validate(100, context) is True
        assert rule.validate(101, context) is False
    
    def test_validate_positive(self):
        """测试正数验证"""
        rule = NumericValidationRule("test_rule", positive_only=True, allow_zero=False)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(-1, context) is False
        assert rule.validate(0, context) is False
        assert rule.validate(1, context) is True
    
    def test_validate_positive_with_zero(self):
        """测试正数验证（允许零）"""
        rule = NumericValidationRule("test_rule", positive_only=True, allow_zero=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(-1, context) is False
        assert rule.validate(0, context) is True
        assert rule.validate(1, context) is True


class TestDateTimeValidationRule:
    """测试日期时间验证规则"""
    
    def test_validate_future_only(self):
        """测试仅未来时间验证"""
        rule = DateTimeValidationRule("test_rule", future_only=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        past_time = datetime.now() - timedelta(days=1)
        future_time = datetime.now() + timedelta(days=1)
        
        assert rule.validate(past_time, context) is False
        assert rule.validate(future_time, context) is True
    
    def test_validate_past_only(self):
        """测试仅过去时间验证"""
        rule = DateTimeValidationRule("test_rule", past_only=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        past_time = datetime.now() - timedelta(days=1)
        future_time = datetime.now() + timedelta(days=1)
        
        assert rule.validate(past_time, context) is True
        assert rule.validate(future_time, context) is False
    
    def test_validate_date_range(self):
        """测试日期范围验证"""
        min_date = datetime(2020, 1, 1)
        max_date = datetime(2025, 12, 31)
        rule = DateTimeValidationRule("test_rule", min_date=min_date, max_date=max_date)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(datetime(2019, 12, 31), context) is False
        assert rule.validate(datetime(2020, 1, 1), context) is True
        assert rule.validate(datetime(2022, 6, 15), context) is True
        assert rule.validate(datetime(2025, 12, 31), context) is True
        assert rule.validate(datetime(2026, 1, 1), context) is False


class TestListValidationRule:
    """测试列表验证规则"""
    
    def test_validate_length(self):
        """测试长度验证"""
        rule = ListValidationRule("test_rule", min_length=2, max_length=5)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate([1], context) is False
        assert rule.validate([1, 2], context) is True
        assert rule.validate([1, 2, 3, 4, 5], context) is True
        assert rule.validate([1, 2, 3, 4, 5, 6], context) is False
    
    def test_validate_unique_items(self):
        """测试唯一性验证"""
        rule = ListValidationRule("test_rule", unique_items=True)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate([1, 2, 3], context) is True
        assert rule.validate([1, 2, 2], context) is False
    
    def test_validate_with_item_validator(self):
        """测试项目验证器"""
        item_rule = StringValidationRule("item_rule", min_length=2)
        rule = ListValidationRule("test_rule", item_validator=item_rule)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(["ab", "cd"], context) is True
        assert rule.validate(["ab", "c"], context) is False


class TestEnumValidationRule:
    """测试枚举验证规则"""
    
    def test_validate_enum_instance(self):
        """测试枚举实例验证"""
        rule = EnumValidationRule("test_rule", TestEnum)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(TestEnum.VALUE1, context) is True
        assert rule.validate(TestEnum.VALUE2, context) is True
    
    def test_validate_enum_value(self):
        """测试枚举值验证"""
        rule = EnumValidationRule("test_rule", TestEnum)
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate("value1", context) is True
        assert rule.validate("value2", context) is True
        assert rule.validate("invalid_value", context) is False


class TestUUIDValidationRule:
    """测试UUID验证规则"""
    
    def test_validate_uuid_instance(self):
        """测试UUID实例验证"""
        rule = UUIDValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        test_uuid = uuid4()
        assert rule.validate(test_uuid, context) is True
    
    def test_validate_uuid_string(self):
        """测试UUID字符串验证"""
        rule = UUIDValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        valid_uuid_str = str(uuid4())
        assert rule.validate(valid_uuid_str, context) is True
        assert rule.validate("invalid-uuid", context) is False


class TestEmailValidationRule:
    """测试邮箱验证规则"""
    
    def test_validate_valid_emails(self):
        """测试有效邮箱"""
        rule = EmailValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            assert rule.validate(email, context) is True
    
    def test_validate_invalid_emails(self):
        """测试无效邮箱"""
        rule = EmailValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        invalid_emails = [
            "invalid-email",
            "@example.com",
            "user@",
            "user@.com"
        ]
        
        for email in invalid_emails:
            assert rule.validate(email, context) is False


class TestURLValidationRule:
    """测试URL验证规则"""
    
    def test_validate_valid_urls(self):
        """测试有效URL"""
        rule = URLValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        valid_urls = [
            "http://example.com",
            "https://www.example.com",
            "https://example.com/path?query=value"
        ]
        
        for url in valid_urls:
            assert rule.validate(url, context) is True
    
    def test_validate_invalid_urls(self):
        """测试无效URL"""
        rule = URLValidationRule()
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",  # 不在允许的协议中
            "http://",
            "https://"
        ]
        
        for url in invalid_urls:
            result = rule.validate(url, context)
            assert result is False, f"URL '{url}' should be invalid but validation returned {result}"


class TestCustomValidationRule:
    """测试自定义验证规则"""
    
    def test_validate_with_custom_function(self):
        """测试自定义函数验证"""
        def is_even(value, context):
            return isinstance(value, int) and value % 2 == 0
        
        rule = CustomValidationRule("even_number", is_even, "必须为偶数")
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.validate(2, context) is True
        assert rule.validate(4, context) is True
        assert rule.validate(3, context) is False
        assert rule.validate(5, context) is False
    
    def test_get_error_message(self):
        """测试错误消息"""
        def always_fail(value, context):
            return False
        
        rule = CustomValidationRule("test_rule", always_fail, "自定义错误消息")
        character = Character(full_name="测试")
        context = ValidationContext(character)
        
        assert rule.get_error_message("test", context) == "自定义错误消息"
