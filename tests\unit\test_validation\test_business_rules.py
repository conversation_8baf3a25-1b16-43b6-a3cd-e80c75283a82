"""业务规则验证器测试"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from src.core.validation.business_rules import (
    ProjectBusinessRules, CharacterBusinessRules,
    SceneBusinessRules, EventBusinessRules
)
from src.core.validation.validators import ValidationContext
from src.core.models.project import WritingProject, ProjectSettings, AIConfiguration
from src.core.models.character import Character, CharacterRelationship
from src.core.models.scene import Scene, SceneLocation
from src.core.models.event import Event, EventParticipation
from src.core.models.enums import (
    ProjectStatus, ImportanceLevel, RelationType,
    ParticipationRole, EventType
)


class TestProjectBusinessRules:
    """测试项目业务规则"""
    
    def test_validate_completed_project_with_100_percent(self):
        """测试已完成项目100%完成度"""
        project = WritingProject(
            name="测试项目",
            status=ProjectStatus.COMPLETED,
            completion_percentage=100.0
        )
        
        validator = ProjectBusinessRules()
        context = ValidationContext(project)
        
        result = validator.validate(project, context)
        assert result is True
        assert len(context.issues) == 0
    
    def test_validate_completed_project_with_low_percentage(self):
        """测试已完成项目低完成度"""
        project = WritingProject(
            name="测试项目",
            status=ProjectStatus.COMPLETED,
            completion_percentage=80.0
        )
        
        validator = ProjectBusinessRules()
        context = ValidationContext(project)
        
        result = validator.validate(project, context)
        assert len([issue for issue in context.issues if "完成度应该为100%" in issue.message]) > 0
    
    def test_validate_not_started_project_with_progress(self):
        """测试未开始项目有进度"""
        project = WritingProject(
            name="测试项目",
            status=ProjectStatus.DRAFT,
            completion_percentage=20.0
        )
        
        validator = ProjectBusinessRules()
        context = ValidationContext(project)
        
        result = validator.validate(project, context)
        assert len([issue for issue in context.issues if "完成度应该为0%" in issue.message]) > 0
    
    def test_validate_statistics_consistency(self):
        """测试统计数据一致性"""
        project = WritingProject(
            name="测试项目",
            word_count=10000,
            chapter_count=0  # 有字数但没有章节
        )
        
        validator = ProjectBusinessRules()
        context = ValidationContext(project)
        
        result = validator.validate(project, context)
        assert len([issue for issue in context.issues if "数据可能不一致" in issue.message]) > 0
    
    def test_validate_ai_configuration(self):
        """测试AI配置验证"""
        ai_config = AIConfiguration(
            default_provider='openai',
            openai_api_key=None,  # 选择了OpenAI但没有API密钥
            temperature=1.5  # 在有效范围内
        )
        
        project = WritingProject(
            name="测试项目",
            ai_config=ai_config
        )
        
        validator = ProjectBusinessRules()
        context = ValidationContext(project)
        
        result = validator.validate(project, context)
        assert len([issue for issue in context.issues if "未配置API密钥" in issue.message]) > 0
        assert len([issue for issue in context.issues if "温度参数应该在0-2之间" in issue.message]) > 0


class TestCharacterBusinessRules:
    """测试角色业务规则"""
    
    def test_validate_critical_character_completeness(self):
        """测试关键角色完整性"""
        character = Character(
            full_name="主角",
            importance=ImportanceLevel.CRITICAL,
            character_arc="",  # 缺少角色弧线
            motivation=""  # 缺少动机
        )
        
        validator = CharacterBusinessRules()
        context = ValidationContext(character)
        
        result = validator.validate(character, context)
        assert len([issue for issue in context.issues if "角色弧线" in issue.message]) > 0
        assert len([issue for issue in context.issues if "动机" in issue.message]) > 0
    
    def test_validate_relationship_consistency(self):
        """测试关系一致性"""
        relationship = CharacterRelationship(
            target_character=uuid4(),
            relationship_type=RelationType.ROMANTIC,
            intimacy_level=0.9,
            trust_level=0.3,  # 亲密度高但信任度低
            conflict_level=0.8  # 恋爱关系冲突度过高
        )
        
        character = Character(
            full_name="测试角色",
            character_relationships=[relationship]
        )
        
        validator = CharacterBusinessRules()
        context = ValidationContext(character)
        
        result = validator.validate(character, context)
        assert len([issue for issue in context.issues if "亲密度高但信任度低" in issue.message]) > 0
        assert len([issue for issue in context.issues if "恋爱关系冲突度过高" in issue.message]) > 0
    
    def test_validate_relationship_timeline(self):
        """测试关系时间线"""
        start_time = datetime.now()
        end_time = start_time - timedelta(days=30)  # 结束时间早于开始时间
        
        relationship = CharacterRelationship(
            target_character=uuid4(),
            relationship_type=RelationType.FRIEND,
            relationship_start=start_time,
            relationship_end=end_time
        )
        
        character = Character(
            full_name="测试角色",
            character_relationships=[relationship]
        )
        
        validator = CharacterBusinessRules()
        context = ValidationContext(character)
        
        result = validator.validate(character, context)
        assert result is False
        assert len([issue for issue in context.issues if "结束时间早于开始时间" in issue.message]) > 0


class TestSceneBusinessRules:
    """测试场景业务规则"""
    
    def test_validate_critical_scene_completeness(self):
        """测试关键场景完整性"""
        scene = Scene(
            location=SceneLocation(name="测试场景"),
            importance=ImportanceLevel.CRITICAL,
            detailed_description="",  # 缺少详细描述
        )
        
        validator = SceneBusinessRules()
        context = ValidationContext(scene)
        
        result = validator.validate(scene, context)
        assert len([issue for issue in context.issues if "详细描述" in issue.message]) > 0
    
    def test_validate_scene_usage_consistency(self):
        """测试场景使用一致性"""
        scene = Scene(
            location=SceneLocation(name="测试场景"),
            usage_count=3,
            chapters_used=[]  # 有使用次数但没有章节记录
        )
        
        validator = SceneBusinessRules()
        context = ValidationContext(scene)
        
        result = validator.validate(scene, context)
        assert len([issue for issue in context.issues if "使用次数但没有使用章节记录" in issue.message]) > 0
    
    def test_validate_scene_usage_count_error(self):
        """测试场景使用次数错误"""
        scene = Scene(
            location=SceneLocation(name="测试场景"),
            usage_count=2,
            chapters_used=[uuid4(), uuid4(), uuid4()]  # 章节数超过使用次数
        )
        
        validator = SceneBusinessRules()
        context = ValidationContext(scene)
        
        result = validator.validate(scene, context)
        assert result is False
        assert len([issue for issue in context.issues if "使用章节数不能超过使用次数" in issue.message]) > 0


class TestEventBusinessRules:
    """测试事件业务规则"""
    
    def test_validate_critical_event_completeness(self):
        """测试关键事件完整性"""
        event = Event(
            summary="关键事件",
            importance=ImportanceLevel.CRITICAL,
            outcome="",  # 缺少结果
            participants=[]  # 缺少参与者
        )
        
        validator = EventBusinessRules()
        context = ValidationContext(event)
        
        result = validator.validate(event, context)
        assert len([issue for issue in context.issues if "明确的结果" in issue.message]) > 0
        assert len([issue for issue in context.issues if "参与者" in issue.message]) > 0
    
    def test_validate_turning_point_event(self):
        """测试转折点事件"""
        event = Event(
            summary="转折点事件",
            is_turning_point=True,
            tension_level=0.3,  # 紧张度过低
            consequences=[]  # 缺少后果
        )
        
        validator = EventBusinessRules()
        context = ValidationContext(event)
        
        result = validator.validate(event, context)
        assert len([issue for issue in context.issues if "紧张度通常应该较高" in issue.message]) > 0
        assert len([issue for issue in context.issues if "明确的后果" in issue.message]) > 0
    
    def test_validate_event_participants(self):
        """测试事件参与者"""
        # 创建一个关键事件但没有主要参与者
        participant = EventParticipation(
            character_id=uuid4(),
            role=ParticipationRole.HELPER  # 只有帮助者参与
        )
        
        event = Event(
            summary="关键事件",
            importance=ImportanceLevel.CRITICAL,
            participants=[participant]
        )
        
        validator = EventBusinessRules()
        context = ValidationContext(event)
        
        result = validator.validate(event, context)
        assert len([issue for issue in context.issues if "主要参与者" in issue.message]) > 0
    
    def test_validate_event_impact_consistency(self):
        """测试事件影响一致性"""
        event = Event(
            summary="高情感影响事件",
            importance=ImportanceLevel.CRITICAL,
            tension_level=0.3  # 紧张度低但重要性高
        )
        
        # 设置高情感影响但低紧张度
        event.impact.emotional_impact = 0.9
        
        validator = EventBusinessRules()
        context = ValidationContext(event)
        
        result = validator.validate(event, context)
        assert len([issue for issue in context.issues if "情节影响通常应该较高" in issue.message]) > 0
        assert len([issue for issue in context.issues if "紧张度通常也应该较高" in issue.message]) > 0

    def test_validate_event_timeline_consistency(self):
        """测试事件时间线一致性"""
        validator = EventBusinessRules()

        # 创建有效的事件
        event = Event(
            name="测试事件",
            importance=ImportanceLevel.HIGH,
            timeline_position=1
        )
        context = ValidationContext(event)

        # 添加其他事件到上下文
        other_event = Event(
            name="其他事件",
            importance=ImportanceLevel.MEDIUM,
            timeline_position=2
        )
        context.add_entity(other_event)

        result = validator.validate(event, context)
        assert result is True

        # 测试时间线位置冲突
        conflicting_event = Event(
            name="冲突事件",
            importance=ImportanceLevel.LOW,
            timeline_position=1  # 与第一个事件相同的位置
        )
        context = ValidationContext(conflicting_event)
        context.add_entity(event)

        result = validator.validate(conflicting_event, context)
        assert result is False
        assert any("时间线位置冲突" in issue.message for issue in context.issues)

    def test_validate_event_participant_existence(self):
        """测试事件参与者存在性验证"""
        validator = EventBusinessRules()

        # 创建角色
        character = Character(name="测试角色", full_name="测试角色全名")

        # 创建事件，参与者引用存在的角色
        participation = EventParticipation(
            character_id=character.id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8
        )
        event = Event(
            name="测试事件",
            importance=ImportanceLevel.HIGH,
            timeline_position=1,
            participants=[participation]
        )

        context = ValidationContext(event)
        context.add_entity(character)

        result = validator.validate(event, context)
        assert result is True

        # 测试参与者引用不存在的角色
        invalid_participation = EventParticipation(
            character_id=uuid4(),  # 不存在的角色ID
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8
        )
        invalid_event = Event(
            name="无效事件",
            importance=ImportanceLevel.HIGH,
            timeline_position=2,
            participants=[invalid_participation]
        )

        context = ValidationContext(invalid_event)
        context.add_entity(character)

        result = validator.validate(invalid_event, context)
        assert result is False
        assert any("参与者角色不存在" in issue.message for issue in context.issues)

    def test_validate_event_impact_consistency(self):
        """测试事件影响一致性验证"""
        validator = EventBusinessRules()

        # 创建高重要性事件，但影响很低
        event = Event(
            name="高重要性低影响事件",
            importance=ImportanceLevel.HIGH,
            timeline_position=1
        )
        event.impact.plot_impact = 0.1  # 很低的情节影响
        event.impact.world_impact = 0.1  # 很低的世界影响

        context = ValidationContext(event)
        result = validator.validate(event, context)

        # 应该有警告，因为高重要性事件通常应该有高影响
        assert any("高重要性事件" in issue.message and "影响" in issue.message for issue in context.issues)

        # 创建低重要性事件，但影响很高
        event2 = Event(
            name="低重要性高影响事件",
            importance=ImportanceLevel.LOW,
            timeline_position=2
        )
        event2.impact.plot_impact = 0.9  # 很高的情节影响
        event2.impact.world_impact = 0.9  # 很高的世界影响

        context = ValidationContext(event2)
        result = validator.validate(event2, context)

        # 应该有警告，因为低重要性事件通常不应该有高影响
        assert any("低重要性事件" in issue.message and "影响" in issue.message for issue in context.issues)
