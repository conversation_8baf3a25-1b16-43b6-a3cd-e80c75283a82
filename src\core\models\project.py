"""项目模型

定义小说项目的数据模型，包括：
- WritingProject: 主项目模型
- ProjectSettings: 项目设置
- AIConfiguration: AI配置
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import Field, validator

from .base import BaseModel, ValidationResult, ModelValidator, VersionedModel
from .enums import ProjectType, ProjectStatus


class ProjectSettings(BaseModel):
    """项目设置"""
    
    # 写作设置
    auto_save_interval: int = Field(default=300, description="自动保存间隔（秒）")
    backup_enabled: bool = Field(default=True, description="是否启用备份")
    backup_interval: int = Field(default=3600, description="备份间隔（秒）")
    max_backup_count: int = Field(default=10, description="最大备份数量")
    
    # 界面设置
    theme: str = Field(default="light", description="主题")
    font_family: str = Field(default="Microsoft YaHei", description="字体")
    font_size: int = Field(default=12, description="字体大小")
    line_spacing: float = Field(default=1.5, description="行间距")
    
    # 导出设置
    default_export_format: str = Field(default="txt", description="默认导出格式")
    export_include_metadata: bool = Field(default=True, description="导出时包含元数据")
    
    # 统计设置
    word_count_target: int = Field(default=80000, description="目标字数")
    daily_word_target: int = Field(default=2000, description="每日目标字数")
    
    @validator('auto_save_interval')
    def validate_auto_save_interval(cls, v):
        if v < 60:
            raise ValueError("自动保存间隔不能少于60秒")
        return v
    
    @validator('font_size')
    def validate_font_size(cls, v):
        if v < 8 or v > 72:
            raise ValueError("字体大小必须在8-72之间")
        return v


class AIConfiguration(BaseModel):
    """AI配置"""
    
    # 默认AI服务
    default_provider: str = Field(default="openai", description="默认AI服务商")
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_model: str = Field(default="gpt-4o-mini", description="OpenAI模型")
    openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI API地址")
    
    # DeepSeek配置
    deepseek_api_key: Optional[str] = Field(default=None, description="DeepSeek API密钥")
    deepseek_model: str = Field(default="deepseek-chat", description="DeepSeek模型")
    deepseek_base_url: str = Field(default="https://api.deepseek.com/v1", description="DeepSeek API地址")
    
    # 智谱AI配置
    zhipu_api_key: Optional[str] = Field(default=None, description="智谱AI API密钥")
    zhipu_model: str = Field(default="glm-4", description="智谱AI模型")
    zhipu_base_url: str = Field(default="https://open.bigmodel.cn/api/paas/v4", description="智谱AI API地址")
    
    # Anthropic配置
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API密钥")
    anthropic_model: str = Field(default="claude-3-haiku-20240307", description="Anthropic模型")
    anthropic_base_url: str = Field(default="https://api.anthropic.com", description="Anthropic API地址")
    
    # 通用设置
    max_tokens: int = Field(default=2000, description="最大令牌数")
    temperature: float = Field(default=0.7, description="温度参数")
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if v < 0 or v > 2:
            raise ValueError("温度参数必须在0-2之间")
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        if v < 1 or v > 8000:
            raise ValueError("最大令牌数必须在1-8000之间")
        return v


class WritingProject(VersionedModel):
    """小说项目模型"""
    
    # 基础信息
    name: str = Field(..., description="项目名称")
    author: str = Field(default="", description="作者")
    project_type: ProjectType = Field(default=ProjectType.FANTASY, description="项目类型")
    description: str = Field(default="", description="项目描述")
    cover_image: Optional[str] = Field(default=None, description="封面图片路径")
    
    # 项目状态
    status: ProjectStatus = Field(default=ProjectStatus.DRAFT, description="项目状态")
    completion_percentage: float = Field(default=0.0, description="完成百分比")
    
    # 统计信息
    word_count: int = Field(default=0, description="总字数")
    chapter_count: int = Field(default=0, description="章节数")
    character_count: int = Field(default=0, description="角色数")
    scene_count: int = Field(default=0, description="场景数")
    event_count: int = Field(default=0, description="事件数")
    
    # 时间信息
    last_opened_at: Optional[datetime] = Field(default=None, description="最后打开时间")
    writing_time_minutes: int = Field(default=0, description="写作时间（分钟）")
    
    # 配置
    settings: ProjectSettings = Field(default_factory=ProjectSettings, description="项目设置")
    ai_config: AIConfiguration = Field(default_factory=AIConfiguration, description="AI配置")
    
    # 文件路径
    project_path: Optional[str] = Field(default=None, description="项目文件路径")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError("项目名称不能为空")
        
        if len(v) > 100:
            raise ValueError("项目名称不能超过100个字符")
        
        # 检查非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        if re.search(illegal_chars, v):
            raise ValueError("项目名称包含非法字符")
        
        return v.strip()
    
    @validator('completion_percentage')
    def validate_completion_percentage(cls, v):
        if v < 0 or v > 100:
            raise ValueError("完成百分比必须在0-100之间")
        return v
    
    @validator('word_count', 'chapter_count', 'character_count', 'scene_count', 'event_count')
    def validate_counts(cls, v):
        if v < 0:
            raise ValueError("计数不能为负数")
        return v
    
    def _validate_specific(self, result: ValidationResult):
        """项目特定验证"""
        # 验证项目名称
        if not self.name or not self.name.strip():
            result.add_error("项目名称不能为空")
        elif len(self.name) > 100:
            result.add_error("项目名称不能超过100个字符")
        
        # 验证作者名称
        if self.author and len(self.author) > 50:
            result.add_error("作者名称不能超过50个字符")
        
        # 验证描述
        if self.description and len(self.description) > 1000:
            result.add_error("项目描述不能超过1000个字符")
        
        # 验证完成百分比
        if self.completion_percentage < 0 or self.completion_percentage > 100:
            result.add_error("完成百分比必须在0-100之间")
        
        # 验证统计数据一致性
        if self.word_count < 0:
            result.add_error("字数不能为负数")
        
        if self.chapter_count < 0:
            result.add_error("章节数不能为负数")
    
    def update_statistics(self, word_count: int = None, chapter_count: int = None,
                         character_count: int = None, scene_count: int = None,
                         event_count: int = None):
        """更新统计信息"""
        if word_count is not None:
            self.word_count = word_count
        if chapter_count is not None:
            self.chapter_count = chapter_count
        if character_count is not None:
            self.character_count = character_count
        if scene_count is not None:
            self.scene_count = scene_count
        if event_count is not None:
            self.event_count = event_count
        
        self.update_timestamp()
    
    def calculate_completion_percentage(self) -> float:
        """计算完成百分比"""
        target_words = self.settings.word_count_target
        if target_words <= 0:
            return 0.0
        
        percentage = (self.word_count / target_words) * 100
        return min(percentage, 100.0)
    
    def update_completion_percentage(self):
        """更新完成百分比"""
        self.completion_percentage = self.calculate_completion_percentage()
        self.update_timestamp()
    
    def mark_opened(self):
        """标记项目已打开"""
        self.last_opened_at = datetime.now()
        self.update_timestamp()
    
    def add_writing_time(self, minutes: int):
        """添加写作时间"""
        if minutes > 0:
            self.writing_time_minutes += minutes
            self.update_timestamp()
    
    def get_daily_progress(self) -> Dict[str, Any]:
        """获取每日进度信息"""
        target = self.settings.daily_word_target
        # 这里需要从章节数据中计算今日写作字数
        # 暂时返回基础信息
        return {
            'target_words': target,
            'written_words': 0,  # 需要从实际数据计算
            'percentage': 0.0,
            'remaining_words': target
        }


class ProjectValidator(ModelValidator):
    """项目验证器"""
    
    def validate(self, model: WritingProject) -> ValidationResult:
        """验证项目"""
        result = ValidationResult()
        
        # 基础验证
        base_result = model.validate_model()
        result.merge(base_result)
        
        # 项目特定验证
        name_result = self.validate_required_string(model.name, "项目名称", 100)
        result.merge(name_result)
        
        author_result = self.validate_optional_string(model.author, "作者", 50)
        result.merge(author_result)
        
        desc_result = self.validate_optional_string(model.description, "项目描述", 1000)
        result.merge(desc_result)
        
        # 验证数值范围
        completion_result = self.validate_range(
            model.completion_percentage, "完成百分比", 0, 100
        )
        result.merge(completion_result)
        
        return result
