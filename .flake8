[flake8]
# 笔落App Flake8配置文件

# 最大行长度
max-line-length = 88

# 排除的文件和目录
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .tox,
    .pytest_cache,
    .mypy_cache,
    docs/_build,
    migrations

# 忽略的错误代码
ignore = 
    # E203: whitespace before ':'
    E203,
    # E501: line too long (handled by black)
    E501,
    # W503: line break before binary operator
    W503,
    # F401: imported but unused (handled by isort)
    F401

# 每个文件的最大复杂度
max-complexity = 10

# 导入顺序检查
import-order-style = google

# 文档字符串检查
docstring-convention = google

# 选择的错误和警告
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity
    B,  # flake8-bugbear
    I,  # isort

# 每行最大字符数（与black保持一致）
max-line-length = 88

# 缩进大小
indent-size = 4

# 统计信息
statistics = True
count = True

# 显示源代码
show-source = True

# 基准测试
benchmark = False
