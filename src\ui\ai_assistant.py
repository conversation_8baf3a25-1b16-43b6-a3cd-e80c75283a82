"""
AI助手界面模块

实现AI辅助创作功能的用户界面
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QPushButton, QComboBox, QLabel, QGroupBox,
    QFormLayout, QSpinBox, QSlider, QCheckBox,
    QTabWidget, QListWidget, QListWidgetItem,
    QMessageBox, QProgressBar, QFrame, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor

from src.core.models.project import WritingProject
from src.core.ai.manager import AIServiceManager, ContentGenerator
from src.core.ai.base import GenerationContext, GenerationOptions


class AIGenerationThread(QThread):
    """AI生成线程"""
    
    # 信号定义
    generation_finished = pyqtSignal(str)
    generation_error = pyqtSignal(str)
    generation_progress = pyqtSignal(str)
    
    def __init__(self, ai_manager: AIServiceManager, context: GenerationContext, options: GenerationOptions):
        super().__init__()
        self.ai_manager = ai_manager
        self.context = context
        self.options = options
        
    def run(self):
        """运行生成任务"""
        try:
            self.generation_progress.emit("正在生成内容...")
            
            # 调用AI服务生成内容
            result = self.ai_manager.generate_content(
                context=self.context,
                options=self.options
            )
            
            if result and result.content:
                self.generation_finished.emit(result.content)
            else:
                self.generation_error.emit("生成内容为空")
                
        except Exception as e:
            self.generation_error.emit(f"生成失败: {str(e)}")


class ChatWidget(QWidget):
    """聊天界面组件"""
    
    # 信号定义
    message_sent = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 聊天历史
        self.chat_history = QTextEdit()
        self.chat_history.setReadOnly(True)
        self.chat_history.setMinimumHeight(300)
        self.chat_history.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        layout.addWidget(self.chat_history)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        self.input_edit = QTextEdit()
        self.input_edit.setMaximumHeight(80)
        self.input_edit.setPlaceholderText("输入你的问题或指令...")
        self.input_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        self.send_btn = QPushButton("发送")
        self.send_btn.setMinimumSize(80, 40)
        self.send_btn.setStyleSheet("""
            QPushButton {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056b3;
            }
            QPushButton:pressed {
                background: #004085;
            }
        """)
        
        input_layout.addWidget(self.input_edit)
        input_layout.addWidget(self.send_btn)
        
        layout.addLayout(input_layout)
        
        # 连接信号
        self.send_btn.clicked.connect(self.send_message)
        
    def send_message(self):
        """发送消息"""
        message = self.input_edit.toPlainText().strip()
        if message:
            self.add_user_message(message)
            self.message_sent.emit(message)
            self.input_edit.clear()
            
    def add_user_message(self, message: str):
        """添加用户消息"""
        self.chat_history.append(f"<div style='color: #007bff; font-weight: bold;'>用户:</div>")
        self.chat_history.append(f"<div style='margin-bottom: 10px;'>{message}</div>")
        
    def add_ai_message(self, message: str):
        """添加AI消息"""
        self.chat_history.append(f"<div style='color: #28a745; font-weight: bold;'>AI助手:</div>")
        self.chat_history.append(f"<div style='margin-bottom: 10px;'>{message}</div>")
        
    def add_system_message(self, message: str):
        """添加系统消息"""
        self.chat_history.append(f"<div style='color: #6c757d; font-style: italic; margin-bottom: 10px;'>{message}</div>")


class ContentGeneratorWidget(QWidget):
    """内容生成器组件"""
    
    # 信号定义
    content_generated = pyqtSignal(str)
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.ai_manager = AIServiceManager(project.ai_config)
        self.content_generator = ContentGenerator(self.ai_manager)
        self.generation_thread: Optional[AIGenerationThread] = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 生成类型选择
        type_group = QGroupBox("生成类型")
        type_layout = QFormLayout(type_group)
        
        self.generation_type = QComboBox()
        self.generation_type.addItems([
            "角色描述",
            "场景描述", 
            "情节发展",
            "对话生成",
            "内容优化"
        ])
        type_layout.addRow("类型:", self.generation_type)
        
        layout.addWidget(type_group)
        
        # 生成参数
        params_group = QGroupBox("生成参数")
        params_layout = QFormLayout(params_group)
        
        self.length_slider = QSlider(Qt.Orientation.Horizontal)
        self.length_slider.setRange(50, 500)
        self.length_slider.setValue(200)
        self.length_label = QLabel("200字")
        
        length_layout = QHBoxLayout()
        length_layout.addWidget(self.length_slider)
        length_layout.addWidget(self.length_label)
        
        params_layout.addRow("长度:", length_layout)
        
        self.creativity_slider = QSlider(Qt.Orientation.Horizontal)
        self.creativity_slider.setRange(1, 10)
        self.creativity_slider.setValue(7)
        self.creativity_label = QLabel("7")
        
        creativity_layout = QHBoxLayout()
        creativity_layout.addWidget(self.creativity_slider)
        creativity_layout.addWidget(self.creativity_label)
        
        params_layout.addRow("创意度:", creativity_layout)
        
        layout.addWidget(params_group)
        
        # 提示词输入
        prompt_group = QGroupBox("提示词")
        prompt_layout = QVBoxLayout(prompt_group)
        
        self.prompt_edit = QTextEdit()
        self.prompt_edit.setMaximumHeight(100)
        self.prompt_edit.setPlaceholderText("输入自定义提示词（可选）...")
        prompt_layout.addWidget(self.prompt_edit)
        
        layout.addWidget(prompt_group)
        
        # 生成按钮和进度
        action_layout = QVBoxLayout()
        
        self.generate_btn = QPushButton("生成内容")
        self.generate_btn.setMinimumHeight(40)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #218838;
            }
            QPushButton:disabled {
                background: #6c757d;
            }
        """)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        
        action_layout.addWidget(self.generate_btn)
        action_layout.addWidget(self.progress_bar)
        action_layout.addWidget(self.status_label)
        
        layout.addLayout(action_layout)
        
        # 生成结果
        result_group = QGroupBox("生成结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_edit = QTextEdit()
        self.result_edit.setMinimumHeight(200)
        self.result_edit.setReadOnly(True)
        self.result_edit.setPlaceholderText("生成的内容将显示在这里...")
        
        result_button_layout = QHBoxLayout()
        
        self.copy_btn = QPushButton("复制")
        self.insert_btn = QPushButton("插入到编辑器")
        self.regenerate_btn = QPushButton("重新生成")
        
        result_button_layout.addWidget(self.copy_btn)
        result_button_layout.addWidget(self.insert_btn)
        result_button_layout.addWidget(self.regenerate_btn)
        result_button_layout.addStretch()
        
        result_layout.addWidget(self.result_edit)
        result_layout.addLayout(result_button_layout)
        
        layout.addWidget(result_group)
        
        # 连接信号
        self.setup_connections()
        
    def setup_connections(self):
        """设置信号连接"""
        self.length_slider.valueChanged.connect(
            lambda v: self.length_label.setText(f"{v}字")
        )
        self.creativity_slider.valueChanged.connect(
            lambda v: self.creativity_label.setText(str(v))
        )
        
        self.generate_btn.clicked.connect(self.generate_content)
        self.copy_btn.clicked.connect(self.copy_result)
        self.insert_btn.clicked.connect(self.insert_result)
        self.regenerate_btn.clicked.connect(self.generate_content)
        
    def generate_content(self):
        """生成内容"""
        if self.generation_thread and self.generation_thread.isRunning():
            return
            
        try:
            # 准备生成参数
            generation_type = self.generation_type.currentText()
            custom_prompt = self.prompt_edit.toPlainText().strip()
            
            context = GenerationContext(
                project_context=f"项目: {self.project.name}",
                current_content="",  # TODO: 获取当前编辑器内容
                generation_type=generation_type
            )
            
            options = GenerationOptions(
                max_tokens=self.length_slider.value() * 2,  # 估算token数
                temperature=self.creativity_slider.value() / 10.0,
                custom_prompt=custom_prompt if custom_prompt else None
            )
            
            # 启动生成线程
            self.generation_thread = AIGenerationThread(
                self.ai_manager, context, options
            )
            
            self.generation_thread.generation_finished.connect(self.on_generation_finished)
            self.generation_thread.generation_error.connect(self.on_generation_error)
            self.generation_thread.generation_progress.connect(self.on_generation_progress)
            
            self.generation_thread.start()
            
            # 更新UI状态
            self.generate_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.status_label.setText("正在生成内容...")
            
        except Exception as e:
            QMessageBox.critical(self, "生成错误", f"启动内容生成失败: {e}")
            
    def on_generation_finished(self, content: str):
        """生成完成"""
        self.result_edit.setPlainText(content)
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("生成完成")
        
        # 3秒后清除状态
        QTimer.singleShot(3000, lambda: self.status_label.setText(""))
        
    def on_generation_error(self, error: str):
        """生成错误"""
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"生成失败: {error}")
        
        QMessageBox.warning(self, "生成失败", error)
        
    def on_generation_progress(self, message: str):
        """生成进度"""
        self.status_label.setText(message)
        
    def copy_result(self):
        """复制结果"""
        content = self.result_edit.toPlainText()
        if content:
            clipboard = self.parent().clipboard()
            clipboard.setText(content)
            self.status_label.setText("已复制到剪贴板")
            QTimer.singleShot(2000, lambda: self.status_label.setText(""))
            
    def insert_result(self):
        """插入结果到编辑器"""
        content = self.result_edit.toPlainText()
        if content:
            self.content_generated.emit(content)
            self.status_label.setText("已插入到编辑器")
            QTimer.singleShot(2000, lambda: self.status_label.setText(""))


class AIAssistantWidget(QWidget):
    """AI助手主组件"""
    
    # 信号定义
    text_generated = pyqtSignal(str)
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.ai_manager = AIServiceManager(project.ai_config)
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 聊天助手
        self.chat_widget = ChatWidget()
        self.tab_widget.addTab(self.chat_widget, "聊天助手")
        
        # 内容生成器
        self.content_generator = ContentGeneratorWidget(self.project)
        self.tab_widget.addTab(self.content_generator, "内容生成")
        
        layout.addWidget(self.tab_widget)
        
    def setup_connections(self):
        """设置信号连接"""
        self.chat_widget.message_sent.connect(self.handle_chat_message)
        self.content_generator.content_generated.connect(self.text_generated.emit)
        
    def handle_chat_message(self, message: str):
        """处理聊天消息"""
        try:
            # 这里应该调用AI服务处理消息
            # 暂时返回一个简单的回复
            self.chat_widget.add_system_message("正在处理您的请求...")
            
            # TODO: 实现真正的AI对话
            response = f"收到您的消息: {message}\n\n这是一个示例回复。"
            
            QTimer.singleShot(1000, lambda: self.chat_widget.add_ai_message(response))
            
        except Exception as e:
            self.chat_widget.add_system_message(f"处理消息时发生错误: {e}")
            
    def get_current_context(self) -> str:
        """获取当前上下文"""
        # TODO: 从编辑器获取当前内容
        return ""
