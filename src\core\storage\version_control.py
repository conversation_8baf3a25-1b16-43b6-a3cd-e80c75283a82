"""版本控制管理器

提供数据变更历史追踪功能，包括：
- 变更记录和版本管理
- 差异比较
- 版本回滚
- 变更历史查询
"""

import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4
from enum import Enum

from .serializer import JSONSerializer


class VersionControlError(Exception):
    """版本控制错误"""
    pass


class ChangeType(Enum):
    """变更类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    RESTORE = "restore"


class VersionInfo:
    """版本信息"""
    
    def __init__(self, version_id: str, object_id: UUID, object_type: str,
                 change_type: ChangeType, created_at: datetime,
                 created_by: str, description: str = "",
                 data_hash: str = "", metadata: Optional[Dict[str, Any]] = None):
        self.version_id = version_id
        self.object_id = object_id
        self.object_type = object_type
        self.change_type = change_type
        self.created_at = created_at
        self.created_by = created_by
        self.description = description
        self.data_hash = data_hash
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'version_id': self.version_id,
            'object_id': str(self.object_id),
            'object_type': self.object_type,
            'change_type': self.change_type.value,
            'created_at': self.created_at.isoformat(),
            'created_by': self.created_by,
            'description': self.description,
            'data_hash': self.data_hash,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VersionInfo':
        """从字典创建"""
        return cls(
            version_id=data['version_id'],
            object_id=UUID(data['object_id']),
            object_type=data['object_type'],
            change_type=ChangeType(data['change_type']),
            created_at=datetime.fromisoformat(data['created_at']),
            created_by=data['created_by'],
            description=data.get('description', ''),
            data_hash=data.get('data_hash', ''),
            metadata=data.get('metadata', {})
        )


class VersionControlManager:
    """版本控制管理器
    
    提供数据对象的版本控制功能，包括：
    - 版本创建和管理
    - 变更历史追踪
    - 版本比较和回滚
    - 版本清理和优化
    """
    
    def __init__(self, versions_dir: Union[str, Path]):
        """初始化版本控制管理器
        
        Args:
            versions_dir: 版本存储目录
        """
        self.versions_dir = Path(versions_dir)
        self.serializer = JSONSerializer()
        
        # 确保版本目录存在
        self.versions_dir.mkdir(parents=True, exist_ok=True)
        
        # 版本索引文件
        self.index_file = self.versions_dir / "version_index.json"
        self._load_index()
    
    def _load_index(self):
        """加载版本索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    self.version_index = {
                        version_id: VersionInfo.from_dict(data)
                        for version_id, data in index_data.items()
                    }
            except Exception:
                self.version_index = {}
        else:
            self.version_index = {}
    
    def _save_index(self):
        """保存版本索引"""
        try:
            index_data = {
                version_id: info.to_dict()
                for version_id, info in self.version_index.items()
            }
            
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2, ensure_ascii=False)
        except Exception:
            pass
    
    def create_version(self, obj: Any, change_type: ChangeType,
                      created_by: str = "user", description: str = "") -> VersionInfo:
        """创建新版本
        
        Args:
            obj: 数据对象
            change_type: 变更类型
            created_by: 创建者
            description: 变更描述
            
        Returns:
            版本信息
        """
        try:
            # 生成版本ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            version_id = f"{obj.id}_{timestamp}"
            
            # 序列化对象数据
            data_json = self.serializer.serialize(obj)
            data_hash = hashlib.md5(data_json.encode()).hexdigest()
            
            # 保存版本数据
            version_file = self.versions_dir / f"{version_id}.json"
            version_file.write_text(data_json, encoding='utf-8')
            
            # 创建版本信息
            version_info = VersionInfo(
                version_id=version_id,
                object_id=obj.id,
                object_type=obj.__class__.__name__,
                change_type=change_type,
                created_at=datetime.now(),
                created_by=created_by,
                description=description,
                data_hash=data_hash,
                metadata={
                    'object_version': getattr(obj, 'version', 1),
                    'file_size': version_file.stat().st_size
                }
            )
            
            # 添加到索引
            self.version_index[version_id] = version_info
            self._save_index()
            
            return version_info
            
        except Exception as e:
            raise Exception(f"创建版本失败: {e}") from e
    
    def get_version(self, version_id: str, target_class: type) -> Any:
        """获取指定版本的对象
        
        Args:
            version_id: 版本ID
            target_class: 目标类型
            
        Returns:
            版本对象
        """
        if version_id not in self.version_index:
            raise ValueError(f"版本不存在: {version_id}")
        
        version_file = self.versions_dir / f"{version_id}.json"
        if not version_file.exists():
            raise ValueError(f"版本文件不存在: {version_file}")
        
        return self.serializer.deserialize(file_path=version_file, target_class=target_class)
    
    def get_object_versions(self, object_id: UUID) -> List[VersionInfo]:
        """获取对象的所有版本
        
        Args:
            object_id: 对象ID
            
        Returns:
            版本信息列表
        """
        versions = [
            version for version in self.version_index.values()
            if version.object_id == object_id
        ]
        
        # 按创建时间倒序排列
        versions.sort(key=lambda x: x.created_at, reverse=True)
        
        return versions
    
    def get_latest_version(self, object_id: UUID) -> Optional[VersionInfo]:
        """获取对象的最新版本
        
        Args:
            object_id: 对象ID
            
        Returns:
            最新版本信息
        """
        versions = self.get_object_versions(object_id)
        return versions[0] if versions else None
    
    def compare_versions(self, version_id1: str, version_id2: str) -> Dict[str, Any]:
        """比较两个版本
        
        Args:
            version_id1: 版本1 ID
            version_id2: 版本2 ID
            
        Returns:
            比较结果
        """
        if version_id1 not in self.version_index:
            raise ValueError(f"版本不存在: {version_id1}")
        
        if version_id2 not in self.version_index:
            raise ValueError(f"版本不存在: {version_id2}")
        
        version1 = self.version_index[version_id1]
        version2 = self.version_index[version_id2]
        
        # 基本比较信息
        comparison = {
            'version1': version1.to_dict(),
            'version2': version2.to_dict(),
            'same_object': version1.object_id == version2.object_id,
            'same_hash': version1.data_hash == version2.data_hash,
            'time_diff_seconds': (version2.created_at - version1.created_at).total_seconds()
        }
        
        # 如果是同一对象的不同版本，进行详细比较
        if version1.object_id == version2.object_id and version1.data_hash != version2.data_hash:
            try:
                # 加载两个版本的数据
                file1 = self.versions_dir / f"{version_id1}.json"
                file2 = self.versions_dir / f"{version_id2}.json"
                
                if file1.exists() and file2.exists():
                    data1 = json.loads(file1.read_text(encoding='utf-8'))
                    data2 = json.loads(file2.read_text(encoding='utf-8'))
                    
                    # 简单的字段级比较
                    changed_fields = []
                    for key in set(data1.keys()) | set(data2.keys()):
                        if data1.get(key) != data2.get(key):
                            changed_fields.append({
                                'field': key,
                                'old_value': data1.get(key),
                                'new_value': data2.get(key)
                            })
                    
                    comparison['changed_fields'] = changed_fields
                    comparison['field_count'] = len(changed_fields)
                    
            except Exception:
                comparison['detailed_comparison_failed'] = True
        
        return comparison
    
    def rollback_to_version(self, version_id: str, target_class: type) -> Any:
        """回滚到指定版本
        
        Args:
            version_id: 目标版本ID
            target_class: 目标类型
            
        Returns:
            回滚后的对象
        """
        # 获取指定版本的对象
        obj = self.get_version(version_id, target_class)
        
        # 创建回滚版本记录
        self.create_version(
            obj,
            ChangeType.RESTORE,
            created_by="system",
            description=f"回滚到版本 {version_id}"
        )
        
        return obj
    
    def delete_version(self, version_id: str) -> bool:
        """删除指定版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            是否删除成功
        """
        try:
            if version_id not in self.version_index:
                return False
            
            # 删除版本文件
            version_file = self.versions_dir / f"{version_id}.json"
            if version_file.exists():
                version_file.unlink()
            
            # 从索引中移除
            del self.version_index[version_id]
            self._save_index()
            
            return True
            
        except Exception:
            return False
    
    def cleanup_old_versions(self, object_id: UUID, keep_count: int = 10) -> int:
        """清理对象的旧版本
        
        Args:
            object_id: 对象ID
            keep_count: 保留的版本数量
            
        Returns:
            删除的版本数量
        """
        versions = self.get_object_versions(object_id)
        
        if len(versions) <= keep_count:
            return 0
        
        # 删除多余的旧版本
        versions_to_delete = versions[keep_count:]
        deleted_count = 0
        
        for version in versions_to_delete:
            if self.delete_version(version.version_id):
                deleted_count += 1
        
        return deleted_count
    
    def get_version_stats(self) -> Dict[str, Any]:
        """获取版本统计信息"""
        total_versions = len(self.version_index)
        
        # 按对象类型分组统计
        type_stats = {}
        object_stats = {}
        
        for version in self.version_index.values():
            # 按类型统计
            obj_type = version.object_type
            if obj_type not in type_stats:
                type_stats[obj_type] = 0
            type_stats[obj_type] += 1
            
            # 按对象统计
            obj_id = str(version.object_id)
            if obj_id not in object_stats:
                object_stats[obj_id] = 0
            object_stats[obj_id] += 1
        
        # 计算总存储大小
        total_size = 0
        for version_id in self.version_index:
            version_file = self.versions_dir / f"{version_id}.json"
            if version_file.exists():
                total_size += version_file.stat().st_size
        
        return {
            'total_versions': total_versions,
            'total_size_bytes': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'type_stats': type_stats,
            'objects_with_versions': len(object_stats),
            'avg_versions_per_object': total_versions / len(object_stats) if object_stats else 0
        }
