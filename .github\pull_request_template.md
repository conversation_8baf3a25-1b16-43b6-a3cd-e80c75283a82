# Pull Request

## 描述
请简要描述此PR的更改内容。

## 更改类型
请删除不适用的选项：

- [ ] Bug修复（不破坏现有功能的更改）
- [ ] 新功能（添加功能的不破坏性更改）
- [ ] 破坏性更改（会导致现有功能无法正常工作的修复或功能）
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能改进
- [ ] 测试改进

## 相关Issue
修复 #(issue编号)

## 更改详情
请详细描述您所做的更改：

### 添加的功能
- 

### 修复的Bug
- 

### 重构的代码
- 

## 测试
请描述您为验证更改而运行的测试：

- [ ] 单元测试
- [ ] 集成测试
- [ ] 手动测试
- [ ] 性能测试

### 测试配置
- Python版本：
- 操作系统：
- PyQt6版本：

## 检查清单
请确认您已完成以下检查：

- [ ] 我的代码遵循项目的代码规范
- [ ] 我已进行自我代码审查
- [ ] 我已为代码添加了注释，特别是在难以理解的区域
- [ ] 我已对相应的文档进行了更改
- [ ] 我的更改不会产生新的警告
- [ ] 我已添加了证明我的修复有效或我的功能正常工作的测试
- [ ] 新的和现有的单元测试在我的更改下都通过了
- [ ] 任何依赖的更改都已合并并发布

## 截图（如果适用）
如果您的更改包括UI更改，请添加截图：

## 附加说明
添加任何其他信息或对审查者的说明：

## 审查者注意事项
请特别关注以下方面：
- [ ] 代码质量
- [ ] 性能影响
- [ ] 安全考虑
- [ ] 向后兼容性
- [ ] 文档完整性
