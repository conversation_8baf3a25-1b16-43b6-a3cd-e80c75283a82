"""用户界面模块

提供应用程序的图形用户界面，包括：
- 主窗口
- 创作界面
- 设置界面
- 对话框组件
- 自定义控件
"""

from .main_window import MainWindow
from .writing_interface import WritingInterface
from .settings_dialog import SettingsDialog
from .project_dialog import ProjectDialog
from .ai_assistant import AIAssistantWidget
from .character_manager import CharacterManagerWidget
from .scene_manager import SceneManagerWidget
from .event_manager import EventManagerWidget
# from .components import *
# from .widgets import *

__all__ = [
    "MainWindow",
    "WritingInterface",
    "SettingsDialog",
    "ProjectDialog",
    "AIAssistantWidget",
    "CharacterManagerWidget",
    "SceneManagerWidget",
    "EventManagerWidget"
]
