"""用户界面模块

提供应用程序的图形用户界面，包括：
- 主窗口
- 创作界面
- 设置界面
- 对话框组件
- 自定义控件
"""

from src.ui.main_window import MainWindow
from src.ui.writing_interface import WritingInterface
from src.ui.settings_dialog import SettingsDialog
from src.ui.project_dialog import ProjectDialog
from src.ui.ai_assistant import AIAssistantWidget
from src.ui.character_manager import CharacterManagerWidget
from src.ui.scene_manager import SceneManagerWidget
from src.ui.event_manager import EventManagerWidget
# from .components import *
# from .widgets import *

__all__ = [
    "MainWindow",
    "WritingInterface",
    "SettingsDialog",
    "ProjectDialog",
    "AIAssistantWidget",
    "CharacterManagerWidget",
    "SceneManagerWidget",
    "EventManagerWidget"
]
