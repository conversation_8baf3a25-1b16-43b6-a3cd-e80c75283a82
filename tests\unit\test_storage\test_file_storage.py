"""文件存储测试"""

import tempfile
from pathlib import Path
from uuid import uuid4
import pytest

from src.core.storage.file_storage import FileStorage, FileStorageError
from src.core.models.project import WritingProject, ProjectType
from src.core.models.character import Character, Gender
from src.core.models.scene import Scene
from src.core.models.event import Event
from src.core.models.enums import ElementType


class TestFileStorage:
    """文件存储测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_storage = FileStorage(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        assert self.file_storage.base_path == self.temp_dir
        assert self.temp_dir.exists()
        assert self.file_storage.serializer is not None
    
    def test_create_project_structure(self):
        """测试创建项目目录结构"""
        project_id = uuid4()
        
        project_path = self.file_storage.create_project_structure(project_id)
        
        # 验证项目根目录
        assert project_path.exists()
        assert project_path == self.temp_dir / str(project_id)
        
        # 验证子目录
        expected_dirs = [
            "characters", "scenes", "events", "chapters", 
            "chapters/content", "arcs", "backups", "cache",
            "exports", "resources", "resources/images", "resources/documents"
        ]
        
        for dir_name in expected_dirs:
            dir_path = project_path / dir_name
            assert dir_path.exists(), f"目录不存在: {dir_name}"
            assert dir_path.is_dir(), f"不是目录: {dir_name}"
        
        # 验证配置文件
        config_file = project_path / "project.json"
        assert config_file.exists()
        assert config_file.is_file()
    
    def test_create_project_structure_duplicate(self):
        """测试创建重复项目结构"""
        project_id = uuid4()
        
        # 第一次创建
        self.file_storage.create_project_structure(project_id)
        
        # 第二次创建应该失败
        with pytest.raises(FileStorageError):
            self.file_storage.create_project_structure(project_id)
    
    def test_save_and_load_project(self):
        """测试保存和加载项目"""
        project = WritingProject(
            name="测试项目",
            author="测试作者",
            project_type=ProjectType.FANTASY,
            description="测试描述"
        )
        
        # 创建项目结构
        project_path = self.file_storage.create_project_structure(project.id)
        
        # 保存项目
        saved_path = self.file_storage.save_project(project, project_path)
        assert saved_path.exists()
        assert saved_path == project_path / "project.json"
        
        # 加载项目
        loaded_project = self.file_storage.load_project(project.id, WritingProject)
        
        assert isinstance(loaded_project, WritingProject)
        assert loaded_project.id == project.id
        assert loaded_project.name == project.name
        assert loaded_project.author == project.author
        assert loaded_project.project_type == project.project_type
        assert loaded_project.description == project.description
    
    def test_save_and_load_character(self):
        """测试保存和加载角色"""
        project_id = uuid4()
        character = Character(
            full_name="测试角色",
            gender=Gender.MALE,
            element_type=ElementType.CHARACTER
        )
        
        # 创建项目结构
        self.file_storage.create_project_structure(project_id)
        
        # 保存角色
        saved_path = self.file_storage.save_character(character, project_id)
        expected_path = self.temp_dir / str(project_id) / "characters" / f"{character.id}.json"
        assert saved_path == expected_path
        assert saved_path.exists()
        
        # 加载角色
        loaded_character = self.file_storage.load_character(character.id, project_id, Character)
        
        assert isinstance(loaded_character, Character)
        assert loaded_character.id == character.id
        assert loaded_character.full_name == character.full_name
        assert loaded_character.gender == character.gender
    
    def test_save_and_load_scene(self):
        """测试保存和加载场景"""
        project_id = uuid4()
        scene = Scene(
            name="测试场景",
            element_type=ElementType.SCENE,
            description="测试场景描述"
        )
        
        # 创建项目结构
        self.file_storage.create_project_structure(project_id)
        
        # 保存场景
        saved_path = self.file_storage.save_scene(scene, project_id)
        expected_path = self.temp_dir / str(project_id) / "scenes" / f"{scene.id}.json"
        assert saved_path == expected_path
        assert saved_path.exists()
        
        # 加载场景
        loaded_scene = self.file_storage.load_scene(scene.id, project_id, Scene)
        
        assert isinstance(loaded_scene, Scene)
        assert loaded_scene.id == scene.id
        assert loaded_scene.name == scene.name
        assert loaded_scene.description == scene.description
    
    def test_save_and_load_event(self):
        """测试保存和加载事件"""
        project_id = uuid4()
        event = Event(
            name="测试事件",
            element_type=ElementType.EVENT,
            description="测试事件描述"
        )
        
        # 创建项目结构
        self.file_storage.create_project_structure(project_id)
        
        # 保存事件
        saved_path = self.file_storage.save_event(event, project_id)
        expected_path = self.temp_dir / str(project_id) / "events" / f"{event.id}.json"
        assert saved_path == expected_path
        assert saved_path.exists()
        
        # 加载事件
        loaded_event = self.file_storage.load_event(event.id, project_id, Event)
        
        assert isinstance(loaded_event, Event)
        assert loaded_event.id == event.id
        assert loaded_event.name == event.name
        assert loaded_event.description == event.description
    
    def test_list_projects(self):
        """测试列出项目"""
        # 创建多个项目
        project_ids = [uuid4() for _ in range(3)]
        
        for project_id in project_ids:
            self.file_storage.create_project_structure(project_id)
        
        # 列出项目
        projects = self.file_storage.list_projects()
        
        assert len(projects) == 3
        
        for project in projects:
            assert 'id' in project
            assert 'path' in project
            assert 'config' in project
            assert project['id'] in [str(pid) for pid in project_ids]
    
    def test_list_characters(self):
        """测试列出角色"""
        project_id = uuid4()
        self.file_storage.create_project_structure(project_id)
        
        # 创建多个角色
        characters = []
        for i in range(3):
            character = Character(
                full_name=f"角色{i}",
                element_type=ElementType.CHARACTER
            )
            characters.append(character)
            self.file_storage.save_character(character, project_id)
        
        # 列出角色
        character_ids = self.file_storage.list_characters(project_id)
        
        assert len(character_ids) == 3
        for character in characters:
            assert character.id in character_ids
    
    def test_list_scenes(self):
        """测试列出场景"""
        project_id = uuid4()
        self.file_storage.create_project_structure(project_id)
        
        # 创建多个场景
        scenes = []
        for i in range(3):
            scene = Scene(
                name=f"场景{i}",
                element_type=ElementType.SCENE
            )
            scenes.append(scene)
            self.file_storage.save_scene(scene, project_id)
        
        # 列出场景
        scene_ids = self.file_storage.list_scenes(project_id)
        
        assert len(scene_ids) == 3
        for scene in scenes:
            assert scene.id in scene_ids
    
    def test_list_events(self):
        """测试列出事件"""
        project_id = uuid4()
        self.file_storage.create_project_structure(project_id)
        
        # 创建多个事件
        events = []
        for i in range(3):
            event = Event(
                name=f"事件{i}",
                element_type=ElementType.EVENT
            )
            events.append(event)
            self.file_storage.save_event(event, project_id)
        
        # 列出事件
        event_ids = self.file_storage.list_events(project_id)
        
        assert len(event_ids) == 3
        for event in events:
            assert event.id in event_ids
    
    def test_delete_project(self):
        """测试删除项目"""
        project_id = uuid4()
        project_path = self.file_storage.create_project_structure(project_id)
        
        # 确认项目存在
        assert project_path.exists()
        
        # 删除项目
        success = self.file_storage.delete_project(project_id)
        assert success is True
        assert not project_path.exists()
        
        # 删除不存在的项目
        success = self.file_storage.delete_project(uuid4())
        assert success is False
    
    def test_delete_character(self):
        """测试删除角色"""
        project_id = uuid4()
        character = Character(
            full_name="测试角色",
            element_type=ElementType.CHARACTER
        )
        
        self.file_storage.create_project_structure(project_id)
        self.file_storage.save_character(character, project_id)
        
        # 确认角色文件存在
        character_file = self.temp_dir / str(project_id) / "characters" / f"{character.id}.json"
        assert character_file.exists()
        
        # 删除角色
        success = self.file_storage.delete_character(character.id, project_id)
        assert success is True
        assert not character_file.exists()
        
        # 删除不存在的角色
        success = self.file_storage.delete_character(uuid4(), project_id)
        assert success is False
    
    def test_load_nonexistent_project(self):
        """测试加载不存在的项目"""
        with pytest.raises(FileStorageError):
            self.file_storage.load_project(uuid4(), WritingProject)
    
    def test_load_nonexistent_character(self):
        """测试加载不存在的角色"""
        project_id = uuid4()
        self.file_storage.create_project_structure(project_id)
        
        with pytest.raises(FileStorageError):
            self.file_storage.load_character(uuid4(), project_id, Character)
    
    def test_atomic_write(self):
        """测试原子写入"""
        test_file = self.temp_dir / "test_atomic.txt"
        
        # 正常写入
        with self.file_storage._atomic_write(test_file) as temp_path:
            temp_path.write_text("test content")
        
        assert test_file.exists()
        assert test_file.read_text() == "test content"
        
        # 异常情况下的写入
        try:
            with self.file_storage._atomic_write(test_file) as temp_path:
                temp_path.write_text("new content")
                raise Exception("模拟异常")
        except Exception:
            pass
        
        # 原文件应该保持不变
        assert test_file.read_text() == "test content"
