@echo off
REM 笔落App Windows开发环境设置脚本
REM 此脚本用于在Windows系统上自动化设置开发环境

setlocal enabledelayedexpansion

echo ============================================================
echo 笔落App开发环境设置 (Windows)
echo ============================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.11+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查Python版本...
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python版本: %PYTHON_VERSION%

REM 创建虚拟环境
echo.
echo 创建Python虚拟环境...
if exist .venv (
    echo 虚拟环境已存在，跳过创建
) else (
    python -m venv .venv
    if errorlevel 1 (
        echo 错误: 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo 虚拟环境创建成功
)

REM 激活虚拟环境
echo.
echo 激活虚拟环境...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo 错误: 虚拟环境激活失败
    pause
    exit /b 1
)

REM 升级pip
echo.
echo 升级pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo 警告: pip升级失败，继续安装依赖
)

REM 安装依赖
echo.
echo 安装项目依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

REM 安装开发依赖
echo.
echo 安装开发依赖...
pip install -e .[dev]
if errorlevel 1 (
    echo 警告: 开发依赖安装失败，继续设置
)

REM 安装pre-commit
echo.
echo 设置pre-commit钩子...
pip install pre-commit
if errorlevel 1 (
    echo 警告: pre-commit安装失败
) else (
    pre-commit install
    if errorlevel 1 (
        echo 警告: pre-commit钩子安装失败
    ) else (
        echo pre-commit钩子设置完成
    )
)

REM 创建必要目录
echo.
echo 创建必要目录...
if not exist data mkdir data
if not exist cache mkdir cache
if not exist backups mkdir backups
if not exist logs mkdir logs
if not exist temp mkdir temp
if not exist exports mkdir exports
if not exist reports mkdir reports
echo 目录创建完成

REM 复制环境配置文件
echo.
echo 设置环境配置文件...
if exist .env.example (
    if not exist .env (
        copy .env.example .env >nul
        echo 已创建 .env 文件，请根据需要修改配置
    ) else (
        echo .env 文件已存在
    )
) else (
    echo 警告: .env.example 文件不存在
)

REM 验证安装
echo.
echo 验证环境配置...
python -c "import PyQt6; print('PyQt6: OK')" 2>nul || echo 警告: PyQt6 导入失败
python -c "import openai; print('openai: OK')" 2>nul || echo 警告: openai 导入失败
python -c "import pydantic; print('pydantic: OK')" 2>nul || echo 警告: pydantic 导入失败
python -c "import pytest; print('pytest: OK')" 2>nul || echo 警告: pytest 导入失败

echo.
echo ============================================================
echo 开发环境设置完成！
echo ============================================================
echo.
echo 下一步操作:
echo 1. 激活虚拟环境: .venv\Scripts\activate.bat
echo 2. 编辑 .env 文件配置API密钥
echo 3. 运行测试: pytest tests/
echo 4. 启动应用: python src/main.py
echo.
echo 常用命令:
echo   python scripts/setup_dev.py  - 重新运行设置脚本
echo   pytest tests/                - 运行测试
echo   black src/ tests/            - 格式化代码
echo   flake8 src/ tests/           - 代码质量检查
echo   mypy src/                    - 类型检查
echo.

pause
