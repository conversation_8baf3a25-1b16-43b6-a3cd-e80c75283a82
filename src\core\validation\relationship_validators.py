"""关系验证器

实现跨模型关联验证功能
"""

from typing import List, Dict, Set, Any
from uuid import UUID
from datetime import datetime

from ..models.base import BaseModel
from ..models.character import Character
from ..models.scene import Scene
from ..models.event import Event
from ..models.project import WritingProject
from ..models.story_element import StoryElement
from .validators import RelationshipValidator, ValidationContext
from .exceptions import ValidationError


class RelationshipValidator:
    """关系验证器基类"""

    def validate_entity_existence(self, entity_id: UUID, entities: List[Any]) -> bool:
        """验证实体存在性"""
        return any(getattr(entity, 'id', None) == entity_id for entity in entities)

    def has_circular_reference(self, relationships: List[tuple]) -> bool:
        """检测循环引用"""
        # 简单的循环检测实现
        graph = {}
        for source, target in relationships:
            if source not in graph:
                graph[source] = []
            graph[source].append(target)

        def has_cycle(node, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)

            for neighbor in graph.get(node, []):
                if neighbor not in visited:
                    if has_cycle(neighbor, visited, rec_stack):
                        return True
                elif neighbor in rec_stack:
                    return True

            rec_stack.remove(node)
            return False

        visited = set()
        for node in graph:
            if node not in visited:
                if has_cycle(node, visited, set()):
                    return True
        return False


class CharacterRelationshipValidator(RelationshipValidator):
    """角色关系验证器"""

    def validate_relationship(self, character: Character, relationship: Any, characters: List[Character]) -> bool:
        """验证单个关系"""
        # 检查关系中引用的角色是否存在
        if not self.validate_entity_existence(relationship.character_id, characters):
            raise ValidationError("关系中引用的角色不存在")

        # 检查是否是自我关系
        if relationship.character_id == character.id:
            raise ValidationError("角色不能与自己建立关系")

        return True

    def validate_all_relationships(self, character: Character, characters: List[Character]) -> bool:
        """验证所有关系"""
        # 检查重复关系
        character_ids = [rel.character_id for rel in character.relationships]
        if len(character_ids) != len(set(character_ids)):
            raise ValidationError("与同一角色存在多个关系")

        # 验证每个关系
        for relationship in character.relationships:
            self.validate_relationship(character, relationship, characters)

        return True


class SceneCharacterValidator:
    """场景角色验证器"""

    def validate_character_presence(self, scene: Scene, characters: List[Character]) -> bool:
        """验证场景中角色的存在性"""
        for character_id_str in scene.characters:
            try:
                character_id = UUID(character_id_str)
                if not any(char.id == character_id for char in characters):
                    raise ValidationError("场景中包含不存在的角色")
            except ValueError:
                raise ValidationError("场景中包含无效的角色ID")
        return True

    def validate_scene_character_count(self, scene: Scene) -> bool:
        """验证场景角色数量"""
        # 这里可以添加场景角色数量的合理性检查
        return True


class EventParticipantValidator:
    """事件参与者验证器"""

    def validate_participant_existence(self, event: Event, characters: List[Character]) -> bool:
        """验证事件参与者存在性"""
        for participation in event.participants:
            if not any(char.id == participation.character_id for char in characters):
                raise ValidationError("事件参与者中包含不存在的角色")
        return True

    def validate_participation_roles(self, event: Event) -> bool:
        """验证参与角色"""
        # 这里可以添加角色分配的合理性检查
        return True

    def validate_involvement_consistency(self, event: Event) -> bool:
        """验证参与程度一致性"""
        # 这里可以添加参与程度的一致性检查
        return True
    
    def get_validator_name(self) -> str:
        return "character_relationship_validator"
    
    def _validate_bidirectional_consistency(self, character: Character, context: ValidationContext) -> bool:
        """验证双向关系一致性"""
        # 获取相关的其他角色
        other_characters = context.get_related_models('characters')
        
        for relationship in character.character_relationships:
            target_id = relationship.target_character_id
            
            # 查找目标角色
            target_character = None
            for other_char in other_characters:
                if isinstance(other_char, Character) and other_char.id == target_id:
                    target_character = other_char
                    break
            
            if target_character:
                # 检查目标角色是否有对应的反向关系
                reverse_relationship = None
                for target_rel in target_character.character_relationships:
                    if target_rel.target_character_id == character.id:
                        reverse_relationship = target_rel
                        break
                
                if not reverse_relationship:
                    context.add_warning(f"角色{character.name}与{target_character.name}的关系缺少反向关系")
                else:
                    # 检查关系类型的一致性
                    if relationship.relationship_type != reverse_relationship.relationship_type:
                        context.add_warning(f"角色{character.name}与{target_character.name}的关系类型不一致")
        
        return True
    
    def _validate_relationship_logic(self, character: Character, context: ValidationContext) -> bool:
        """验证关系逻辑合理性"""
        # 检查冲突的关系
        romantic_relationships = []
        family_relationships = []
        
        for relationship in character.character_relationships:
            if relationship.relationship_type.value == 'romantic':
                romantic_relationships.append(relationship)
            elif relationship.relationship_type.value == 'family':
                family_relationships.append(relationship)
        
        # 检查多重恋爱关系
        active_romantic = [r for r in romantic_relationships 
                          if not r.relationship_end or r.relationship_end > datetime.now()]
        if len(active_romantic) > 1:
            context.add_warning(f"角色{character.name}同时有多个活跃的恋爱关系")
        
        return True
    
    def _validate_relationship_network(self, character: Character, context: ValidationContext) -> bool:
        """验证关系网络完整性"""
        # 检查孤立角色
        if character.importance.value == 'critical' and len(character.character_relationships) == 0:
            context.add_warning(f"关键角色{character.name}没有任何关系，可能过于孤立")
        
        return True


class SceneEventRelationshipValidator(RelationshipValidator):
    """场景事件关系验证器"""
    
    def validate_relationships(self, model: BaseModel, context: ValidationContext) -> bool:
        all_passed = True
        
        if isinstance(model, Scene):
            if not self._validate_scene_relationships(model, context):
                all_passed = False
        elif isinstance(model, Event):
            if not self._validate_event_relationships(model, context):
                all_passed = False
        
        return all_passed
    
    def get_validator_name(self) -> str:
        return "scene_event_relationship_validator"
    
    def _validate_scene_relationships(self, scene: Scene, context: ValidationContext) -> bool:
        """验证场景关系"""
        # 获取相关的角色和事件
        characters = context.get_related_models('characters')
        events = context.get_related_models('events')
        
        # 验证场景中的角色是否存在
        for char_id in scene.typical_characters:
            char_exists = any(isinstance(c, Character) and c.id == char_id for c in characters)
            if not char_exists:
                context.add_warning(f"场景{scene.name}引用了不存在的角色ID: {char_id}")
        
        # 验证场景中的事件是否存在
        for event_id in scene.typical_events:
            event_exists = any(isinstance(e, Event) and e.id == event_id for e in events)
            if not event_exists:
                context.add_warning(f"场景{scene.name}引用了不存在的事件ID: {event_id}")
        
        return True
    
    def _validate_event_relationships(self, event: Event, context: ValidationContext) -> bool:
        """验证事件关系"""
        # 获取相关的角色和场景
        characters = context.get_related_models('characters')
        scenes = context.get_related_models('scenes')
        
        # 验证事件参与者是否存在
        for participant in event.participants:
            char_id = participant.character_id
            char_exists = any(isinstance(c, Character) and c.id == char_id for c in characters)
            if not char_exists:
                context.add_warning(f"事件{event.name}引用了不存在的角色ID: {char_id}")
        
        # 验证事件发生场景是否存在
        if hasattr(event, 'scene_id') and event.scene_id:
            scene_exists = any(isinstance(s, Scene) and s.id == event.scene_id for s in scenes)
            if not scene_exists:
                context.add_warning(f"事件{event.name}引用了不存在的场景ID: {event.scene_id}")
        
        return True


class ProjectElementRelationshipValidator(RelationshipValidator):
    """项目元素关系验证器"""
    
    def validate_relationships(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, WritingProject):
            return True
        
        project = model
        all_passed = True
        
        # 验证项目统计数据与实际元素数量的一致性
        if not self._validate_project_statistics(project, context):
            all_passed = False
        
        return all_passed
    
    def get_validator_name(self) -> str:
        return "project_element_relationship_validator"
    
    def _validate_project_statistics(self, project: WritingProject, context: ValidationContext) -> bool:
        """验证项目统计数据"""
        # 获取实际的元素数量
        characters = context.get_related_models('characters')
        scenes = context.get_related_models('scenes')
        events = context.get_related_models('events')
        
        actual_character_count = len([c for c in characters if isinstance(c, Character)])
        actual_scene_count = len([s for s in scenes if isinstance(s, Scene)])
        actual_event_count = len([e for e in events if isinstance(e, Event)])
        
        # 检查统计数据的一致性
        if project.character_count != actual_character_count:
            context.add_warning(f"项目角色统计({project.character_count})与实际数量({actual_character_count})不一致")
        
        if project.scene_count != actual_scene_count:
            context.add_warning(f"项目场景统计({project.scene_count})与实际数量({actual_scene_count})不一致")
        
        if project.event_count != actual_event_count:
            context.add_warning(f"项目事件统计({project.event_count})与实际数量({actual_event_count})不一致")
        
        return True


class TimelineConsistencyValidator(RelationshipValidator):
    """时间线一致性验证器"""
    
    def validate_relationships(self, model: BaseModel, context: ValidationContext) -> bool:
        # 获取所有有时间线信息的元素
        events = context.get_related_models('events')
        
        if not events:
            return True
        
        all_passed = True
        
        # 验证事件时间线的一致性
        if not self._validate_event_timeline(events, context):
            all_passed = False
        
        return all_passed
    
    def get_validator_name(self) -> str:
        return "timeline_consistency_validator"
    
    def _validate_event_timeline(self, events: List[BaseModel], context: ValidationContext) -> bool:
        """验证事件时间线"""
        event_list = [e for e in events if isinstance(e, Event)]
        
        # 按时间线位置排序
        sorted_events = sorted(event_list, key=lambda e: e.timeline_position)
        
        # 检查时间线位置的合理性
        for i in range(len(sorted_events) - 1):
            current_event = sorted_events[i]
            next_event = sorted_events[i + 1]
            
            # 检查序列顺序
            if current_event.sequence_order >= next_event.sequence_order:
                context.add_warning(f"事件{current_event.name}和{next_event.name}的序列顺序可能有问题")
            
            # 检查因果关系
            if next_event.id in current_event.consequences:
                # 后续事件应该在当前事件之后
                if next_event.timeline_position <= current_event.timeline_position:
                    context.add_error(f"因果关系错误：{next_event.name}应该在{current_event.name}之后发生")
                    return False
        
        return True


class PlotConsistencyValidator(RelationshipValidator):
    """情节一致性验证器"""
    
    def validate_relationships(self, model: BaseModel, context: ValidationContext) -> bool:
        # 获取所有故事元素
        characters = context.get_related_models('characters')
        events = context.get_related_models('events')
        scenes = context.get_related_models('scenes')
        
        all_passed = True
        
        # 验证情节发展的合理性
        if not self._validate_plot_development(characters, events, scenes, context):
            all_passed = False
        
        return all_passed
    
    def get_validator_name(self) -> str:
        return "plot_consistency_validator"
    
    def _validate_plot_development(self, characters, events, scenes, context: ValidationContext) -> bool:
        """验证情节发展"""
        event_list = [e for e in events if isinstance(e, Event)]
        
        # 检查情节结构的完整性
        has_setup = any(e.plot_function.value == 'setup' for e in event_list)
        has_climax = any(e.is_climax for e in event_list)
        has_resolution = any(e.is_resolution for e in event_list)
        
        if not has_setup:
            context.add_warning("故事缺少设置/铺垫事件")
        
        if not has_climax:
            context.add_warning("故事缺少高潮事件")
        
        if not has_resolution:
            context.add_warning("故事缺少解决/结局事件")
        
        # 检查主角的参与度
        character_list = [c for c in characters if isinstance(c, Character)]
        protagonists = [c for c in character_list if c.story_role and '主角' in c.story_role]
        
        if protagonists:
            for protagonist in protagonists:
                # 检查主角在关键事件中的参与
                critical_events = [e for e in event_list if e.importance.value == 'critical']
                protagonist_participation = 0
                
                for event in critical_events:
                    if any(p.character_id == protagonist.id for p in event.participants):
                        protagonist_participation += 1
                
                participation_rate = protagonist_participation / len(critical_events) if critical_events else 0
                if participation_rate < 0.5:
                    context.add_warning(f"主角{protagonist.name}在关键事件中的参与度较低({participation_rate:.1%})")
        
        return True
