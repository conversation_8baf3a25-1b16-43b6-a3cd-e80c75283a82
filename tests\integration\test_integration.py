"""
集成测试模块
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from uuid import uuid4

from src.core.storage.manager import StorageManager
from src.core.ai.manager import AIServiceManager
from src.core.validation.validators import DataValidator
from src.core.models.project import WritingProject
from src.core.models.character import Character, CharacterRelationship
from src.core.models.scene import Scene
from src.core.models.event import Event, EventParticipation
from src.core.models.enums import (
    ProjectType, CharacterRole, RelationType, SceneType, 
    EventImportance, ParticipationRole
)


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def storage_manager(temp_dir):
    """创建存储管理器"""
    return StorageManager(temp_dir)


@pytest.fixture
def ai_manager():
    """创建AI管理器"""
    with patch('src.core.ai.manager.OpenAIAdapter') as mock_adapter:
        mock_adapter.return_value = Mock()
        return AIServiceManager()


@pytest.fixture
def data_validator():
    """创建数据验证器"""
    return DataValidator()


@pytest.fixture
def complete_project():
    """创建完整的项目数据"""
    project = WritingProject(
        name="完整测试项目",
        author="集成测试作者",
        description="用于集成测试的完整项目",
        project_type=ProjectType.NOVEL
    )
    return project


class TestStorageAIIntegration:
    """存储与AI服务集成测试"""
    
    def test_project_creation_with_ai_assistance(self, storage_manager, ai_manager, complete_project):
        """测试使用AI辅助创建项目"""
        # 模拟AI生成项目描述
        with patch.object(ai_manager, 'generate_content') as mock_generate:
            mock_generate.return_value = "AI生成的项目描述"
            
            # 使用AI增强项目描述
            enhanced_description = ai_manager.generate_content(
                "为小说项目生成描述",
                {"project_name": complete_project.name}
            )
            complete_project.description = enhanced_description
            
            # 保存项目
            project_path = storage_manager.save_project(complete_project)
            
            # 验证项目保存成功
            assert Path(project_path).exists()
            
            # 加载并验证项目
            loaded_project = storage_manager.load_project(project_path)
            assert loaded_project.description == "AI生成的项目描述"
            
    def test_character_creation_workflow(self, storage_manager, ai_manager, complete_project):
        """测试角色创建工作流"""
        # 保存项目
        storage_manager.save_project(complete_project)
        
        # 使用AI生成角色
        with patch.object(ai_manager, 'generate_character_description') as mock_generate:
            mock_generate.return_value = {
                "name": "AI生成角色",
                "description": "AI生成的角色描述",
                "personality": "勇敢、智慧、善良"
            }
            
            # 生成角色数据
            ai_character_data = ai_manager.generate_character_description({
                "role": "主角",
                "genre": "奇幻"
            })
            
            # 创建角色
            character = Character(
                name=ai_character_data["name"],
                full_name=ai_character_data["name"],
                description=ai_character_data["description"],
                role=CharacterRole.PROTAGONIST
            )
            
            # 保存角色
            character_path = storage_manager.save_character(complete_project.id, character)
            
            # 验证角色保存成功
            assert Path(character_path).exists()
            
            # 加载并验证角色
            loaded_character = storage_manager.load_character(complete_project.id, character.id)
            assert loaded_character.name == "AI生成角色"
            assert loaded_character.description == "AI生成的角色描述"


class TestStorageValidationIntegration:
    """存储与验证系统集成测试"""
    
    def test_project_validation_before_save(self, storage_manager, data_validator, complete_project):
        """测试保存前项目验证"""
        # 验证项目
        validation_result = data_validator.validate_project(complete_project)
        assert validation_result.is_valid is True
        
        # 保存验证通过的项目
        project_path = storage_manager.save_project(complete_project)
        assert Path(project_path).exists()
        
        # 测试无效项目
        invalid_project = WritingProject(
            name="",  # 空名称
            author="",  # 空作者
            description=""
        )
        
        validation_result = data_validator.validate_project(invalid_project)
        assert validation_result.is_valid is False
        assert len(validation_result.errors) > 0
        
    def test_character_relationship_validation(self, storage_manager, data_validator, complete_project):
        """测试角色关系验证"""
        # 保存项目
        storage_manager.save_project(complete_project)
        
        # 创建角色
        char1 = Character(name="角色1", full_name="角色1", role=CharacterRole.PROTAGONIST)
        char2 = Character(name="角色2", full_name="角色2", role=CharacterRole.SUPPORTING)
        
        # 保存角色
        storage_manager.save_character(complete_project.id, char1)
        storage_manager.save_character(complete_project.id, char2)
        
        # 创建关系
        relationship = CharacterRelationship(
            character_id=char2.id,
            relation_type=RelationType.FRIEND,
            intimacy_level=0.8,
            trust_level=0.9,
            conflict_level=0.1
        )
        char1.relationships = [relationship]
        
        # 验证角色关系
        characters = [char1, char2]
        validation_result = data_validator.validate_character_relationships(char1, characters)
        assert validation_result.is_valid is True
        
        # 更新角色
        storage_manager.save_character(complete_project.id, char1)
        
        # 加载并验证关系
        loaded_char1 = storage_manager.load_character(complete_project.id, char1.id)
        assert len(loaded_char1.relationships) == 1
        assert loaded_char1.relationships[0].character_id == char2.id


class TestCompleteWorkflow:
    """完整工作流测试"""
    
    def test_novel_writing_workflow(self, storage_manager, ai_manager, data_validator):
        """测试小说写作完整工作流"""
        # 1. 创建项目
        project = WritingProject(
            name="测试小说",
            author="测试作者",
            description="测试小说描述",
            project_type=ProjectType.NOVEL
        )
        
        # 验证并保存项目
        validation_result = data_validator.validate_project(project)
        assert validation_result.is_valid is True
        
        project_path = storage_manager.save_project(project)
        assert Path(project_path).exists()
        
        # 2. 创建主要角色
        protagonist = Character(
            name="主角",
            full_name="主角全名",
            role=CharacterRole.PROTAGONIST,
            age=25,
            description="勇敢的主角"
        )
        
        antagonist = Character(
            name="反派",
            full_name="反派全名",
            role=CharacterRole.ANTAGONIST,
            age=35,
            description="邪恶的反派"
        )
        
        # 验证并保存角色
        validation_result = data_validator.validate_character(protagonist)
        assert validation_result.is_valid is True
        
        storage_manager.save_character(project.id, protagonist)
        storage_manager.save_character(project.id, antagonist)
        
        # 3. 创建场景
        scene = Scene(
            name="开场场景",
            scene_type=SceneType.OUTDOOR,
            location="森林",
            description="神秘的森林场景",
            characters=[str(protagonist.id)]
        )
        
        # 验证并保存场景
        characters = [protagonist, antagonist]
        validation_result = data_validator.validate_scene(scene, characters)
        assert validation_result.is_valid is True
        
        storage_manager.save_scene(project.id, scene)
        
        # 4. 创建事件
        event = Event(
            name="初次相遇",
            importance=EventImportance.HIGH,
            timeline_position=1,
            description="主角与反派的初次相遇",
            participants=[
                EventParticipation(
                    character_id=protagonist.id,
                    role=ParticipationRole.PROTAGONIST,
                    involvement_level=0.9
                ),
                EventParticipation(
                    character_id=antagonist.id,
                    role=ParticipationRole.ANTAGONIST,
                    involvement_level=0.8
                )
            ]
        )
        
        # 验证并保存事件
        validation_result = data_validator.validate_event(event, characters)
        assert validation_result.is_valid is True
        
        storage_manager.save_event(project.id, event)
        
        # 5. 使用AI生成内容
        with patch.object(ai_manager, 'generate_scene_description') as mock_generate:
            mock_generate.return_value = "AI生成的场景描述"
            
            # 生成场景描述
            ai_description = ai_manager.generate_scene_description({
                "scene_name": scene.name,
                "location": scene.location,
                "characters": [protagonist.name]
            })
            
            # 更新场景描述
            scene.description = ai_description
            storage_manager.save_scene(project.id, scene)
            
        # 6. 验证完整项目
        loaded_project = storage_manager.load_project(project_path)
        assert loaded_project.name == project.name
        
        # 验证项目统计
        stats = storage_manager.get_project_statistics(project.id)
        assert stats['character_count'] >= 2
        assert stats['scene_count'] >= 1
        assert stats['event_count'] >= 1
        
    def test_project_backup_and_restore_workflow(self, storage_manager, complete_project):
        """测试项目备份和恢复工作流"""
        # 1. 创建完整项目数据
        storage_manager.save_project(complete_project)
        
        # 添加一些角色和场景
        character = Character(name="测试角色", full_name="测试角色")
        scene = Scene(name="测试场景", scene_type=SceneType.INDOOR, location="测试地点")
        
        storage_manager.save_character(complete_project.id, character)
        storage_manager.save_scene(complete_project.id, scene)
        
        # 2. 创建备份
        backup_path = storage_manager.backup_project(complete_project.id)
        assert Path(backup_path).exists()
        
        # 3. 删除原项目
        storage_manager.delete_project(complete_project.id)
        
        # 验证项目已删除
        with pytest.raises(Exception):
            storage_manager.load_project_by_id(complete_project.id)
            
        # 4. 从备份恢复
        restored_path = storage_manager.restore_project(backup_path)
        assert Path(restored_path).exists()
        
        # 5. 验证恢复的项目
        restored_project = storage_manager.load_project(restored_path)
        assert restored_project.name == complete_project.name
        assert restored_project.author == complete_project.author
        
        # 验证角色和场景也被恢复
        restored_characters = storage_manager.list_characters(restored_project.id)
        restored_scenes = storage_manager.list_scenes(restored_project.id)
        
        assert len(restored_characters) >= 1
        assert len(restored_scenes) >= 1
        assert any(c.name == character.name for c in restored_characters)
        assert any(s.name == scene.name for s in restored_scenes)


class TestErrorHandlingIntegration:
    """错误处理集成测试"""
    
    def test_storage_error_recovery(self, storage_manager, complete_project):
        """测试存储错误恢复"""
        # 保存项目
        project_path = storage_manager.save_project(complete_project)
        
        # 模拟存储错误
        with patch.object(storage_manager.file_storage, 'save') as mock_save:
            mock_save.side_effect = Exception("存储错误")
            
            # 尝试保存角色，应该优雅地处理错误
            character = Character(name="测试角色", full_name="测试角色")
            
            with pytest.raises(Exception):
                storage_manager.save_character(complete_project.id, character)
                
        # 验证原项目仍然可以加载
        loaded_project = storage_manager.load_project(project_path)
        assert loaded_project.name == complete_project.name
        
    def test_ai_service_error_handling(self, ai_manager):
        """测试AI服务错误处理"""
        # 模拟AI服务错误
        with patch.object(ai_manager, 'get_service') as mock_get_service:
            mock_service = Mock()
            mock_service.generate_content.side_effect = Exception("AI服务错误")
            mock_get_service.return_value = mock_service
            
            # 应该优雅地处理AI服务错误
            with pytest.raises(Exception):
                ai_manager.generate_content("测试提示", {})
                
    def test_validation_error_handling(self, data_validator):
        """测试验证错误处理"""
        # 创建无效项目
        invalid_project = WritingProject(
            name="",  # 空名称
            author="",  # 空作者
        )
        
        # 验证应该返回错误而不是抛出异常
        validation_result = data_validator.validate_project(invalid_project)
        assert validation_result.is_valid is False
        assert len(validation_result.errors) > 0
        
        # 验证错误信息有用
        error_messages = [error.message for error in validation_result.errors]
        assert any("名称" in msg for msg in error_messages)
        assert any("作者" in msg for msg in error_messages)


class TestPerformanceIntegration:
    """性能集成测试"""
    
    def test_large_project_handling(self, storage_manager, ai_manager):
        """测试大型项目处理"""
        import time
        
        # 创建大型项目
        project = WritingProject(
            name="大型项目",
            author="测试作者",
            description="大型项目描述"
        )
        
        start_time = time.time()
        
        # 保存项目
        storage_manager.save_project(project)
        
        # 创建大量角色
        characters = []
        for i in range(50):
            character = Character(
                name=f"角色{i}",
                full_name=f"角色{i}全名",
                description=f"角色{i}的描述"
            )
            characters.append(character)
            storage_manager.save_character(project.id, character)
            
        # 创建大量场景
        for i in range(30):
            scene = Scene(
                name=f"场景{i}",
                scene_type=SceneType.INDOOR,
                location=f"地点{i}",
                description=f"场景{i}的描述"
            )
            storage_manager.save_scene(project.id, scene)
            
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证性能在合理范围内
        assert total_time < 10.0, f"大型项目处理时间过长: {total_time:.3f}秒"
        
        # 验证数据完整性
        stats = storage_manager.get_project_statistics(project.id)
        assert stats['character_count'] == 50
        assert stats['scene_count'] == 30
