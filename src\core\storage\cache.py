"""缓存管理器

提供内存缓存和磁盘缓存功能，包括：
- LRU内存缓存
- 磁盘缓存持久化
- 缓存失效策略
- 缓存统计和监控
"""

import pickle
import time
import threading
from collections import OrderedDict
from pathlib import Path
from typing import Any, Dict, Optional, Union, Callable
from uuid import UUID
from datetime import datetime, timedelta
import hashlib
import json

from .serializer import JSONSerializer


class CacheError(Exception):
    """缓存错误"""
    pass


class CacheEntry:
    """缓存条目"""
    
    def __init__(self, value: Any, ttl: Optional[float] = None):
        """初始化缓存条目
        
        Args:
            value: 缓存值
            ttl: 生存时间（秒）
        """
        self.value = value
        self.created_at = time.time()
        self.accessed_at = self.created_at
        self.access_count = 0
        self.ttl = ttl
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """更新访问时间"""
        self.accessed_at = time.time()
        self.access_count += 1


class LRUCache:
    """LRU内存缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        """初始化LRU缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认生存时间（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self._hits = 0
        self._misses = 0
        self._evictions = 0
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._misses += 1
                return default
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._misses += 1
                return default
            
            # 更新访问信息
            entry.touch()
            
            # 移动到末尾（最近使用）
            self._cache.move_to_end(key)
            
            self._hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """设置缓存值"""
        with self._lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # 如果已存在，更新值
            if key in self._cache:
                self._cache[key] = CacheEntry(value, ttl)
                self._cache.move_to_end(key)
                return
            
            # 检查是否需要清理
            while len(self._cache) >= self.max_size:
                # 移除最久未使用的条目
                oldest_key, _ = self._cache.popitem(last=False)
                self._evictions += 1
            
            # 添加新条目
            self._cache[key] = CacheEntry(value, ttl)
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
    
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': hit_rate,
                'evictions': self._evictions
            }


class DiskCache:
    """磁盘缓存"""
    
    def __init__(self, cache_dir: Union[str, Path], max_size_mb: int = 100):
        """初始化磁盘缓存
        
        Args:
            cache_dir: 缓存目录
            max_size_mb: 最大缓存大小（MB）
        """
        self.cache_dir = Path(cache_dir)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.serializer = JSONSerializer()
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 元数据文件
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self._load_metadata()
    
    def _load_metadata(self):
        """加载元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            except Exception:
                self.metadata = {}
        else:
            self.metadata = {}
    
    def _save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception:
            pass
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名过长
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        try:
            cache_path = self._get_cache_path(key)
            
            if not cache_path.exists():
                return default
            
            # 检查元数据中的过期时间
            if key in self.metadata:
                entry_meta = self.metadata[key]
                if 'expires_at' in entry_meta:
                    expires_at = datetime.fromisoformat(entry_meta['expires_at'])
                    if datetime.now() > expires_at:
                        # 已过期，删除
                        self.delete(key)
                        return default
            
            # 读取缓存文件
            with open(cache_path, 'rb') as f:
                value = pickle.load(f)
            
            # 更新访问时间
            if key in self.metadata:
                self.metadata[key]['accessed_at'] = datetime.now().isoformat()
                self.metadata[key]['access_count'] = self.metadata[key].get('access_count', 0) + 1
                self._save_metadata()
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """设置缓存值"""
        try:
            cache_path = self._get_cache_path(key)
            
            # 写入缓存文件
            with open(cache_path, 'wb') as f:
                pickle.dump(value, f)
            
            # 更新元数据
            now = datetime.now()
            entry_meta = {
                'key': key,
                'created_at': now.isoformat(),
                'accessed_at': now.isoformat(),
                'access_count': 1,
                'file_size': cache_path.stat().st_size
            }
            
            if ttl is not None:
                entry_meta['expires_at'] = (now + timedelta(seconds=ttl)).isoformat()
            
            self.metadata[key] = entry_meta
            self._save_metadata()
            
            # 检查缓存大小
            self._cleanup_if_needed()
            
            return True
            
        except Exception:
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        try:
            cache_path = self._get_cache_path(key)
            
            if cache_path.exists():
                cache_path.unlink()
            
            if key in self.metadata:
                del self.metadata[key]
                self._save_metadata()
            
            return True
            
        except Exception:
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            
            self.metadata.clear()
            self._save_metadata()
            
        except Exception:
            pass
    
    def _cleanup_if_needed(self):
        """如果需要则清理缓存"""
        total_size = sum(entry.get('file_size', 0) for entry in self.metadata.values())
        
        if total_size > self.max_size_bytes:
            # 按访问时间排序，删除最久未访问的
            sorted_entries = sorted(
                self.metadata.items(),
                key=lambda x: x[1].get('accessed_at', ''),
            )
            
            for key, entry in sorted_entries:
                if total_size <= self.max_size_bytes * 0.8:  # 清理到80%
                    break
                
                self.delete(key)
                total_size -= entry.get('file_size', 0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_size = sum(entry.get('file_size', 0) for entry in self.metadata.values())
        total_access = sum(entry.get('access_count', 0) for entry in self.metadata.values())
        
        return {
            'entries': len(self.metadata),
            'total_size_bytes': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'max_size_mb': self.max_size_bytes / (1024 * 1024),
            'total_access_count': total_access
        }


class CacheManager:
    """缓存管理器
    
    统一管理内存缓存和磁盘缓存
    """
    
    def __init__(self, cache_dir: Union[str, Path], 
                 memory_cache_size: int = 1000,
                 disk_cache_size_mb: int = 100,
                 default_ttl: Optional[float] = None):
        """初始化缓存管理器"""
        self.memory_cache = LRUCache(memory_cache_size, default_ttl)
        self.disk_cache = DiskCache(cache_dir, disk_cache_size_mb)
        self.default_ttl = default_ttl
    
    def get(self, key: str, default: Any = None, use_disk: bool = True) -> Any:
        """获取缓存值"""
        # 先尝试内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 再尝试磁盘缓存
        if use_disk:
            value = self.disk_cache.get(key)
            if value is not None:
                # 将值放入内存缓存
                self.memory_cache.set(key, value)
                return value
        
        return default
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None, 
            use_disk: bool = True) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        # 设置内存缓存
        self.memory_cache.set(key, value, ttl)
        
        # 设置磁盘缓存
        if use_disk:
            self.disk_cache.set(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        memory_deleted = self.memory_cache.delete(key)
        disk_deleted = self.disk_cache.delete(key)
        return memory_deleted or disk_deleted
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.memory_cache.clear()
        self.disk_cache.clear()
    
    def cleanup(self) -> Dict[str, int]:
        """清理过期缓存"""
        memory_cleaned = self.memory_cache.cleanup_expired()
        return {
            'memory_cleaned': memory_cleaned
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'memory': self.memory_cache.get_stats(),
            'disk': self.disk_cache.get_stats()
        }
