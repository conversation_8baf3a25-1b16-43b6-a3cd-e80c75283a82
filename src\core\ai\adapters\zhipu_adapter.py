"""
智谱AI服务适配器

实现智谱AI API的具体适配逻辑。智谱AI API兼容OpenAI格式，因此继承OpenAI适配器。
"""

from typing import List, Optional
from ..base import AIProvider
from .openai_adapter import OpenAIAdapter


class ZhipuAdapter(OpenAIAdapter):
    """智谱AI服务适配器"""
    
    def __init__(self, api_key: str, model: str = "glm-4", base_url: Optional[str] = None):
        # 智谱AI使用自己的provider类型，但继承OpenAI的实现
        super().__init__(api_key, model, base_url)
        self.provider = AIProvider.ZHIPU
        self._default_base_url = "https://open.bigmodel.cn/api/paas/v4"
    
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        # 智谱AI的已知模型
        return [
            "glm-4",
            "glm-4-0520",
            "glm-4-air",
            "glm-4-airx",
            "glm-4-flash",
            "glm-4v",
            "glm-3-turbo"
        ]
    
    def _build_system_prompt(self, context) -> str:
        """构建系统提示词"""
        base_prompt = "你是智谱AI开发的GLM大模型，专门协助小说创作。请用中文回答，发挥你的创作能力。"
        
        if context.content_type.value == "character_description":
            return base_prompt + "请专注于创作生动、立体的角色描述，突出中国文化特色和人物深度。"
        elif context.content_type.value == "scene_description":
            return base_prompt + "请专注于创作富有画面感的场景描述，注重文学性和艺术感染力。"
        elif context.content_type.value == "plot_development":
            return base_prompt + "请专注于创作引人入胜的情节发展，体现深厚的文学功底。"
        elif context.content_type.value == "dialogue":
            return base_prompt + "请专注于创作自然、符合角色性格的对话，体现语言的魅力和文化内涵。"
        elif context.content_type.value == "content_optimization":
            return base_prompt + "请专注于优化和润色文本内容，提升文学品质和表达效果。"
        else:
            return base_prompt
    
    def _handle_error(self, error: Exception):
        """处理错误，更新错误消息中的服务商名称"""
        ai_error = super()._handle_error(error)
        # 将错误消息中的OpenAI替换为智谱AI
        ai_error.args = (ai_error.args[0].replace("OpenAI", "智谱AI"),)
        ai_error.provider = self.provider
        return ai_error
