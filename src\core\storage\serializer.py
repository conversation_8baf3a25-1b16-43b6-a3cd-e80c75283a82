"""JSON序列化器

提供数据模型的JSON序列化和反序列化功能，包括：
- 自定义类型处理（UUID、datetime、枚举等）
- 循环引用检测和处理
- 版本兼容性处理
- 数据完整性验证
"""

import json
import uuid
from datetime import datetime, date
from enum import Enum
from typing import Any, Dict, List, Optional, Type, Union, get_type_hints
from pathlib import Path
from uuid import UUID

from pydantic import BaseModel as PydanticBaseModel

from ..models.base import BaseModel
from ..models.project import WritingProject
from ..models.character import Character
from ..models.scene import Scene
from ..models.event import Event
from ..models.story_element import StoryElement


class SerializationError(Exception):
    """序列化错误"""
    pass


class DeserializationError(Exception):
    """反序列化错误"""
    pass


class JSONSerializer:
    """JSON序列化器
    
    提供数据模型的JSON序列化和反序列化功能，支持：
    - 自定义类型编码/解码
    - 循环引用检测
    - 版本兼容性
    - 数据验证
    """
    
    def __init__(self, indent: int = 2, ensure_ascii: bool = False):
        """初始化序列化器
        
        Args:
            indent: JSON缩进空格数
            ensure_ascii: 是否确保ASCII编码
        """
        self.indent = indent
        self.ensure_ascii = ensure_ascii
        self._circular_refs = set()
        
        # 注册自定义编码器
        self._encoders = {
            UUID: lambda x: str(x),
            datetime: lambda x: x.isoformat(),
            date: lambda x: x.isoformat(),
            Enum: lambda x: x.value,
            set: lambda x: list(x),
            frozenset: lambda x: list(x),
        }
        
        # 注册自定义解码器
        self._decoders = {
            'uuid': lambda x: UUID(x),
            'datetime': lambda x: datetime.fromisoformat(x),
            'date': lambda x: date.fromisoformat(x),
        }
    
    def serialize(self, obj: Any, file_path: Optional[Union[str, Path]] = None) -> str:
        """序列化对象为JSON字符串
        
        Args:
            obj: 要序列化的对象
            file_path: 可选的文件路径，如果提供则写入文件
            
        Returns:
            JSON字符串
            
        Raises:
            SerializationError: 序列化失败
        """
        try:
            self._circular_refs.clear()
            
            # 转换为可序列化的字典
            serializable_data = self._to_serializable(obj)
            
            # 添加元数据
            if isinstance(obj, BaseModel):
                serializable_data['__metadata__'] = {
                    'class_name': obj.__class__.__name__,
                    'module_name': obj.__class__.__module__,
                    'serialized_at': datetime.now().isoformat(),
                    'serializer_version': '1.0.0'
                }
            
            # 序列化为JSON
            json_str = json.dumps(
                serializable_data,
                indent=self.indent,
                ensure_ascii=self.ensure_ascii,
                default=self._default_encoder
            )
            
            # 写入文件（如果指定）
            if file_path:
                file_path = Path(file_path)
                file_path.parent.mkdir(parents=True, exist_ok=True)
                file_path.write_text(json_str, encoding='utf-8')
            
            return json_str
            
        except Exception as e:
            raise SerializationError(f"序列化失败: {e}") from e
    
    def deserialize(self, json_str: Optional[str] = None, 
                   file_path: Optional[Union[str, Path]] = None,
                   target_class: Optional[Type] = None) -> Any:
        """反序列化JSON字符串为对象
        
        Args:
            json_str: JSON字符串
            file_path: JSON文件路径
            target_class: 目标类型
            
        Returns:
            反序列化的对象
            
        Raises:
            DeserializationError: 反序列化失败
        """
        try:
            # 获取JSON数据
            if file_path:
                file_path = Path(file_path)
                if not file_path.exists():
                    raise DeserializationError(f"文件不存在: {file_path}")
                json_str = file_path.read_text(encoding='utf-8')
            
            if not json_str:
                raise DeserializationError("没有提供JSON数据")
            
            # 解析JSON
            data = json.loads(json_str)
            
            # 检查元数据
            metadata = data.pop('__metadata__', {})
            
            # 确定目标类型
            if not target_class and metadata:
                class_name = metadata.get('class_name')
                if class_name:
                    target_class = self._get_class_by_name(class_name)
            
            if not target_class:
                return data
            
            # 反序列化为对象
            return self._from_serializable(data, target_class)
            
        except Exception as e:
            raise DeserializationError(f"反序列化失败: {e}") from e
    
    def _to_serializable(self, obj: Any) -> Any:
        """转换对象为可序列化格式"""
        # 检查循环引用
        obj_id = id(obj)
        if obj_id in self._circular_refs:
            return {'__circular_ref__': obj_id}
        
        # Pydantic模型
        if isinstance(obj, PydanticBaseModel):
            self._circular_refs.add(obj_id)
            try:
                # 使用model_dump获取字典表示
                data = obj.model_dump()
                return self._to_serializable(data)
            finally:
                self._circular_refs.discard(obj_id)
        
        # 字典
        elif isinstance(obj, dict):
            return {key: self._to_serializable(value) for key, value in obj.items()}
        
        # 列表和元组
        elif isinstance(obj, (list, tuple)):
            return [self._to_serializable(item) for item in obj]
        
        # 集合
        elif isinstance(obj, (set, frozenset)):
            return list(obj)
        
        # 自定义类型
        else:
            for type_class, encoder in self._encoders.items():
                if isinstance(obj, type_class):
                    return encoder(obj)
        
        # 基本类型
        if obj is None or isinstance(obj, (str, int, float, bool)):
            return obj
        
        # 其他类型尝试转换为字符串
        return str(obj)
    
    def _from_serializable(self, data: Any, target_class: Type) -> Any:
        """从可序列化格式转换为对象"""
        # 处理循环引用
        if isinstance(data, dict) and '__circular_ref__' in data:
            # 这里可以实现循环引用的恢复逻辑
            return None
        
        # Pydantic模型
        if issubclass(target_class, PydanticBaseModel):
            # 递归处理嵌套对象
            processed_data = self._process_nested_data(data, target_class)
            return target_class(**processed_data)
        
        return data
    
    def _process_nested_data(self, data: Dict[str, Any], target_class: Type) -> Dict[str, Any]:
        """处理嵌套数据"""
        if not isinstance(data, dict):
            return data

        processed = {}

        # 获取字段信息
        try:
            type_hints = get_type_hints(target_class)
        except (NameError, AttributeError):
            type_hints = {}

        # 获取字段定义（对于Pydantic模型）
        field_info = {}
        if hasattr(target_class, '__fields__'):
            field_info = target_class.__fields__
        elif hasattr(target_class, 'model_fields'):
            field_info = target_class.model_fields

        for key, value in data.items():
            # 跳过元数据
            if key == '__metadata__':
                continue

            # 尝试从类型提示获取字段类型
            field_type = type_hints.get(key)

            # 如果没有类型提示，尝试从字段信息获取
            if field_type is None and key in field_info:
                field_def = field_info[key]
                if hasattr(field_def, 'annotation'):
                    field_type = field_def.annotation
                elif hasattr(field_def, 'type_'):
                    field_type = field_def.type_

            if field_type:
                processed[key] = self._convert_field_value(value, field_type)
            else:
                processed[key] = value

        return processed
    
    def _convert_field_value(self, value: Any, field_type: Type) -> Any:
        """转换字段值"""
        # UUID字段
        if field_type == UUID and isinstance(value, str):
            try:
                return UUID(value)
            except ValueError:
                return value

        # datetime字段
        elif field_type == datetime and isinstance(value, str):
            try:
                return datetime.fromisoformat(value)
            except ValueError:
                return value

        # 枚举字段 - 改进枚举检测逻辑
        elif (hasattr(field_type, '__bases__') and Enum in field_type.__bases__) or \
             (hasattr(field_type, '__mro__') and Enum in field_type.__mro__):
            if isinstance(value, str):
                try:
                    return field_type(value)
                except ValueError:
                    return value
            elif isinstance(value, field_type):
                return value

        # 列表字段
        elif hasattr(field_type, '__origin__') and field_type.__origin__ == list:
            if isinstance(value, list):
                return [self._convert_field_value(item, field_type.__args__[0]) for item in value]

        return value
    
    def _default_encoder(self, obj: Any) -> Any:
        """默认编码器"""
        for type_class, encoder in self._encoders.items():
            if isinstance(obj, type_class):
                return encoder(obj)
        
        raise TypeError(f"无法序列化类型: {type(obj)}")
    
    def _get_class_by_name(self, class_name: str) -> Optional[Type]:
        """根据类名获取类"""
        class_mapping = {
            'WritingProject': WritingProject,
            'Character': Character,
            'Scene': Scene,
            'Event': Event,
            'StoryElement': StoryElement,
        }
        
        return class_mapping.get(class_name)
    
    def validate_json(self, json_str: str) -> bool:
        """验证JSON格式"""
        try:
            json.loads(json_str)
            return True
        except json.JSONDecodeError:
            return False
    
    def get_metadata(self, json_str: str) -> Dict[str, Any]:
        """获取JSON元数据"""
        try:
            data = json.loads(json_str)
            return data.get('__metadata__', {})
        except json.JSONDecodeError:
            return {}
