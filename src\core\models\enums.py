"""数据模型枚举类型

定义应用程序中使用的所有枚举类型，包括：
- 项目相关枚举
- 元素相关枚举
- 角色相关枚举
- 场景相关枚举
- 事件相关枚举
- 章节相关枚举
"""

from enum import Enum, auto


class ProjectType(Enum):
    """项目类型"""
    FANTASY = "fantasy"           # 奇幻
    ROMANCE = "romance"           # 言情
    MYSTERY = "mystery"           # 悬疑
    SCIFI = "scifi"              # 科幻
    HISTORICAL = "historical"     # 历史
    CONTEMPORARY = "contemporary" # 现代
    THRILLER = "thriller"         # 惊悚
    ADVENTURE = "adventure"       # 冒险
    COMEDY = "comedy"            # 喜剧
    DRAMA = "drama"              # 剧情
    OTHER = "other"              # 其他


class ProjectGenre(Enum):
    """项目体裁（与ProjectType相同，为了兼容性）"""
    FANTASY = "奇幻小说"
    ROMANCE = "言情小说"
    MYSTERY = "悬疑小说"
    SCIFI = "科幻小说"
    HISTORICAL = "历史小说"
    CONTEMPORARY = "现代小说"
    THRILLER = "惊悚小说"
    ADVENTURE = "冒险小说"
    COMEDY = "喜剧小说"
    DRAMA = "剧情小说"
    URBAN = "都市小说"
    MARTIAL_ARTS = "武侠小说"
    XIANXIA = "仙侠小说"
    GAME = "游戏小说"
    MILITARY = "军事小说"
    BUSINESS = "商战小说"
    OTHER = "其他"


class ProjectStatus(Enum):
    """项目状态"""
    DRAFT = "draft"              # 草稿
    PLANNING = "planning"        # 规划中
    WRITING = "writing"          # 创作中
    REVISING = "revising"        # 修改中
    COMPLETED = "completed"      # 已完成
    PUBLISHED = "published"      # 已发布
    ARCHIVED = "archived"        # 已归档
    ABANDONED = "abandoned"      # 已放弃


class ElementType(Enum):
    """元素类型"""
    CHARACTER = "character"      # 角色
    SCENE = "scene"             # 场景
    EVENT = "event"             # 事件
    ITEM = "item"               # 物品
    CONCEPT = "concept"         # 概念
    UNKNOWN = "unknown"         # 未知


class ImportanceLevel(Enum):
    """重要性级别"""
    LOW = "low"                 # 低
    NORMAL = "normal"           # 普通
    HIGH = "high"               # 高
    CRITICAL = "critical"       # 关键


class Gender(Enum):
    """性别"""
    MALE = "male"               # 男性
    FEMALE = "female"           # 女性
    NON_BINARY = "non_binary"   # 非二元
    UNKNOWN = "unknown"         # 未知


class CharacterRole(Enum):
    """角色类型"""
    PROTAGONIST = "主角"         # 主角
    ANTAGONIST = "反派"          # 反派
    SUPPORTING = "配角"          # 配角
    MINOR = "次要角色"           # 次要角色
    BACKGROUND = "背景角色"      # 背景角色
    MENTOR = "导师"              # 导师
    LOVE_INTEREST = "恋爱对象"   # 恋爱对象
    COMIC_RELIEF = "喜剧角色"    # 喜剧角色
    VILLAIN = "反派"             # 反派
    HERO = "英雄"                # 英雄


class PersonalityTrait(Enum):
    """性格特质"""
    # 积极特质
    BRAVE = "勇敢"
    KIND = "善良"
    INTELLIGENT = "聪明"
    LOYAL = "忠诚"
    HONEST = "诚实"
    PATIENT = "耐心"
    OPTIMISTIC = "乐观"
    CONFIDENT = "自信"
    CREATIVE = "有创造力"
    DETERMINED = "坚定"
    GENEROUS = "慷慨"
    HUMBLE = "谦逊"

    # 消极特质
    COWARDLY = "胆小"
    CRUEL = "残忍"
    STUPID = "愚蠢"
    TREACHEROUS = "背叛"
    DISHONEST = "不诚实"
    IMPATIENT = "急躁"
    PESSIMISTIC = "悲观"
    ARROGANT = "傲慢"
    LAZY = "懒惰"
    WEAK_WILLED = "意志薄弱"
    SELFISH = "自私"
    PRIDEFUL = "骄傲"

    # 中性特质
    QUIET = "安静"
    TALKATIVE = "健谈"
    SERIOUS = "严肃"
    PLAYFUL = "爱玩"
    CAUTIOUS = "谨慎"
    IMPULSIVE = "冲动"
    INDEPENDENT = "独立"
    SOCIAL = "社交"


class RelationType(Enum):
    """关系类型"""
    # 角色关系
    FAMILY = "family"           # 家庭关系
    FRIEND = "friend"           # 朋友关系
    ENEMY = "enemy"             # 敌对关系
    ROMANTIC = "romantic"       # 恋爱关系
    MENTOR = "mentor"           # 师徒关系
    COLLEAGUE = "colleague"     # 同事关系
    RIVAL = "rival"             # 竞争关系
    ALLY = "ally"               # 盟友关系
    
    # 元素关系
    CONTAINS = "contains"       # 包含关系
    TRIGGERS = "triggers"       # 触发关系
    DEPENDS_ON = "depends_on"   # 依赖关系
    CONFLICTS_WITH = "conflicts_with"  # 冲突关系
    RELATED_TO = "related_to"   # 相关关系


class RelationshipStatus(Enum):
    """关系状态"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    DEVELOPING = "developing"   # 发展中
    DETERIORATING = "deteriorating"  # 恶化中
    ENDED = "ended"             # 已结束


class LocationType(Enum):
    """位置类型"""
    INDOOR = "indoor"           # 室内
    OUTDOOR = "outdoor"         # 室外
    NATURAL = "natural"         # 自然环境
    URBAN = "urban"             # 城市环境
    RURAL = "rural"             # 乡村环境
    FANTASY = "fantasy"         # 奇幻环境
    VIRTUAL = "virtual"         # 虚拟环境


class EventType(Enum):
    """事件类型"""
    SCENE = "scene"             # 场景事件
    DIALOGUE = "dialogue"       # 对话事件
    ACTION = "action"           # 动作事件
    INTERNAL = "internal"       # 内心事件
    FLASHBACK = "flashback"     # 回忆事件
    FORESHADOWING = "foreshadowing"  # 伏笔事件


class EventCategory(Enum):
    """事件类别"""
    NORMAL = "normal"           # 普通事件
    CLIMAX = "climax"           # 高潮事件
    TURNING_POINT = "turning_point"  # 转折点
    RESOLUTION = "resolution"   # 解决事件
    SETUP = "setup"             # 铺垫事件


class ParticipationRole(Enum):
    """参与角色"""
    PROTAGONIST = "protagonist" # 主角
    ANTAGONIST = "antagonist"   # 反角
    OBSERVER = "observer"       # 观察者
    CATALYST = "catalyst"       # 催化剂
    VICTIM = "victim"           # 受害者
    HELPER = "helper"           # 帮助者


class PlotFunction(Enum):
    """情节功能"""
    EXPOSITION = "exposition"   # 说明
    DEVELOPMENT = "development" # 发展
    CLIMAX = "climax"           # 高潮
    RESOLUTION = "resolution"   # 解决
    TRANSITION = "transition"   # 过渡


class ChapterStatus(Enum):
    """章节状态"""
    DRAFT = "draft"             # 草稿
    WRITING = "writing"         # 创作中
    COMPLETED = "completed"     # 已完成
    REVIEWING = "reviewing"     # 审阅中
    PUBLISHED = "published"     # 已发布


class ReviewStatus(Enum):
    """审阅状态"""
    PENDING = "pending"         # 待审阅
    IN_REVIEW = "in_review"     # 审阅中
    APPROVED = "approved"       # 已通过
    REJECTED = "rejected"       # 已拒绝
    NEEDS_REVISION = "needs_revision"  # 需要修改


class ElementRole(Enum):
    """元素角色"""
    MAIN = "main"               # 主要
    SECONDARY = "secondary"     # 次要
    BACKGROUND = "background"   # 背景
    CAMEO = "cameo"             # 客串


class ChangeType(Enum):
    """变更类型"""
    CREATE = "create"           # 创建
    UPDATE = "update"           # 更新
    DELETE = "delete"           # 删除
    MOVE = "move"               # 移动
    RENAME = "rename"           # 重命名


class SceneFunction(Enum):
    """场景功能"""
    SETTING = "setting"         # 背景设定
    ACTION = "action"           # 动作场景
    DIALOGUE = "dialogue"       # 对话场景
    TRANSITION = "transition"   # 过渡场景
    CLIMAX = "climax"           # 高潮场景


class EventImpact(Enum):
    """事件影响"""
    MINIMAL = "minimal"         # 最小影响
    MINOR = "minor"             # 轻微影响
    MODERATE = "moderate"       # 中等影响
    MAJOR = "major"             # 重大影响
    CRITICAL = "critical"       # 关键影响


class CharacterArcType(Enum):
    """角色弧线类型"""
    GROWTH = "growth"           # 成长型
    FALL = "fall"               # 堕落型
    REDEMPTION = "redemption"   # 救赎型
    CORRUPTION = "corruption"   # 腐化型
    STATIC = "static"           # 静态型
    TRANSFORMATION = "transformation"  # 转变型


class TimeOfDay(Enum):
    """时间段"""
    DAWN = "dawn"               # 黎明
    MORNING = "morning"         # 上午
    NOON = "noon"               # 中午
    AFTERNOON = "afternoon"     # 下午
    EVENING = "evening"         # 傍晚
    NIGHT = "night"             # 夜晚
    MIDNIGHT = "midnight"       # 午夜


class Season(Enum):
    """季节"""
    SPRING = "spring"           # 春季
    SUMMER = "summer"           # 夏季
    AUTUMN = "autumn"           # 秋季
    WINTER = "winter"           # 冬季


class Weather(Enum):
    """天气"""
    SUNNY = "sunny"             # 晴朗
    CLOUDY = "cloudy"           # 多云
    RAINY = "rainy"             # 雨天
    STORMY = "stormy"           # 暴风雨
    SNOWY = "snowy"             # 雪天
    FOGGY = "foggy"             # 雾天
    WINDY = "windy"             # 大风


class SceneType(Enum):
    """场景类型"""
    INDOOR = "室内场景"
    OUTDOOR = "室外场景"
    NATURAL = "自然场景"
    URBAN = "城市场景"
    RURAL = "乡村场景"
    FANTASY = "奇幻场景"
    HISTORICAL = "历史场景"
    FUTURISTIC = "未来场景"
    VIRTUAL = "虚拟场景"
    DREAM = "梦境场景"


class Atmosphere(Enum):
    """氛围类型"""
    PEACEFUL = "平静"
    TENSE = "紧张"
    ROMANTIC = "浪漫"
    MYSTERIOUS = "神秘"
    SCARY = "恐怖"
    JOYFUL = "欢乐"
    MELANCHOLY = "忧郁"
    EXCITING = "兴奋"
    SOLEMN = "庄严"
    CHAOTIC = "混乱"
    NOSTALGIC = "怀旧"
    HOPEFUL = "充满希望"


class EventImportance(Enum):
    """事件重要程度"""
    TRIVIAL = "微不足道"
    MINOR = "次要"
    MODERATE = "中等"
    IMPORTANT = "重要"
    CRITICAL = "关键"
    PIVOTAL = "转折性"


class AIProvider(Enum):
    """AI服务提供商"""
    OPENAI = "OpenAI"
    DEEPSEEK = "DeepSeek"
    ZHIPU = "智谱AI"
    ANTHROPIC = "Anthropic"
    BAIDU = "百度文心"
    ALIBABA = "阿里通义"
    TENCENT = "腾讯混元"
