"""故事元素模型

定义故事元素的基础模型，包括：
- StoryElement: 故事元素基类
- ElementRelationship: 元素关系
- ElementVersion: 元素版本
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import Field, validator

from .base import BaseModel, ValidationResult, ModelValidator, VersionedModel
from .enums import ElementType, ImportanceLevel, RelationType, ElementRole


class ElementRelationship(BaseModel):
    """元素关系"""
    
    # 关系基础信息
    from_element: UUID = Field(..., description="源元素ID")
    to_element: UUID = Field(..., description="目标元素ID")
    relationship_type: RelationType = Field(..., description="关系类型")
    
    # 关系属性
    strength: float = Field(default=1.0, description="关系强度 (0.0-1.0)")
    description: str = Field(default="", description="关系描述")
    
    # 时间信息
    valid_from: Optional[datetime] = Field(default=None, description="关系开始时间")
    valid_to: Optional[datetime] = Field(default=None, description="关系结束时间")
    
    # 上下文信息
    context: Dict[str, Any] = Field(default_factory=dict, description="关系上下文")
    
    @validator('strength')
    def validate_strength(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("关系强度必须在0.0-1.0之间")
        return v
    
    @validator('valid_to')
    def validate_time_range(cls, v, values):
        if v and 'valid_from' in values and values['valid_from']:
            if v <= values['valid_from']:
                raise ValueError("结束时间必须晚于开始时间")
        return v
    
    def is_active(self, at_time: Optional[datetime] = None) -> bool:
        """检查关系在指定时间是否有效"""
        if at_time is None:
            at_time = datetime.now()
        
        if self.valid_from and at_time < self.valid_from:
            return False
        
        if self.valid_to and at_time > self.valid_to:
            return False
        
        return True


class ElementVersion(BaseModel):
    """元素版本"""
    
    # 版本信息
    version_number: int = Field(..., description="版本号")
    parent_version: Optional[int] = Field(default=None, description="父版本号")
    
    # 变更信息
    change_message: str = Field(default="", description="变更说明")
    changed_fields: List[str] = Field(default_factory=list, description="变更字段")
    
    # 版本数据
    version_data: Dict[str, Any] = Field(default_factory=dict, description="版本数据")
    
    # 元数据
    created_by: str = Field(default="user", description="创建者")
    
    @validator('version_number')
    def validate_version_number(cls, v):
        if v < 1:
            raise ValueError("版本号必须大于0")
        return v


class StoryElement(VersionedModel):
    """故事元素基类"""
    
    # 基础信息
    element_type: ElementType = Field(default=ElementType.UNKNOWN, description="元素类型")
    name: str = Field(..., description="元素名称")
    description: str = Field(default="", description="元素描述")
    
    # 分类信息
    category: str = Field(default="", description="分类")
    importance: ImportanceLevel = Field(default=ImportanceLevel.NORMAL, description="重要性级别")
    
    # 创建信息
    created_by: str = Field(default="user", description="创建者 (user|ai|import)")
    
    # 关联信息
    related_elements: List[UUID] = Field(default_factory=list, description="相关元素ID列表")
    relationships: List[ElementRelationship] = Field(default_factory=list, description="元素关系")
    
    # 出现统计
    appearance_count: int = Field(default=0, description="出现次数")
    first_appearance: Optional[UUID] = Field(default=None, description="首次出现的章节ID")
    last_appearance: Optional[UUID] = Field(default=None, description="最后出现的章节ID")
    
    # 章节关联
    chapter_appearances: List[Dict[str, Any]] = Field(default_factory=list, description="章节出现记录")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError("元素名称不能为空")
        
        if len(v) > 100:
            raise ValueError("元素名称不能超过100个字符")
        
        return v.strip()
    
    @validator('appearance_count')
    def validate_appearance_count(cls, v):
        if v < 0:
            raise ValueError("出现次数不能为负数")
        return v
    
    def _validate_specific(self, result: ValidationResult):
        """元素特定验证"""
        # 验证名称
        if not self.name or not self.name.strip():
            result.add_error("元素名称不能为空")
        elif len(self.name) > 100:
            result.add_error("元素名称不能超过100个字符")
        
        # 验证描述
        if self.description and len(self.description) > 2000:
            result.add_error("元素描述不能超过2000个字符")
        
        # 验证分类
        if self.category and len(self.category) > 50:
            result.add_error("分类名称不能超过50个字符")
        
        # 验证出现次数
        if self.appearance_count < 0:
            result.add_error("出现次数不能为负数")
        
        # 验证关系
        for relationship in self.relationships:
            rel_result = relationship.validate_model()
            result.merge(rel_result)
    
    def add_relationship(self, target_element: UUID, relationship_type: RelationType,
                        strength: float = 1.0, description: str = "",
                        context: Optional[Dict[str, Any]] = None):
        """添加关系"""
        relationship = ElementRelationship(
            from_element=self.id,
            to_element=target_element,
            relationship_type=relationship_type,
            strength=strength,
            description=description,
            context=context or {}
        )
        
        self.relationships.append(relationship)
        
        # 添加到相关元素列表
        if target_element not in self.related_elements:
            self.related_elements.append(target_element)
        
        self.update_timestamp()
    
    def remove_relationship(self, target_element: UUID, relationship_type: Optional[RelationType] = None):
        """移除关系"""
        original_count = len(self.relationships)
        
        if relationship_type:
            # 移除特定类型的关系
            self.relationships = [
                rel for rel in self.relationships
                if not (rel.to_element == target_element and rel.relationship_type == relationship_type)
            ]
        else:
            # 移除所有与目标元素的关系
            self.relationships = [
                rel for rel in self.relationships
                if rel.to_element != target_element
            ]
        
        # 如果没有关系了，从相关元素列表中移除
        if not any(rel.to_element == target_element for rel in self.relationships):
            if target_element in self.related_elements:
                self.related_elements.remove(target_element)
        
        # 如果有变化，更新时间戳
        if len(self.relationships) != original_count:
            self.update_timestamp()
    
    def get_relationships_by_type(self, relationship_type: RelationType) -> List[ElementRelationship]:
        """获取指定类型的关系"""
        return [rel for rel in self.relationships if rel.relationship_type == relationship_type]
    
    def get_related_elements_by_type(self, relationship_type: RelationType) -> List[UUID]:
        """获取指定关系类型的相关元素"""
        return [rel.to_element for rel in self.relationships if rel.relationship_type == relationship_type]
    
    def has_relationship_with(self, target_element: UUID, relationship_type: Optional[RelationType] = None) -> bool:
        """检查是否与目标元素有关系"""
        for rel in self.relationships:
            if rel.to_element == target_element:
                if relationship_type is None or rel.relationship_type == relationship_type:
                    return True
        return False
    
    def record_appearance(self, chapter_id: UUID, role: ElementRole = ElementRole.BACKGROUND,
                         position: int = 0, mention_count: int = 1):
        """记录在章节中的出现"""
        # 检查是否已经记录过
        for appearance in self.chapter_appearances:
            if appearance.get('chapter_id') == chapter_id:
                # 更新现有记录
                appearance['mention_count'] += mention_count
                appearance['last_position'] = position
                if role.value > ElementRole(appearance.get('role', 'background')).value:
                    appearance['role'] = role.value
                self.update_timestamp()
                return
        
        # 添加新记录
        appearance_record = {
            'chapter_id': chapter_id,
            'role': role.value,
            'first_position': position,
            'last_position': position,
            'mention_count': mention_count,
            'recorded_at': datetime.now().isoformat()
        }
        
        self.chapter_appearances.append(appearance_record)
        self.appearance_count += 1
        
        # 更新首次和最后出现
        if self.first_appearance is None:
            self.first_appearance = chapter_id
        self.last_appearance = chapter_id
        
        self.update_timestamp()
    
    def get_appearance_in_chapter(self, chapter_id: UUID) -> Optional[Dict[str, Any]]:
        """获取在指定章节中的出现记录"""
        for appearance in self.chapter_appearances:
            if appearance.get('chapter_id') == chapter_id:
                return appearance
        return None
    
    def get_chapters_appeared(self) -> List[UUID]:
        """获取出现过的章节列表"""
        return [UUID(app['chapter_id']) for app in self.chapter_appearances]
    
    def calculate_importance_score(self) -> float:
        """计算重要性得分"""
        # 基础重要性得分
        base_score = {
            ImportanceLevel.LOW: 0.25,
            ImportanceLevel.NORMAL: 0.5,
            ImportanceLevel.HIGH: 0.75,
            ImportanceLevel.CRITICAL: 1.0
        }.get(self.importance, 0.5)
        
        # 出现次数加权
        appearance_weight = min(self.appearance_count / 10.0, 0.5)
        
        # 关系数量加权
        relationship_weight = min(len(self.relationships) / 20.0, 0.3)
        
        return min(base_score + appearance_weight + relationship_weight, 1.0)


class StoryElementValidator(ModelValidator):
    """故事元素验证器"""
    
    def validate(self, model: StoryElement) -> ValidationResult:
        """验证故事元素"""
        result = ValidationResult()
        
        # 基础验证
        base_result = model.validate_model()
        result.merge(base_result)
        
        # 元素特定验证
        name_result = self.validate_required_string(model.name, "元素名称", 100)
        result.merge(name_result)
        
        desc_result = self.validate_optional_string(model.description, "元素描述", 2000)
        result.merge(desc_result)
        
        category_result = self.validate_optional_string(model.category, "分类", 50)
        result.merge(category_result)
        
        # 验证出现次数
        if model.appearance_count < 0:
            result.add_error("出现次数不能为负数")
        
        # 验证关系
        for i, relationship in enumerate(model.relationships):
            rel_result = self._validate_relationship(relationship)
            if not rel_result.is_valid:
                for error in rel_result.errors:
                    result.add_error(f"关系{i+1}: {error}")
        
        return result
    
    def _validate_relationship(self, relationship: ElementRelationship) -> ValidationResult:
        """验证关系"""
        result = ValidationResult()
        
        # 验证关系强度
        if relationship.strength < 0.0 or relationship.strength > 1.0:
            result.add_error("关系强度必须在0.0-1.0之间")
        
        # 验证时间范围
        if (relationship.valid_from and relationship.valid_to and 
            relationship.valid_to <= relationship.valid_from):
            result.add_error("关系结束时间必须晚于开始时间")
        
        # 验证描述长度
        if relationship.description and len(relationship.description) > 500:
            result.add_error("关系描述不能超过500个字符")
        
        return result
