<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b___init___py.html">src\core\ai\__init__.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060___init___py.html">src\core\ai\adapters\__init__.py</a></td>
                <td class="name left"><a href="z_708c621716977060___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t31">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t31"><data value='init__'>AnthropicAdapter.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t38">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t38"><data value='initialize'>AnthropicAdapter.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t45">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t45"><data value='generate_content'>AnthropicAdapter.generate_content</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t98">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t98"><data value='generate_content_stream'>AnthropicAdapter.generate_content_stream</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t147">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t147"><data value='validate_api_key'>AnthropicAdapter.validate_api_key</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t166">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t166"><data value='get_available_models'>AnthropicAdapter.get_available_models</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t177">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t177"><data value='estimate_tokens'>AnthropicAdapter.estimate_tokens</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t186">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t186"><data value='build_system_prompt'>AnthropicAdapter._build_system_prompt</data></a></td>
                <td>12</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="5 12">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t203">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t203"><data value='handle_error'>AnthropicAdapter._handle_error</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t15">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t15"><data value='init__'>DeepSeekAdapter.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t21">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t21"><data value='get_available_models'>DeepSeekAdapter.get_available_models</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t30">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t30"><data value='build_system_prompt'>DeepSeekAdapter._build_system_prompt</data></a></td>
                <td>12</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="6 12">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t47">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t47"><data value='handle_error'>DeepSeekAdapter._handle_error</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t30">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t30"><data value='init__'>OpenAIAdapter.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t37">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t37"><data value='initialize'>OpenAIAdapter.initialize</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t44">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t44"><data value='generate_content'>OpenAIAdapter.generate_content</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t93">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t93"><data value='generate_content_stream'>OpenAIAdapter.generate_content_stream</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t137">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t137"><data value='validate_api_key'>OpenAIAdapter.validate_api_key</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t156">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t156"><data value='get_available_models'>OpenAIAdapter.get_available_models</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t167">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t167"><data value='estimate_tokens'>OpenAIAdapter.estimate_tokens</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t177">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t177"><data value='build_messages'>OpenAIAdapter._build_messages</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t191">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t191"><data value='build_system_prompt'>OpenAIAdapter._build_system_prompt</data></a></td>
                <td>12</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="3 12">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t208">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t208"><data value='handle_error'>OpenAIAdapter._handle_error</data></a></td>
                <td>13</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="4 13">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t15">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t15"><data value='init__'>ZhipuAdapter.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t21">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t21"><data value='get_available_models'>ZhipuAdapter.get_available_models</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t34">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t34"><data value='build_system_prompt'>ZhipuAdapter._build_system_prompt</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t51">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t51"><data value='handle_error'>ZhipuAdapter._handle_error</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t90">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t90"><data value='init__'>AIServiceError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t103">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t103"><data value='init__'>RateLimitError.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t126">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t126"><data value='init__'>AIService.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t134">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t134"><data value='initialize'>AIService.initialize</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t139">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t139"><data value='generate_content'>AIService.generate_content</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t149">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t149"><data value='generate_content_stream'>AIService.generate_content_stream</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t159">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t159"><data value='validate_api_key'>AIService.validate_api_key</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t164">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t164"><data value='get_available_models'>AIService.get_available_models</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t169">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t169"><data value='estimate_tokens'>AIService.estimate_tokens</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t173">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t173"><data value='health_check'>AIService.health_check</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t184">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t184"><data value='init__'>PromptTemplate.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t188">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t188"><data value='format'>PromptTemplate.format</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t192">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t192"><data value='validate_variables'>PromptTemplate.validate_variables</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t202">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t202"><data value='init__'>PromptManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t206">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t206"><data value='load_default_templates'>PromptManager._load_default_templates</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t243">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t243"><data value='get_template'>PromptManager.get_template</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t247">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t247"><data value='add_template'>PromptManager.add_template</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t251">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t251"><data value='format_prompt'>PromptManager.format_prompt</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>84</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="84 84">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t23">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t23"><data value='init__'>AIServiceManager.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t29">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t29"><data value='initialize'>AIServiceManager.initialize</data></a></td>
                <td>31</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="16 31">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t88">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t88"><data value='generate_content'>AIServiceManager.generate_content</data></a></td>
                <td>21</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="18 21">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t134">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t134"><data value='generate_content_stream'>AIServiceManager.generate_content_stream</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t168">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t168"><data value='validate_service'>AIServiceManager.validate_service</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t179">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t179"><data value='health_check'>AIServiceManager.health_check</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t193">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t193"><data value='get_available_providers'>AIServiceManager.get_available_providers</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t197">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t197"><data value='get_prompt_manager'>AIServiceManager.get_prompt_manager</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t201">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t201"><data value='select_service'>AIServiceManager._select_service</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t218">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t218"><data value='select_fallback_service'>AIServiceManager._select_fallback_service</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t234">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t234"><data value='init__'>ContentGenerator.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t237">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t237"><data value='generate_character_description'>ContentGenerator.generate_character_description</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t261">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t261"><data value='generate_scene_description'>ContentGenerator.generate_scene_description</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t285">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t285"><data value='generate_plot_development'>ContentGenerator.generate_plot_development</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t307">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t307"><data value='generate_dialogue'>ContentGenerator.generate_dialogue</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t331">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t331"><data value='optimize_content'>ContentGenerator.optimize_content</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de954926f2f9d31f___init___py.html">src\core\events\__init__.py</a></td>
                <td class="name left"><a href="z_de954926f2f9d31f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259___init___py.html">src\core\models\__init__.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t31">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t31"><data value='add_error'>ValidationResult.add_error</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t36">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t36"><data value='add_warning'>ValidationResult.add_warning</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t40">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t40"><data value='merge'>ValidationResult.merge</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t48">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t48"><data value='has_errors'>ValidationResult.has_errors</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t53">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t53"><data value='has_warnings'>ValidationResult.has_warnings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t92">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t92"><data value='init__'>BaseModel.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t100">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t100"><data value='set_updated_at'>BaseModel.set_updated_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t104">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t104"><data value='update_timestamp'>BaseModel.update_timestamp</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t109">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t109"><data value='to_dict'>BaseModel.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t113">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t113"><data value='to_json'>BaseModel.to_json</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t118">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t118"><data value='from_dict'>BaseModel.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t123">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t123"><data value='from_json'>BaseModel.from_json</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t127">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t127"><data value='calculate_checksum'>BaseModel.calculate_checksum</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t134">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t134"><data value='validate_model'>BaseModel.validate_model</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t156">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t156"><data value='validate_specific'>BaseModel._validate_specific</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t160">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t160"><data value='clone'>BaseModel.clone</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t170">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t170"><data value='merge_custom_fields'>BaseModel.merge_custom_fields</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t175">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t175"><data value='add_tag'>BaseModel.add_tag</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t181">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t181"><data value='remove_tag'>BaseModel.remove_tag</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t187">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t187"><data value='has_tag'>BaseModel.has_tag</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t196">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t196"><data value='validate'>ModelValidator.validate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t201">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t201"><data value='validate_required_string'>ModelValidator.validate_required_string</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t216">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t216"><data value='validate_optional_string'>ModelValidator.validate_optional_string</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t227">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t227"><data value='validate_range'>ModelValidator.validate_range</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t242">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t242"><data value='validate_list_length'>ModelValidator.validate_list_length</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t263">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t263"><data value='create_version_snapshot'>VersionedModel.create_version_snapshot</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t274">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t274"><data value='get_version_history'>VersionedModel.get_version_history</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t278">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t278"><data value='restore_version'>VersionedModel.restore_version</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>66</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="66 66">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t49">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t49"><data value='validate_age'>CharacterAppearance.validate_age</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t155">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t155"><data value='validate_levels'>CharacterRelationship.validate_levels</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t161">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t161"><data value='validate_relationship_duration'>CharacterRelationship.validate_relationship_duration</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t178">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t178"><data value='init__'>Character.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t208">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t208"><data value='validate_full_name'>Character.validate_full_name</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t217">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t217"><data value='validate_specific'>Character._validate_specific</data></a></td>
                <td>20</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="15 20">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t255">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t255"><data value='add_relationship'>Character.add_relationship</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t274">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t274"><data value='update_relationship'>Character.update_relationship</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t285">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t285"><data value='remove_relationship'>Character.remove_relationship</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t298">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t298"><data value='get_relationship'>Character.get_relationship</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t305">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t305"><data value='get_relationships_by_type'>Character.get_relationships_by_type</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t309">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t309"><data value='add_character_development'>Character.add_character_development</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t322">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t322"><data value='add_key_moment'>Character.add_key_moment</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t334">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t334"><data value='get_display_name'>Character.get_display_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t340">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t340"><data value='get_age'>Character.get_age</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t349">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t349"><data value='calculate_relationship_score'>Character.calculate_relationship_score</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t363">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t363"><data value='validate'>CharacterValidator.validate</data></a></td>
                <td>24</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="20 24">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t404">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t404"><data value='validate_character_relationship'>CharacterValidator._validate_character_relationship</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>116</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="116 116">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>257</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="257 257">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t42">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t42"><data value='validate_involvement_level'>EventParticipation.validate_involvement_level</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t72">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t72"><data value='validate_impact_range'>EventImpactData.validate_impact_range</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t78">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t78"><data value='validate_level_range'>EventImpactData.validate_level_range</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t130">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t130"><data value='init__'>Event.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t142">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t142"><data value='validate_level_fields'>Event.validate_level_fields</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t147">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t147"><data value='add_participant'>Event.add_participant</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t171">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t171"><data value='remove_participant'>Event.remove_participant</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t176">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t176"><data value='get_participant'>Event.get_participant</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t183">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t183"><data value='add_trigger'>Event.add_trigger</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t189">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t189"><data value='add_consequence'>Event.add_consequence</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t195">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t195"><data value='get_main_participants'>Event.get_main_participants</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t200">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t200"><data value='get_impact_score'>Event.get_impact_score</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t221">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t221"><data value='validate_model'>Event.validate_model</data></a></td>
                <td>21</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="13 21">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t261">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t261"><data value='validate_impact_data'>Event._validate_impact_data</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t287">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t287"><data value='validate'>EventValidator.validate</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t302">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t302"><data value='validate_event_completeness'>EventValidator._validate_event_completeness</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t319">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t319"><data value='validate_event_consistency'>EventValidator._validate_event_consistency</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t335">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t335"><data value='validate_event_relationships'>EventValidator._validate_event_relationships</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t44">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t44"><data value='validate_auto_save_interval'>ProjectSettings.validate_auto_save_interval</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t50">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t50"><data value='validate_font_size'>ProjectSettings.validate_font_size</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t89">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t89"><data value='validate_temperature'>AIConfiguration.validate_temperature</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t95">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t95"><data value='validate_max_tokens'>AIConfiguration.validate_max_tokens</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t134">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t134"><data value='validate_name'>WritingProject.validate_name</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t149">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t149"><data value='validate_completion_percentage'>WritingProject.validate_completion_percentage</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t155">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t155"><data value='validate_counts'>WritingProject.validate_counts</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t160">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t160"><data value='validate_specific'>WritingProject._validate_specific</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t187">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t187"><data value='update_statistics'>WritingProject.update_statistics</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t204">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t204"><data value='calculate_completion_percentage'>WritingProject.calculate_completion_percentage</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t213">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t213"><data value='update_completion_percentage'>WritingProject.update_completion_percentage</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t218">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t218"><data value='mark_opened'>WritingProject.mark_opened</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t223">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t223"><data value='add_writing_time'>WritingProject.add_writing_time</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t229">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t229"><data value='get_daily_progress'>WritingProject.get_daily_progress</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t245">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t245"><data value='validate'>ProjectValidator.validate</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>79</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="79 79">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t44">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t44"><data value='validate_name'>SceneLocation.validate_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t107">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t107"><data value='validate_tension_level'>SceneAtmosphere.validate_tension_level</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t148">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t148"><data value='init__'>Scene.__init__</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t158">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t158"><data value='add_character'>Scene.add_character</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t164">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t164"><data value='remove_character'>Scene.remove_character</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t170">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t170"><data value='add_event'>Scene.add_event</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t176">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t176"><data value='remove_event'>Scene.remove_event</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t182">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t182"><data value='use_in_chapter'>Scene.use_in_chapter</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t189">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t189"><data value='get_atmosphere_score'>Scene.get_atmosphere_score</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t215">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t215"><data value='validate_model'>Scene.validate_model</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t253">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t253"><data value='validate'>SceneValidator.validate</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t268">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t268"><data value='validate_scene_completeness'>SceneValidator._validate_scene_completeness</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t283">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t283"><data value='validate_scene_consistency'>SceneValidator._validate_scene_consistency</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t299">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t299"><data value='validate_scene_relationships'>SceneValidator._validate_scene_relationships</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t308">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t308"><data value='is_weather_season_consistent'>SceneValidator._is_weather_season_consistent</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t39">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t39"><data value='validate_strength'>ElementRelationship.validate_strength</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t45">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t45"><data value='validate_time_range'>ElementRelationship.validate_time_range</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t51">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t51"><data value='is_active'>ElementRelationship.is_active</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t83">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t83"><data value='validate_version_number'>ElementVersion.validate_version_number</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t117">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t117"><data value='validate_name'>StoryElement.validate_name</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t127">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t127"><data value='validate_appearance_count'>StoryElement.validate_appearance_count</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t132">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t132"><data value='validate_specific'>StoryElement._validate_specific</data></a></td>
                <td>13</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="6 13">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t157">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t157"><data value='add_relationship'>StoryElement.add_relationship</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t178">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t178"><data value='remove_relationship'>StoryElement.remove_relationship</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t204">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t204"><data value='get_relationships_by_type'>StoryElement.get_relationships_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t208">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t208"><data value='get_related_elements_by_type'>StoryElement.get_related_elements_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t212">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t212"><data value='has_relationship_with'>StoryElement.has_relationship_with</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t220">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t220"><data value='record_appearance'>StoryElement.record_appearance</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t254">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t254"><data value='get_appearance_in_chapter'>StoryElement.get_appearance_in_chapter</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t261">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t261"><data value='get_chapters_appeared'>StoryElement.get_chapters_appeared</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t265">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t265"><data value='calculate_importance_score'>StoryElement.calculate_importance_score</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t287">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t287"><data value='validate'>StoryElementValidator.validate</data></a></td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="12 17">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t318">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t318"><data value='validate_relationship'>StoryElementValidator._validate_relationship</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18___init___py.html">src\core\storage\__init__.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t32">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t32"><data value='init__'>BackupInfo.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t44">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t44"><data value='to_dict'>BackupInfo.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t58">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t58"><data value='from_dict'>BackupInfo.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t82">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t82"><data value='init__'>BackupManager.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t100">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t100"><data value='load_index'>BackupManager._load_index</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t115">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t115"><data value='save_index'>BackupManager._save_index</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t128">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t128"><data value='create_backup'>BackupManager.create_backup</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t188">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t188"><data value='restore_backup'>BackupManager.restore_backup</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t247">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t247"><data value='list_backups'>BackupManager.list_backups</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t266">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t266"><data value='delete_backup'>BackupManager.delete_backup</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t294">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t294"><data value='cleanup_old_backups'>BackupManager.cleanup_old_backups</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t332">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t332"><data value='verify_backup'>BackupManager._verify_backup</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t357">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t357"><data value='calculate_checksum'>BackupManager._calculate_checksum</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t365">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t365"><data value='update_project_id'>BackupManager._update_project_id</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t380">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t380"><data value='get_backup_stats'>BackupManager.get_backup_stats</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t32">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t32"><data value='init__'>CacheEntry.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t45">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t45"><data value='is_expired'>CacheEntry.is_expired</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t51">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t51"><data value='touch'>CacheEntry.touch</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t60">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t60"><data value='init__'>LRUCache.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t77">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t77"><data value='get'>LRUCache.get</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t101">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t101"><data value='set'>LRUCache.set</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t122">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t122"><data value='delete'>LRUCache.delete</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t130">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t130"><data value='clear'>LRUCache.clear</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t135">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t135"><data value='cleanup_expired'>LRUCache.cleanup_expired</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t148">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t148"><data value='get_stats'>LRUCache.get_stats</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t167">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t167"><data value='init__'>DiskCache.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t185">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t185"><data value='load_metadata'>DiskCache._load_metadata</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t196">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t196"><data value='save_metadata'>DiskCache._save_metadata</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t204">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t204"><data value='get_cache_path'>DiskCache._get_cache_path</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t210">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t210"><data value='get'>DiskCache.get</data></a></td>
                <td>20</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="18 20">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t243">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t243"><data value='set'>DiskCache.set</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t276">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t276"><data value='delete'>DiskCache.delete</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t293">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t293"><data value='clear'>DiskCache.clear</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t305">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t305"><data value='cleanup_if_needed'>DiskCache._cleanup_if_needed</data></a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t323">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t323"><data value='get_stats'>DiskCache.get_stats</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t343">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t343"><data value='init__'>CacheManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t352">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t352"><data value='get'>CacheManager.get</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t369">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t369"><data value='set'>CacheManager.set</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t382">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t382"><data value='delete'>CacheManager.delete</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t388">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t388"><data value='clear'>CacheManager.clear</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t393">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t393"><data value='cleanup'>CacheManager.cleanup</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t400">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t400"><data value='get_stats'>CacheManager.get_stats</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t38">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t38"><data value='init__'>FileStorage.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t50">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t50"><data value='create_project_structure'>FileStorage.create_project_structure</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t105">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t105"><data value='save_project'>FileStorage.save_project</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t121">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t121"><data value='load_project'>FileStorage.load_project</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t135">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t135"><data value='save_character'>FileStorage.save_character</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t141">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t141"><data value='load_character'>FileStorage.load_character</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t147">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t147"><data value='save_scene'>FileStorage.save_scene</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t153">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t153"><data value='load_scene'>FileStorage.load_scene</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t159">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t159"><data value='save_event'>FileStorage.save_event</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t165">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t165"><data value='load_event'>FileStorage.load_event</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t171">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t171"><data value='list_projects'>FileStorage.list_projects</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t195">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t195"><data value='list_characters'>FileStorage.list_characters</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t213">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t213"><data value='list_scenes'>FileStorage.list_scenes</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t231">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t231"><data value='list_events'>FileStorage.list_events</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t249">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t249"><data value='delete_project'>FileStorage.delete_project</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t260">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t260"><data value='delete_character'>FileStorage.delete_character</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t266">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t266"><data value='delete_scene'>FileStorage.delete_scene</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t272">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t272"><data value='delete_event'>FileStorage.delete_event</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t278">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t278"><data value='save_object'>FileStorage._save_object</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t293">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t293"><data value='load_object'>FileStorage._load_object</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t304">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t304"><data value='delete_file'>FileStorage._delete_file</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t314">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t314"><data value='atomic_write'>FileStorage._atomic_write</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t317">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t317"><data value='init__'>FileStorage._atomic_write.AtomicWrite.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t321">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t321"><data value='enter__'>FileStorage._atomic_write.AtomicWrite.__enter__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t327">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t327"><data value='exit__'>FileStorage._atomic_write.AtomicWrite.__exit__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t337">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t337"><data value='write_json_file'>FileStorage._write_json_file</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t345">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t345"><data value='read_json_file'>FileStorage._read_json_file</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t38">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t38"><data value='init__'>StorageManager.__init__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t88">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t88"><data value='create_project'>StorageManager.create_project</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t121">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t121"><data value='load_project'>StorageManager.load_project</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t144">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t144"><data value='save_project'>StorageManager.save_project</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t173">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t173"><data value='delete_project'>StorageManager.delete_project</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t194">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t194"><data value='save_character'>StorageManager.save_character</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t224">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t224"><data value='load_character'>StorageManager.load_character</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t247">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t247"><data value='delete_character'>StorageManager.delete_character</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t264">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t264"><data value='save_scene'>StorageManager.save_scene</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t294">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t294"><data value='load_scene'>StorageManager.load_scene</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t318">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t318"><data value='save_event'>StorageManager.save_event</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t348">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t348"><data value='load_event'>StorageManager.load_event</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t372">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t372"><data value='create_backup'>StorageManager.create_backup</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t379">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t379"><data value='restore_backup'>StorageManager.restore_backup</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t387">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t387"><data value='get_object_versions'>StorageManager.get_object_versions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t394">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t394"><data value='rollback_to_version'>StorageManager.rollback_to_version</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t402">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t402"><data value='get_storage_stats'>StorageManager.get_storage_stats</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t419">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t419"><data value='cleanup'>StorageManager.cleanup</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t48">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t48"><data value='init__'>JSONSerializer.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t76">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t76"><data value='serialize'>JSONSerializer.serialize</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t123">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t123"><data value='deserialize'>JSONSerializer.deserialize</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t171">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t171"><data value='to_serializable'>JSONSerializer._to_serializable</data></a></td>
                <td>21</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="20 21">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t213">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t213"><data value='from_serializable'>JSONSerializer._from_serializable</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t228">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t228"><data value='process_nested_data'>JSONSerializer._process_nested_data</data></a></td>
                <td>26</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="14 26">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t271">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t271"><data value='convert_field_value'>JSONSerializer._convert_field_value</data></a></td>
                <td>22</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="14 22">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t305">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t305"><data value='default_encoder'>JSONSerializer._default_encoder</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t313">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t313"><data value='get_class_by_name'>JSONSerializer._get_class_by_name</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t325">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t325"><data value='validate_json'>JSONSerializer.validate_json</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t333">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t333"><data value='get_metadata'>JSONSerializer.get_metadata</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t32">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t32"><data value='init__'>VersionInfo.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t46">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t46"><data value='to_dict'>VersionInfo.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t61">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t61"><data value='from_dict'>VersionInfo.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t86">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t86"><data value='init__'>VersionControlManager.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t102">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t102"><data value='load_index'>VersionControlManager._load_index</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t117">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t117"><data value='save_index'>VersionControlManager._save_index</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t130">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t130"><data value='create_version'>VersionControlManager.create_version</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t181">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t181"><data value='get_version'>VersionControlManager.get_version</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t200">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t200"><data value='get_object_versions'>VersionControlManager.get_object_versions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t219">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t219"><data value='get_latest_version'>VersionControlManager.get_latest_version</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t231">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t231"><data value='compare_versions'>VersionControlManager.compare_versions</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t288">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t288"><data value='rollback_to_version'>VersionControlManager.rollback_to_version</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t311">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t311"><data value='delete_version'>VersionControlManager.delete_version</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t338">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t338"><data value='cleanup_old_versions'>VersionControlManager.cleanup_old_versions</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t363">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t363"><data value='get_version_stats'>VersionControlManager.get_version_stats</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15___init___py.html">src\core\validation\__init__.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t25">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t25"><data value='validate'>ProjectBusinessRules.validate</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t50">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t50"><data value='get_rule_name'>ProjectBusinessRules.get_rule_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t53">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t53"><data value='validate_project_status_consistency'>ProjectBusinessRules._validate_project_status_consistency</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t71">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t71"><data value='validate_statistics_consistency'>ProjectBusinessRules._validate_statistics_consistency</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t90">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t90"><data value='validate_completion_reasonableness'>ProjectBusinessRules._validate_completion_reasonableness</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t102">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t102"><data value='validate_ai_configuration'>ProjectBusinessRules._validate_ai_configuration</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t134">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t134"><data value='validate'>CharacterBusinessRules.validate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t155">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t155"><data value='get_rule_name'>CharacterBusinessRules.get_rule_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t158">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t158"><data value='validate_character_completeness'>CharacterBusinessRules._validate_character_completeness</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t178">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t178"><data value='validate_relationship_reasonableness'>CharacterBusinessRules._validate_relationship_reasonableness</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t199">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t199"><data value='validate_character_development'>CharacterBusinessRules._validate_character_development</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t224">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t224"><data value='validate'>SceneBusinessRules.validate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t245">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t245"><data value='get_rule_name'>SceneBusinessRules.get_rule_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t248">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t248"><data value='validate_scene_completeness'>SceneBusinessRules._validate_scene_completeness</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t264">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t264"><data value='validate_scene_consistency'>SceneBusinessRules._validate_scene_consistency</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t279">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t279"><data value='validate_scene_usage'>SceneBusinessRules._validate_scene_usage</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t295">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t295"><data value='validate'>EventBusinessRules.validate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t316">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t316"><data value='get_rule_name'>EventBusinessRules.get_rule_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t319">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t319"><data value='validate_event_completeness'>EventBusinessRules._validate_event_completeness</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t342">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t342"><data value='validate_event_logic'>EventBusinessRules._validate_event_logic</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t357">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t357"><data value='validate_event_impact'>EventBusinessRules._validate_event_impact</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t376">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t376"><data value='validate'>StoryElementBusinessRules.validate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t393">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t393"><data value='get_rule_name'>StoryElementBusinessRules.get_rule_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t396">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t396"><data value='validate_element_completeness'>StoryElementBusinessRules._validate_element_completeness</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t409">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t409"><data value='validate_element_relationships'>StoryElementBusinessRules._validate_element_relationships</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t20">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t20"><data value='init__'>ValidationIssue.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t34">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t34"><data value='str__'>ValidationIssue.__str__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t43">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t43"><data value='to_dict'>ValidationIssue.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t57">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t57"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t70">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t70"><data value='init__'>ValidationWarning.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t83">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t83"><data value='init__'>ValidationInfo.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t96">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t96"><data value='init__'>ValidationException.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t107">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t107"><data value='has_errors'>ValidationException.has_errors</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t112">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t112"><data value='has_warnings'>ValidationException.has_warnings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t117">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t117"><data value='has_infos'>ValidationException.has_infos</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t121">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t121"><data value='get_summary'>ValidationException.get_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t135">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t135"><data value='init__'>BusinessRuleViolation.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t144">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t144"><data value='init__'>RelationshipValidationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t153">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t153"><data value='init__'>FieldValidationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t22">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t22"><data value='validate_relationships'>CharacterRelationshipValidator.validate_relationships</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t43">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t43"><data value='get_validator_name'>CharacterRelationshipValidator.get_validator_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t46">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t46"><data value='validate_bidirectional_consistency'>CharacterRelationshipValidator._validate_bidirectional_consistency</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t78">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t78"><data value='validate_relationship_logic'>CharacterRelationshipValidator._validate_relationship_logic</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t98">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t98"><data value='validate_relationship_network'>CharacterRelationshipValidator._validate_relationship_network</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t110">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t110"><data value='validate_relationships'>SceneEventRelationshipValidator.validate_relationships</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t122">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t122"><data value='get_validator_name'>SceneEventRelationshipValidator.get_validator_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t125">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t125"><data value='validate_scene_relationships'>SceneEventRelationshipValidator._validate_scene_relationships</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t145">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t145"><data value='validate_event_relationships'>SceneEventRelationshipValidator._validate_event_relationships</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t170">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t170"><data value='validate_relationships'>ProjectElementRelationshipValidator.validate_relationships</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t183">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t183"><data value='get_validator_name'>ProjectElementRelationshipValidator.get_validator_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t186">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t186"><data value='validate_project_statistics'>ProjectElementRelationshipValidator._validate_project_statistics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t213">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t213"><data value='validate_relationships'>TimelineConsistencyValidator.validate_relationships</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t228">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t228"><data value='get_validator_name'>TimelineConsistencyValidator.get_validator_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t231">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t231"><data value='validate_event_timeline'>TimelineConsistencyValidator._validate_event_timeline</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t260">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t260"><data value='validate_relationships'>PlotConsistencyValidator.validate_relationships</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t274">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t274"><data value='get_validator_name'>PlotConsistencyValidator.get_validator_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t277">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t277"><data value='validate_plot_development'>PlotConsistencyValidator._validate_plot_development</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t20">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t20"><data value='init__'>StringValidationRule.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t40">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t40"><data value='validate'>StringValidationRule.validate</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t82">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t82"><data value='get_error_message'>StringValidationRule.get_error_message</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t108">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t108"><data value='init__'>NumericValidationRule.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t124">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t124"><data value='validate'>NumericValidationRule.validate</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t157">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t157"><data value='get_error_message'>NumericValidationRule.get_error_message</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t188">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t188"><data value='init__'>DateTimeValidationRule.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t202">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t202"><data value='validate'>DateTimeValidationRule.validate</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t227">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t227"><data value='get_error_message'>DateTimeValidationRule.get_error_message</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t251">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t251"><data value='init__'>ListValidationRule.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t265">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t265"><data value='validate'>ListValidationRule.validate</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t291">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t291"><data value='get_error_message'>ListValidationRule.get_error_message</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t310">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t310"><data value='init__'>EnumValidationRule.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t314">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t314"><data value='validate'>EnumValidationRule.validate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t329">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t329"><data value='get_error_message'>EnumValidationRule.get_error_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t337">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t337"><data value='init__'>UUIDValidationRule.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t340">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t340"><data value='validate'>UUIDValidationRule.validate</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t356">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t356"><data value='get_error_message'>UUIDValidationRule.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t363">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t363"><data value='init__'>EmailValidationRule.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t369">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t369"><data value='validate'>EmailValidationRule.validate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t378">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t378"><data value='get_error_message'>EmailValidationRule.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t385">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t385"><data value='init__'>URLValidationRule.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t389">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t389"><data value='validate'>URLValidationRule.validate</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t407">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t407"><data value='get_error_message'>URLValidationRule.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t414">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t414"><data value='init__'>FilePathValidationRule.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t424">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t424"><data value='validate'>FilePathValidationRule.validate</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t445">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t445"><data value='get_error_message'>FilePathValidationRule.get_error_message</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t460">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t460"><data value='init__'>CustomValidationRule.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t470">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t470"><data value='validate'>CustomValidationRule.validate</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t476">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t476"><data value='get_error_message'>CustomValidationRule.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t24">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t24"><data value='init__'>ValidationContext.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t37">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t37"><data value='add_error'>ValidationContext.add_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t41">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t41"><data value='add_warning'>ValidationContext.add_warning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t45">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t45"><data value='add_info'>ValidationContext.add_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t49">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t49"><data value='get_related_models'>ValidationContext.get_related_models</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t53">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t53"><data value='has_errors'>ValidationContext.has_errors</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t57">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t57"><data value='get_validation_result'>ValidationContext.get_validation_result</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t71">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t71"><data value='init__'>ValidationRule.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t76">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t76"><data value='validate'>ValidationRule.validate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t88">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t88"><data value='get_error_message'>ValidationRule.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t99">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t99"><data value='init__'>FieldValidator.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t102">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t102"><data value='add_rule'>FieldValidator.add_rule</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t108">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t108"><data value='validate_field'>FieldValidator.validate_field</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t133">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t133"><data value='validate_model'>FieldValidator.validate_model</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t151">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t151"><data value='validate'>BusinessRuleValidator.validate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t156">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t156"><data value='get_rule_name'>BusinessRuleValidator.get_rule_name</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t165">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t165"><data value='validate_relationships'>RelationshipValidator.validate_relationships</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t170">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t170"><data value='get_validator_name'>RelationshipValidator.get_validator_name</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t181">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t181"><data value='init__'>ValidationEngine.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t186">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t186"><data value='register_field_validator'>ValidationEngine.register_field_validator</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t190">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t190"><data value='register_business_rule_validator'>ValidationEngine.register_business_rule_validator</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t196">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t196"><data value='register_relationship_validator'>ValidationEngine.register_relationship_validator</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t200">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t200"><data value='validate'>ValidationEngine.validate</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t238">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t238"><data value='validate_batch'>ValidationEngine.validate_batch</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t252">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t252"><data value='get_validation_summary'>ValidationEngine.get_validation_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t22">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t22"><data value='setup_application'>setup_application</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t40">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t40"><data value='setup_internationalization'>setup_internationalization</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t54">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t54"><data value='main'>main</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t77">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t77"><data value='show_main_window'>main.show_main_window</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>2</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t31">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t31"><data value='init__'>AIGenerationThread.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t37">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t37"><data value='run'>AIGenerationThread.run</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t63">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t63"><data value='init__'>ChatWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t67">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t67"><data value='setup_ui'>ChatWidget.setup_ui</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t128">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t128"><data value='send_message'>ChatWidget.send_message</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t136">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t136"><data value='add_user_message'>ChatWidget.add_user_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t141">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t141"><data value='add_ai_message'>ChatWidget.add_ai_message</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t146">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t146"><data value='add_system_message'>ChatWidget.add_system_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t157">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t157"><data value='init__'>ContentGeneratorWidget.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t166">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t166"><data value='setup_ui'>ContentGeneratorWidget.setup_ui</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t287">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t287"><data value='setup_connections'>ContentGeneratorWidget.setup_connections</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t301">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t301"><data value='generate_content'>ContentGeneratorWidget.generate_content</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t343">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t343"><data value='on_generation_finished'>ContentGeneratorWidget.on_generation_finished</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t353">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t353"><data value='on_generation_error'>ContentGeneratorWidget.on_generation_error</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t361">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t361"><data value='on_generation_progress'>ContentGeneratorWidget.on_generation_progress</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t365">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t365"><data value='copy_result'>ContentGeneratorWidget.copy_result</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t374">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t374"><data value='insert_result'>ContentGeneratorWidget.insert_result</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t389">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t389"><data value='init__'>AIAssistantWidget.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t397">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t397"><data value='setup_ui'>AIAssistantWidget.setup_ui</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t414">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t414"><data value='setup_connections'>AIAssistantWidget.setup_connections</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t419">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t419"><data value='handle_chat_message'>AIAssistantWidget.handle_chat_message</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t434">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t434"><data value='get_current_context'>AIAssistantWidget.get_current_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t27">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t27"><data value='init__'>CharacterEditDialog.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t36">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t36"><data value='setup_ui'>CharacterEditDialog.setup_ui</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t74">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t74"><data value='create_basic_info_tab'>CharacterEditDialog.create_basic_info_tab</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t119">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t119"><data value='create_appearance_tab'>CharacterEditDialog.create_appearance_tab</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t164">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t164"><data value='create_personality_tab'>CharacterEditDialog.create_personality_tab</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t223">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t223"><data value='create_background_tab'>CharacterEditDialog.create_background_tab</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t265">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t265"><data value='load_character_data'>CharacterEditDialog.load_character_data</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t315">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t315"><data value='get_character_data'>CharacterEditDialog.get_character_data</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t363">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t363"><data value='init__'>CharacterListWidget.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t368">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t368"><data value='setup_ui'>CharacterListWidget.setup_ui</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t398">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t398"><data value='add_character'>CharacterListWidget.add_character</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t408">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t408"><data value='remove_character'>CharacterListWidget.remove_character</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t419">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t419"><data value='update_character'>CharacterListWidget.update_character</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t428">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t428"><data value='on_item_clicked'>CharacterListWidget.on_item_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t434">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t434"><data value='on_item_double_clicked'>CharacterListWidget.on_item_double_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t444">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t444"><data value='init__'>CharacterManagerWidget.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t453">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t453"><data value='setup_ui'>CharacterManagerWidget.setup_ui</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t502">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t502"><data value='setup_connections'>CharacterManagerWidget.setup_connections</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t512">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t512"><data value='load_characters'>CharacterManagerWidget.load_characters</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t517">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t517"><data value='add_character'>CharacterManagerWidget.add_character</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t544">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t544"><data value='edit_character'>CharacterManagerWidget.edit_character</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t549">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t549"><data value='edit_character_from_list'>CharacterManagerWidget.edit_character_from_list</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t564">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t564"><data value='delete_character'>CharacterManagerWidget.delete_character</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t581">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t581"><data value='duplicate_character'>CharacterManagerWidget.duplicate_character</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t589">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t589"><data value='show_character_detail'>CharacterManagerWidget.show_character_detail</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t26">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t26"><data value='init__'>EventEditDialog.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t35">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t35"><data value='setup_ui'>EventEditDialog.setup_ui</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t69">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t69"><data value='create_basic_info_tab'>EventEditDialog.create_basic_info_tab</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t123">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t123"><data value='create_detail_tab'>EventEditDialog.create_detail_tab</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t165">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t165"><data value='create_impact_tab'>EventEditDialog.create_impact_tab</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t214">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t214"><data value='load_event_data'>EventEditDialog.load_event_data</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t266">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t266"><data value='get_event_data'>EventEditDialog.get_event_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t305">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t305"><data value='init__'>EventListWidget.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t310">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t310"><data value='setup_ui'>EventListWidget.setup_ui</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t340">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t340"><data value='add_event'>EventListWidget.add_event</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t350">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t350"><data value='remove_event'>EventListWidget.remove_event</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t361">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t361"><data value='update_event'>EventListWidget.update_event</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t370">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t370"><data value='on_item_clicked'>EventListWidget.on_item_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t376">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t376"><data value='on_item_double_clicked'>EventListWidget.on_item_double_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t386">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t386"><data value='init__'>EventManagerWidget.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t395">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t395"><data value='setup_ui'>EventManagerWidget.setup_ui</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t444">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t444"><data value='setup_connections'>EventManagerWidget.setup_connections</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t454">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t454"><data value='load_events'>EventManagerWidget.load_events</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t459">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t459"><data value='add_event'>EventManagerWidget.add_event</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t487">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t487"><data value='edit_event'>EventManagerWidget.edit_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t492">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t492"><data value='edit_event_from_list'>EventManagerWidget.edit_event_from_list</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t507">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t507"><data value='delete_event'>EventManagerWidget.delete_event</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t524">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t524"><data value='duplicate_event'>EventManagerWidget.duplicate_event</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t532">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t532"><data value='show_event_detail'>EventManagerWidget.show_event_detail</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t34">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t34"><data value='init__'>WelcomeWidget.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t42">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t42"><data value='setup_ui'>WelcomeWidget.setup_ui</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t70">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t70"><data value='create_title_section'>WelcomeWidget.create_title_section</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t90">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t90"><data value='create_action_panel'>WelcomeWidget.create_action_panel</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t117">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t117"><data value='create_recent_projects_panel'>WelcomeWidget.create_recent_projects_panel</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t140">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t140"><data value='create_bottom_section'>WelcomeWidget.create_bottom_section</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t165">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t165"><data value='apply_styles'>WelcomeWidget.apply_styles</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t257">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t257"><data value='load_recent_projects'>WelcomeWidget.load_recent_projects</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t276">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t276"><data value='on_project_double_clicked'>WelcomeWidget.on_project_double_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t282">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t282"><data value='show_about'>WelcomeWidget.show_about</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t297">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t297"><data value='init__'>MainWindow.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t308">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t308"><data value='setup_ui'>MainWindow.setup_ui</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t328">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t328"><data value='setup_connections'>MainWindow.setup_connections</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t335">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t335"><data value='show_new_project_dialog'>MainWindow.show_new_project_dialog</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t345">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t345"><data value='show_open_project_dialog'>MainWindow.show_open_project_dialog</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t360">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t360"><data value='show_settings_dialog'>MainWindow.show_settings_dialog</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t368">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t368"><data value='create_new_project'>MainWindow.create_new_project</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t390">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t390"><data value='open_project'>MainWindow.open_project</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t404">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t404"><data value='open_writing_interface'>MainWindow.open_writing_interface</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t423">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t423"><data value='closeEvent'>MainWindow.closeEvent</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t451">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t451"><data value='show_splash_screen'>show_splash_screen</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>7</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t23">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t23"><data value='init__'>ProjectDialog.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t29">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t29"><data value='setup_ui'>ProjectDialog.setup_ui</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t60">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t60"><data value='create_project_info_group'>ProjectDialog.create_project_info_group</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t89">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t89"><data value='create_template_group'>ProjectDialog.create_template_group</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t134">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t134"><data value='create_advanced_group'>ProjectDialog.create_advanced_group</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t165">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t165"><data value='create_button_layout'>ProjectDialog.create_button_layout</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t184">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t184"><data value='populate_genre_combo'>ProjectDialog.populate_genre_combo</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t189">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t189"><data value='setup_connections'>ProjectDialog.setup_connections</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t203">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t203"><data value='on_template_changed'>ProjectDialog.on_template_changed</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t209">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t209"><data value='select_custom_template'>ProjectDialog.select_custom_template</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t222">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t222"><data value='validate_form'>ProjectDialog.validate_form</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t229">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t229"><data value='validate_and_accept'>ProjectDialog.validate_and_accept</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t248">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t248"><data value='collect_project_data'>ProjectDialog.collect_project_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t263">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t263"><data value='get_project_data'>ProjectDialog.get_project_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t267">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t267"><data value='apply_styles'>ProjectDialog.apply_styles</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t376">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t376"><data value='init__'>ProjectSettingsDialog.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t382">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t382"><data value='setup_ui'>ProjectSettingsDialog.setup_ui</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t441">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t441"><data value='load_project_settings'>ProjectSettingsDialog.load_project_settings</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t448">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t448"><data value='save_settings'>ProjectSettingsDialog.save_settings</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t26">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t26"><data value='init__'>SceneEditDialog.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t35">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t35"><data value='setup_ui'>SceneEditDialog.setup_ui</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t69">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t69"><data value='create_basic_info_tab'>SceneEditDialog.create_basic_info_tab</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t121">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t121"><data value='create_environment_tab'>SceneEditDialog.create_environment_tab</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t178">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t178"><data value='create_atmosphere_tab'>SceneEditDialog.create_atmosphere_tab</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t214">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t214"><data value='load_scene_data'>SceneEditDialog.load_scene_data</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t277">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t277"><data value='get_scene_data'>SceneEditDialog.get_scene_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t318">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t318"><data value='init__'>SceneListWidget.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t323">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t323"><data value='setup_ui'>SceneListWidget.setup_ui</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t353">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t353"><data value='add_scene'>SceneListWidget.add_scene</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t363">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t363"><data value='remove_scene'>SceneListWidget.remove_scene</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t374">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t374"><data value='update_scene'>SceneListWidget.update_scene</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t383">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t383"><data value='on_item_clicked'>SceneListWidget.on_item_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t389">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t389"><data value='on_item_double_clicked'>SceneListWidget.on_item_double_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t399">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t399"><data value='init__'>SceneManagerWidget.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t408">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t408"><data value='setup_ui'>SceneManagerWidget.setup_ui</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t457">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t457"><data value='setup_connections'>SceneManagerWidget.setup_connections</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t467">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t467"><data value='load_scenes'>SceneManagerWidget.load_scenes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t472">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t472"><data value='add_scene'>SceneManagerWidget.add_scene</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t499">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t499"><data value='edit_scene'>SceneManagerWidget.edit_scene</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t504">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t504"><data value='edit_scene_from_list'>SceneManagerWidget.edit_scene_from_list</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t519">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t519"><data value='delete_scene'>SceneManagerWidget.delete_scene</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t536">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t536"><data value='duplicate_scene'>SceneManagerWidget.duplicate_scene</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t544">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t544"><data value='show_scene_detail'>SceneManagerWidget.show_scene_detail</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t24">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t24"><data value='init__'>GeneralSettingsWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t28">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t28"><data value='setup_ui'>GeneralSettingsWidget.setup_ui</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t118">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t118"><data value='select_font'>GeneralSettingsWidget.select_font</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t125">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t125"><data value='select_save_path'>GeneralSettingsWidget.select_save_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t135">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t135"><data value='init__'>AISettingsWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t139">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t139"><data value='setup_ui'>AISettingsWidget.setup_ui</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t190">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t190"><data value='create_openai_tab'>AISettingsWidget.create_openai_tab</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t221">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t221"><data value='create_deepseek_tab'>AISettingsWidget.create_deepseek_tab</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t251">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t251"><data value='create_zhipu_tab'>AISettingsWidget.create_zhipu_tab</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t281">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t281"><data value='create_anthropic_tab'>AISettingsWidget.create_anthropic_tab</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t312">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t312"><data value='test_connection'>AISettingsWidget.test_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t325">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t325"><data value='init__'>TemplateSettingsWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t329">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t329"><data value='setup_ui'>TemplateSettingsWidget.setup_ui</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t399">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t399"><data value='init__'>SettingsDialog.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t404">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t404"><data value='setup_ui'>SettingsDialog.setup_ui</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t480">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t480"><data value='setup_connections'>SettingsDialog.setup_connections</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t488">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t488"><data value='load_settings'>SettingsDialog.load_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t493">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t493"><data value='save_settings'>SettingsDialog.save_settings</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t508">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t508"><data value='collect_settings_data'>SettingsDialog.collect_settings_data</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t554">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t554"><data value='apply_settings'>SettingsDialog.apply_settings</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t559">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t559"><data value='save_and_close'>SettingsDialog.save_and_close</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t36">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t36"><data value='init__'>RichTextEditor.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t41">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t41"><data value='setup_editor'>RichTextEditor.setup_editor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t62">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t62"><data value='setup_connections'>RichTextEditor.setup_connections</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t66">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t66"><data value='on_text_changed'>RichTextEditor.on_text_changed</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t74">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t74"><data value='insert_text'>RichTextEditor.insert_text</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t79">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t79"><data value='get_selected_text'>RichTextEditor.get_selected_text</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t83">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t83"><data value='replace_selected_text'>RichTextEditor.replace_selected_text</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t97">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t97"><data value='init__'>ChapterTreeWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t101">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t101"><data value='setup_tree'>ChapterTreeWidget.setup_tree</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t134">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t134"><data value='add_chapter'>ChapterTreeWidget.add_chapter</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t147">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t147"><data value='on_item_clicked'>ChapterTreeWidget.on_item_clicked</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t157">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t157"><data value='init__'>WritingStatsWidget.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t161">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t161"><data value='setup_ui'>WritingStatsWidget.setup_ui</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t195">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t195"><data value='update_stats'>WritingStatsWidget.update_stats</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t210">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t210"><data value='init__'>WritingInterface.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t220">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t220"><data value='setup_ui'>WritingInterface.setup_ui</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t253">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t253"><data value='create_toolbar'>WritingInterface.create_toolbar</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t284">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t284"><data value='create_left_panel'>WritingInterface.create_left_panel</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t309">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t309"><data value='create_central_widget'>WritingInterface.create_central_widget</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t326">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t326"><data value='create_right_panel'>WritingInterface.create_right_panel</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t352">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t352"><data value='create_status_bar'>WritingInterface.create_status_bar</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t386">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t386"><data value='setup_connections'>WritingInterface.setup_connections</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t398">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t398"><data value='setup_auto_save'>WritingInterface.setup_auto_save</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t403">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t403"><data value='add_chapter'>WritingInterface.add_chapter</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t412">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t412"><data value='delete_chapter'>WritingInterface.delete_chapter</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t427">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t427"><data value='load_chapter'>WritingInterface.load_chapter</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t440">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t440"><data value='save_project'>WritingInterface.save_project</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t449">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t449"><data value='auto_save'>WritingInterface.auto_save</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t458">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t458"><data value='update_word_count'>WritingInterface.update_word_count</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t467">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t467"><data value='on_content_changed'>WritingInterface.on_content_changed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t472">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t472"><data value='insert_generated_text'>WritingInterface.insert_generated_text</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t476">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t476"><data value='show_ai_assistant'>WritingInterface.show_ai_assistant</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>5973</td>
                <td>3655</td>
                <td>75</td>
                <td class="right" data-ratio="2318 5973">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
