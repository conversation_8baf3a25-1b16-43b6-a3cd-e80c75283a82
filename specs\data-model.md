# 数据模型设计: 笔落App

## 1. 核心实体关系

```
WritingProject (1) ── (1) StoryOutline
WritingProject (1) ── (1..*) StoryArc
WritingProject (1) ── (1..*) StoryChapter
WritingProject (1) ── (0..*) StoryCharacter
WritingProject (1) ── (0..*) StoryScene  
WritingProject (1) ── (0..*) StoryEvent
WritingProject (1) ── (1) AIConfiguration
```

## 2. 详细实体定义

### 2.1 WritingProject (小说项目)
**用途**: 代表一个完整的小说创作项目

**字段**:
- `id`: UUID (主键)
- `name`: string (项目名称)
- `author`: string (作者姓名)
- `project_type`: enum (小说类型: 玄幻、都市、科幻等)
- `description`: string (项目简介)
- `cover_image`: string (封面图片路径)
- `created_at`: datetime (创建时间)
- `updated_at`: datetime (最后修改时间)
- `version`: string (项目版本号)
- `settings`: dict (项目设置)

**验证规则**:
- 名称不能为空，最大长度100字符
- 作者不能为空，最大长度50字符
- 类型必须来自预设列表
- 版本号符合语义化版本规范

### 2.2 StoryOutline (故事大纲)
**用途**: 管理故事的整体结构

**字段**:
- `introduction`: string (开场介绍)
- `development`: string (发展部分)
- `climax`: string (高潮部分) 
- `ending`: string (结局部分)
- `theme`: string (核心主题)
- `target_audience`: string (目标读者)
- `word_count_goal`: int (目标字数)
- `milestones`: list (里程碑计划)

**关联**:
- 属于一个WritingProject
- 关联主要角色和关键事件

### 2.3 StoryArc (故事主线)
**用途**: 管理分卷和主要情节线

**字段**:
- `arc_id`: UUID
- `title`: string (卷名)
- `order`: int (卷序)
- `description`: string (卷简介)
- `main_plot`: string (主要情节)
- `sub_plots`: list (支线情节)
- `tension_curve`: dict (紧张度曲线)
- `emotional_arc`: dict (情感变化弧线)

**关联**:
- 属于一个WritingProject
- 包含多个StoryChapter

### 2.4 StoryChapter (故事章节)
**用途**: 管理具体章节内容

**字段**:
- `chapter_id`: UUID
- `title`: string (章节标题)
- `order`: int (章节序号)
- `content`: string (章节内容)
- `word_count`: int (字数统计)
- `created_at`: datetime (创作时间)
- `modified_at`: datetime (修改时间)
- `ai_assisted`: bool (是否AI辅助生成)
- `format_styles`: dict (格式样式)

**关联**:
- 属于一个StoryArc
- 关联出现的角色、场景、事件

### 2.5 StoryCharacter (故事角色)
**用途**: 管理小说角色信息

**字段**:
- `character_id`: UUID
- `name`: string (角色姓名)
- `gender`: enum (性别: 男/女/其他)
- `age`: int (年龄)
- `appearance`: string (外貌描述)
- `personality`: dict (性格特质)
- `background`: string (背景故事)
- `skills`: list (技能能力)
- `weaknesses`: list (弱点缺点)
- `relationships`: dict (关系网络)
- `development_arc`: string (成长轨迹)

**验证规则**:
- 姓名不能为空
- 性格特质必须包含核心动机和恐惧

### 2.6 StoryScene (故事场景)
**用途**: 管理事件发生的场景环境

**字段**:
- `scene_id`: UUID
- `name`: string (场景名称)
- `location`: string (地点)
- `time_period`: string (时间背景)
- `atmosphere`: string (氛围描述)
- `sensory_details`: dict (感官细节)
- `importance`: enum (重要性: 主要/次要)
- `recurring`: bool (是否重复使用)
- `emotional_tone`: string (情绪基调)

### 2.7 StoryEvent (故事事件)
**用途**: 管理推动情节的事件

**字段**:
- `event_id`: UUID
- `description`: string (事件描述)
- `event_type`: enum (事件类型: 转折/冲突/解决/启示)
- `impact_scope`: dict (影响范围)
- `timeline_position`: float (时间线位置)
- `causes`: list (事件原因)
- `consequences`: list (事件后果)

### 2.8 AIConfiguration (AI配置)
**用途**: 管理AI服务配置

**字段**:
- `provider`: enum (服务商: OpenAI/DeepSeek/智谱等)
- `api_key`: string (加密存储)
- `model_name`: string (模型名称)
- `temperature`: float (温度参数)
- `max_tokens`: int (最大token数)
- `usage_stats`: dict (使用统计)
- `enabled`: bool (是否启用)

## 3. 数据存储格式

### 3.1 项目文件结构
```
project_name/
├── project.json          # 项目元数据
├── outline.json          # 故事大纲
├── arcs/                 # 主线分卷
│   ├── arc_1.json
│   └── arc_2.json
├── chapters/             # 章节内容
│   ├── chapter_1.json
│   ├── chapter_2.json
│   └── content/          # 大型文本内容
│       ├── chapter_1_content.txt
│       └── chapter_2_content.txt
├── characters/           # 角色数据
│   ├── character_1.json
│   └── character_2.json
├── scenes/              # 场景数据
│   ├── scene_1.json
│   └── scene_2.json
├── events/              # 事件数据
│   ├── event_1.json
│   └── event_2.json
└── ai_config.json       # AI配置
```

### 3.2 JSON序列化规范
- 使用UTF-8编码
- 缩进为2个空格
- 日期时间使用ISO格式
- 枚举值使用字符串表示
- 避免循环引用

## 4. 数据验证规则

### 4.1 业务规则
- 项目名称唯一性检查
- 章节序号连续性验证
- 角色名称唯一性约束
- 事件时间线顺序验证

### 4.2 完整性检查
- 必需字段非空验证
- 关联关系有效性检查
- 数据格式合规性检查

## 5. 数据迁移策略

### 5.1 版本兼容性
- 向前兼容至少2个主要版本
- 自动数据迁移工具
- 手动迁移指导文档

### 5.2 备份恢复
- 自动定期备份
- 手动备份导出
- 增量备份支持
- 损坏数据恢复机制