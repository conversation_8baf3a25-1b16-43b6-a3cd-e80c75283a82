<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b___init___py.html">src\core\ai\__init__.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060___init___py.html">src\core\ai\adapters\__init__.py</a></td>
                <td class="name left"><a href="z_708c621716977060___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t28">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html#t28"><data value='AnthropicAdapter'>AnthropicAdapter</data></a></td>
                <td>78</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="22 78">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t12">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html#t12"><data value='DeepSeekAdapter'>DeepSeekAdapter</data></a></td>
                <td>20</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="10 20">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t27">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html#t27"><data value='OpenAIAdapter'>OpenAIAdapter</data></a></td>
                <td>80</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="51 80">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html">src\core\ai\adapters\openai_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t12">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html#t12"><data value='ZhipuAdapter'>ZhipuAdapter</data></a></td>
                <td>20</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="11 20">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t17">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t17"><data value='AIProvider'>AIProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t25">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t25"><data value='GenerationType'>GenerationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t37">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t37"><data value='GenerationContext'>GenerationContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t50">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t50"><data value='GenerationOptions'>GenerationOptions</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t63">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t63"><data value='GenerationResult'>GenerationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t79">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t79"><data value='StreamChunk'>StreamChunk</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t88">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t88"><data value='AIServiceError'>AIServiceError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t96">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t96"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t101">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t101"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t108">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t108"><data value='QuotaExceededError'>QuotaExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t113">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t113"><data value='ModelNotFoundError'>ModelNotFoundError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t118">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t118"><data value='ContentFilterError'>ContentFilterError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t123">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t123"><data value='AIService'>AIService</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>12</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t181">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t181"><data value='PromptTemplate'>PromptTemplate</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t199">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html#t199"><data value='PromptManager'>PromptManager</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html">src\core\ai\base.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>84</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="84 84">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t20">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t20"><data value='AIServiceManager'>AIServiceManager</data></a></td>
                <td>96</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="69 96">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t231">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html#t231"><data value='ContentGenerator'>ContentGenerator</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html">src\core\ai\manager.py</a></td>
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de954926f2f9d31f___init___py.html">src\core\events\__init__.py</a></td>
                <td class="name left"><a href="z_de954926f2f9d31f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259___init___py.html">src\core\models\__init__.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t25">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t25"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t58">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t58"><data value='BaseModel'>BaseModel</data></a></td>
                <td>40</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="36 40">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t78">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t78"><data value='Config'>BaseModel.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t192">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t192"><data value='ModelValidator'>ModelValidator</data></a></td>
                <td>23</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t257">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html#t257"><data value='VersionedModel'>VersionedModel</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html">src\core\models\base.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="66 66">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t22">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t22"><data value='CharacterAppearance'>CharacterAppearance</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t55">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t55"><data value='CharacterPersonality'>CharacterPersonality</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t87">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t87"><data value='CharacterBackground'>CharacterBackground</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t126">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t126"><data value='CharacterRelationship'>CharacterRelationship</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t168">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t168"><data value='Character'>Character</data></a></td>
                <td>68</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="63 68">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t360">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html#t360"><data value='CharacterValidator'>CharacterValidator</data></a></td>
                <td>35</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="20 35">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html">src\core\models\character.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>116</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="116 116">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t15">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t15"><data value='ProjectType'>ProjectType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t30">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t30"><data value='ProjectGenre'>ProjectGenre</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t51">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t51"><data value='ProjectStatus'>ProjectStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t63">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t63"><data value='ElementType'>ElementType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t73">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t73"><data value='ImportanceLevel'>ImportanceLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t81">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t81"><data value='Gender'>Gender</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t89">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t89"><data value='CharacterRole'>CharacterRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t103">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t103"><data value='PersonalityTrait'>PersonalityTrait</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t144">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t144"><data value='RelationType'>RelationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t164">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t164"><data value='RelationshipStatus'>RelationshipStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t173">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t173"><data value='LocationType'>LocationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t184">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t184"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t194">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t194"><data value='EventCategory'>EventCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t203">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t203"><data value='ParticipationRole'>ParticipationRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t213">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t213"><data value='PlotFunction'>PlotFunction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t222">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t222"><data value='ChapterStatus'>ChapterStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t231">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t231"><data value='ReviewStatus'>ReviewStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t240">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t240"><data value='ElementRole'>ElementRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t248">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t248"><data value='ChangeType'>ChangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t257">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t257"><data value='SceneFunction'>SceneFunction</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t266">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t266"><data value='EventImpact'>EventImpact</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t275">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t275"><data value='CharacterArcType'>CharacterArcType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t285">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t285"><data value='TimeOfDay'>TimeOfDay</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t296">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t296"><data value='Season'>Season</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t304">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t304"><data value='Weather'>Weather</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t315">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t315"><data value='SceneType'>SceneType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t329">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t329"><data value='Atmosphere'>Atmosphere</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t345">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t345"><data value='EventImportance'>EventImportance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t355">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html#t355"><data value='AIProvider'>AIProvider</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html">src\core\models\enums.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>257</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="257 257">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t24">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t24"><data value='EventParticipation'>EventParticipation</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t48">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t48"><data value='EventImpactData'>EventImpactData</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t84">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t84"><data value='Event'>Event</data></a></td>
                <td>78</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="62 78">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t284">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html#t284"><data value='EventValidator'>EventValidator</data></a></td>
                <td>35</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="22 35">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html">src\core\models\event.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t20">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t20"><data value='ProjectSettings'>ProjectSettings</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t56">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t56"><data value='AIConfiguration'>AIConfiguration</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t101">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t101"><data value='WritingProject'>WritingProject</data></a></td>
                <td>53</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="44 53">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t242">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html#t242"><data value='ProjectValidator'>ProjectValidator</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html">src\core\models\project.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="79 79">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t25">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t25"><data value='SceneLocation'>SceneLocation</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t50">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t50"><data value='SceneTime'>SceneTime</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t65">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t65"><data value='SceneEnvironment'>SceneEnvironment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t90">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t90"><data value='SceneAtmosphere'>SceneAtmosphere</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t113">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t113"><data value='Scene'>Scene</data></a></td>
                <td>52</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="45 52">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t250">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html#t250"><data value='SceneValidator'>SceneValidator</data></a></td>
                <td>28</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="16 28">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html">src\core\models\scene.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t19">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t19"><data value='ElementRelationship'>ElementRelationship</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t65">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t65"><data value='ElementVersion'>ElementVersion</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t89">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t89"><data value='StoryElement'>StoryElement</data></a></td>
                <td>66</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="13 66">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t284">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html#t284"><data value='StoryElementValidator'>StoryElementValidator</data></a></td>
                <td>25</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="12 25">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html">src\core\models\story_element.py</a></td>
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18___init___py.html">src\core\storage\__init__.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t24">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t24"><data value='BackupError'>BackupError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t29">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t29"><data value='BackupInfo'>BackupInfo</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t72">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html#t72"><data value='BackupManager'>BackupManager</data></a></td>
                <td>135</td>
                <td>135</td>
                <td>0</td>
                <td class="right" data-ratio="0 135">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html">src\core\storage\backup.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t24">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t24"><data value='CacheError'>CacheError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t29">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t29"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t57">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t57"><data value='LRUCache'>LRUCache</data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t164">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t164"><data value='DiskCache'>DiskCache</data></a></td>
                <td>82</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="61 82">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t337">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html#t337"><data value='CacheManager'>CacheManager</data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html">src\core\storage\cache.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t20">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t20"><data value='FileStorageError'>FileStorageError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t28">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t28"><data value='FileStorage'>FileStorage</data></a></td>
                <td>134</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="110 134">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t316">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html#t316"><data value='AtomicWrite'>FileStorage._atomic_write.AtomicWrite</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html">src\core\storage\file_storage.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t27">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t27"><data value='StorageError'>StorageError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t32">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html#t32"><data value='StorageManager'>StorageManager</data></a></td>
                <td>192</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="0 192">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html">src\core\storage\manager.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t28">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t28"><data value='SerializationError'>SerializationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t33">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t33"><data value='DeserializationError'>DeserializationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t38">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html#t38"><data value='JSONSerializer'>JSONSerializer</data></a></td>
                <td>128</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="93 128">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html">src\core\storage\serializer.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t21">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t21"><data value='ChangeType'>ChangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t29">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t29"><data value='VersionInfo'>VersionInfo</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t76">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html#t76"><data value='VersionControlManager'>VersionControlManager</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html">src\core\storage\version_control.py</a></td>
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15___init___py.html">src\core\validation\__init__.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t22">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t22"><data value='ProjectBusinessRules'>ProjectBusinessRules</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t131">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t131"><data value='CharacterBusinessRules'>CharacterBusinessRules</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t221">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t221"><data value='SceneBusinessRules'>SceneBusinessRules</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t292">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t292"><data value='EventBusinessRules'>EventBusinessRules</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t373">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html#t373"><data value='StoryElementBusinessRules'>StoryElementBusinessRules</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html">src\core\validation\business_rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t10">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t10"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t17">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t17"><data value='ValidationIssue'>ValidationIssue</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t54">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t54"><data value='ValidationError'>ValidationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t67">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t67"><data value='ValidationWarning'>ValidationWarning</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t80">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t80"><data value='ValidationInfo'>ValidationInfo</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t93">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t93"><data value='ValidationException'>ValidationException</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t132">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t132"><data value='BusinessRuleViolation'>BusinessRuleViolation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t141">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t141"><data value='RelationshipValidationError'>RelationshipValidationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t150">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html#t150"><data value='FieldValidationError'>FieldValidationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html">src\core\validation\exceptions.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t19">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t19"><data value='CharacterRelationshipValidator'>CharacterRelationshipValidator</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t107">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t107"><data value='SceneEventRelationshipValidator'>SceneEventRelationshipValidator</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t167">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t167"><data value='ProjectElementRelationshipValidator'>ProjectElementRelationshipValidator</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t210">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t210"><data value='TimelineConsistencyValidator'>TimelineConsistencyValidator</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t257">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html#t257"><data value='PlotConsistencyValidator'>PlotConsistencyValidator</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html">src\core\validation\relationship_validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t17">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t17"><data value='StringValidationRule'>StringValidationRule</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t105">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t105"><data value='NumericValidationRule'>NumericValidationRule</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t185">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t185"><data value='DateTimeValidationRule'>DateTimeValidationRule</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t248">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t248"><data value='ListValidationRule'>ListValidationRule</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t307">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t307"><data value='EnumValidationRule'>EnumValidationRule</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t334">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t334"><data value='UUIDValidationRule'>UUIDValidationRule</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t360">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t360"><data value='EmailValidationRule'>EmailValidationRule</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t382">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t382"><data value='URLValidationRule'>URLValidationRule</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t411">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t411"><data value='FilePathValidationRule'>FilePathValidationRule</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t457">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html#t457"><data value='CustomValidationRule'>CustomValidationRule</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html">src\core\validation\rules.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t18">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t18"><data value='ValidationContext'>ValidationContext</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t68">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t68"><data value='ValidationRule'>ValidationRule</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>10</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t93">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t93"><data value='FieldValidator'>FieldValidator</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t147">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t147"><data value='BusinessRuleValidator'>BusinessRuleValidator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t161">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t161"><data value='RelationshipValidator'>RelationshipValidator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t175">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html#t175"><data value='ValidationEngine'>ValidationEngine</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html">src\core\validation\validators.py</a></td>
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>2</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t23">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t23"><data value='AIGenerationThread'>AIGenerationThread</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t57">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t57"><data value='ChatWidget'>ChatWidget</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t151">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t151"><data value='ContentGeneratorWidget'>ContentGeneratorWidget</data></a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t383">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html#t383"><data value='AIAssistantWidget'>AIAssistantWidget</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html">src\ui\ai_assistant.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t24">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t24"><data value='CharacterEditDialog'>CharacterEditDialog</data></a></td>
                <td>174</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="0 174">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t356">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t356"><data value='CharacterListWidget'>CharacterListWidget</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t441">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html#t441"><data value='CharacterManagerWidget'>CharacterManagerWidget</data></a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html">src\ui\character_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t23">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t23"><data value='EventEditDialog'>EventEditDialog</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t298">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t298"><data value='EventListWidget'>EventListWidget</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t383">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html#t383"><data value='EventManagerWidget'>EventManagerWidget</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html">src\ui\event_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t25">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t25"><data value='WelcomeWidget'>WelcomeWidget</data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t294">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html#t294"><data value='MainWindow'>MainWindow</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html">src\ui\main_window.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>7</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t20">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t20"><data value='ProjectDialog'>ProjectDialog</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t373">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html#t373"><data value='ProjectSettingsDialog'>ProjectSettingsDialog</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html">src\ui\project_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t23">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t23"><data value='SceneEditDialog'>SceneEditDialog</data></a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t311">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t311"><data value='SceneListWidget'>SceneListWidget</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t396">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html#t396"><data value='SceneManagerWidget'>SceneManagerWidget</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html">src\ui\scene_manager.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t21">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t21"><data value='GeneralSettingsWidget'>GeneralSettingsWidget</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t132">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t132"><data value='AISettingsWidget'>AISettingsWidget</data></a></td>
                <td>95</td>
                <td>95</td>
                <td>0</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t322">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t322"><data value='TemplateSettingsWidget'>TemplateSettingsWidget</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t396">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html#t396"><data value='SettingsDialog'>SettingsDialog</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html">src\ui\settings_dialog.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t29">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t29"><data value='RichTextEditor'>RichTextEditor</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t89">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t89"><data value='ChapterTreeWidget'>ChapterTreeWidget</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t154">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t154"><data value='WritingStatsWidget'>WritingStatsWidget</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t207">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html#t207"><data value='WritingInterface'>WritingInterface</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html">src\ui\writing_interface.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td class="name left"><a href="z_6156a86a215061be___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>5973</td>
                <td>3655</td>
                <td>75</td>
                <td class="right" data-ratio="2318 5973">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
