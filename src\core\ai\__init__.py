"""AI服务集成模块

提供AI服务的抽象层和具体实现，包括：
- AI服务抽象接口
- 多服务商适配器
- 内容生成器
- 配置管理
"""

from .manager import AIServiceManager, ContentGenerator
from .base import (
    AIProvider, GenerationType, GenerationContext, GenerationOptions,
    GenerationResult, StreamChunk, AIServiceError, PromptManager
)
from .adapters import OpenAIAdapter, AnthropicAdapter, DeepSeekAdapter, ZhipuAdapter

__all__ = [
    "AIServiceManager",
    "ContentGenerator",
    "AIProvider",
    "GenerationType",
    "GenerationContext",
    "GenerationOptions",
    "GenerationResult",
    "StreamChunk",
    "AIServiceError",
    "PromptManager",
    "OpenAIAdapter",
    "AnthropicAdapter",
    "DeepSeekAdapter",
    "ZhipuAdapter"
]
