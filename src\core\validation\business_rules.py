"""业务规则验证器

实现各种业务逻辑验证规则
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta

from ..models.base import BaseModel
from ..models.project import WritingProject
from ..models.character import Character
from ..models.scene import Scene
from ..models.event import Event
from ..models.story_element import StoryElement
from ..models.enums import (
    ImportanceLevel, ProjectStatus, ElementType,
    RelationType, EventType, EventCategory
)
from .validators import BusinessRuleValidator, ValidationContext


class ProjectBusinessRules(BusinessRuleValidator):
    """项目业务规则验证器"""
    
    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, WritingProject):
            return True
        
        project = model
        all_passed = True
        
        # 验证项目状态一致性
        if not self._validate_project_status_consistency(project, context):
            all_passed = False
        
        # 验证统计数据一致性
        if not self._validate_statistics_consistency(project, context):
            all_passed = False
        
        # 验证完成度合理性
        if not self._validate_completion_reasonableness(project, context):
            all_passed = False
        
        # 验证AI配置有效性
        if not self._validate_ai_configuration(project, context):
            all_passed = False
        
        return all_passed
    
    def get_rule_name(self) -> str:
        return "project_business_rules"
    
    def _validate_project_status_consistency(self, project: WritingProject, context: ValidationContext) -> bool:
        """验证项目状态一致性"""
        # 已完成项目的完成度应该是100%
        if project.status == ProjectStatus.COMPLETED and project.completion_percentage < 100:
            context.add_warning("已完成项目的完成度应该为100%")
            return False
        
        # 草稿项目的完成度应该是0%
        if project.status == ProjectStatus.DRAFT and project.completion_percentage > 0:
            context.add_warning("草稿项目的完成度应该为0%")
            return False

        # 规划中项目应该有一定进度
        if project.status == ProjectStatus.PLANNING and project.completion_percentage == 0:
            context.add_info("规划中的项目通常应该有一定进度")
        
        return True
    
    def _validate_statistics_consistency(self, project: WritingProject, context: ValidationContext) -> bool:
        """验证统计数据一致性"""
        # 字数和章节数的合理性
        if project.word_count > 0 and project.chapter_count == 0:
            context.add_warning("有字数但没有章节，数据可能不一致")
        
        if project.chapter_count > 0 and project.word_count == 0:
            context.add_warning("有章节但没有字数，数据可能不一致")
        
        # 平均每章字数合理性检查
        if project.chapter_count > 0:
            avg_words_per_chapter = project.word_count / project.chapter_count
            if avg_words_per_chapter < 500:
                context.add_warning("平均每章字数过少，可能存在数据问题")
            elif avg_words_per_chapter > 20000:
                context.add_warning("平均每章字数过多，可能存在数据问题")
        
        return True
    
    def _validate_completion_reasonableness(self, project: WritingProject, context: ValidationContext) -> bool:
        """验证完成度合理性"""
        # 根据字数估算完成度
        if project.settings.word_count_target > 0:
            estimated_completion = (project.word_count / project.settings.word_count_target) * 100
            completion_diff = abs(project.completion_percentage - estimated_completion)
            
            if completion_diff > 20:  # 差异超过20%
                context.add_warning(f"完成度({project.completion_percentage}%)与字数进度({estimated_completion:.1f}%)差异较大")
        
        return True
    
    def _validate_ai_configuration(self, project: WritingProject, context: ValidationContext) -> bool:
        """验证AI配置有效性"""
        ai_config = project.ai_config
        
        # 检查API密钥配置
        if ai_config.default_provider == 'openai' and not ai_config.openai_api_key:
            context.add_warning("选择了OpenAI但未配置API密钥")
        
        if ai_config.default_provider == 'deepseek' and not ai_config.deepseek_api_key:
            context.add_warning("选择了DeepSeek但未配置API密钥")
        
        if ai_config.default_provider == 'zhipu' and not ai_config.zhipu_api_key:
            context.add_warning("选择了智谱但未配置API密钥")
        
        if ai_config.default_provider == 'anthropic' and not ai_config.anthropic_api_key:
            context.add_warning("选择了Anthropic但未配置API密钥")
        
        # 检查参数合理性
        if ai_config.temperature < 0 or ai_config.temperature > 2:
            context.add_error("AI温度参数应该在0-2之间")
            return False
        
        if ai_config.max_tokens < 100 or ai_config.max_tokens > 32000:
            context.add_error("AI最大令牌数应该在100-32000之间")
            return False
        
        return True


class CharacterBusinessRules(BusinessRuleValidator):
    """角色业务规则验证器"""
    
    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, Character):
            return True
        
        character = model
        all_passed = True
        
        # 验证角色完整性
        if not self._validate_character_completeness(character, context):
            all_passed = False
        
        # 验证角色关系合理性
        if not self._validate_relationship_reasonableness(character, context):
            all_passed = False
        
        # 验证角色发展一致性
        if not self._validate_character_development(character, context):
            all_passed = False
        
        return all_passed
    
    def get_rule_name(self) -> str:
        return "character_business_rules"
    
    def _validate_character_completeness(self, character: Character, context: ValidationContext) -> bool:
        """验证角色完整性"""
        # 重要角色应该有完整信息
        if character.importance == ImportanceLevel.CRITICAL:
            if not character.character_arc:
                context.add_warning("关键角色应该有明确的角色弧线")
            
            if not character.motivation:
                context.add_warning("关键角色应该有明确的动机")
            
            if not character.personality.core_traits:
                context.add_warning("关键角色应该有核心性格特征")
        
        # 检查年龄和外貌的一致性
        if character.appearance.age and character.appearance.age > 60:
            if "年轻" in character.appearance.overall_description:
                context.add_warning("角色年龄与外貌描述可能不一致")
        
        return True
    
    def _validate_relationship_reasonableness(self, character: Character, context: ValidationContext) -> bool:
        """验证角色关系合理性"""
        # 检查关系的合理性
        for relationship in character.character_relationships:
            # 亲密度和信任度的一致性
            if relationship.intimacy_level > 8 and relationship.trust_level < 5:
                context.add_warning(f"与{relationship.target_character_id}的关系中，亲密度高但信任度低，可能不合理")
            
            # 冲突度和关系类型的一致性
            if relationship.relationship_type == RelationType.ROMANTIC and relationship.conflict_level > 7:
                context.add_warning(f"与{relationship.target_character_id}的恋爱关系冲突度过高")
            
            # 关系持续时间的合理性
            if relationship.relationship_start and relationship.relationship_end:
                duration = relationship.relationship_end - relationship.relationship_start
                if duration.days < 0:
                    context.add_error(f"与{relationship.target_character_id}的关系结束时间早于开始时间")
                    return False
        
        return True
    
    def _validate_character_development(self, character: Character, context: ValidationContext) -> bool:
        """验证角色发展一致性"""
        # 检查角色发展的时间顺序
        developments = character.character_development
        if len(developments) > 1:
            for i in range(1, len(developments)):
                prev_dev = developments[i-1]
                curr_dev = developments[i]
                
                # 如果有时间信息，检查顺序
                if 'timestamp' in prev_dev and 'timestamp' in curr_dev:
                    try:
                        prev_time = datetime.fromisoformat(prev_dev['timestamp'])
                        curr_time = datetime.fromisoformat(curr_dev['timestamp'])
                        if curr_time < prev_time:
                            context.add_warning("角色发展的时间顺序可能有问题")
                    except (ValueError, KeyError):
                        pass
        
        return True


class SceneBusinessRules(BusinessRuleValidator):
    """场景业务规则验证器"""

    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, Scene):
            return True

        scene = model
        all_passed = True

        # 验证场景完整性
        if not self._validate_scene_completeness(scene, context):
            all_passed = False

        # 验证场景一致性
        if not self._validate_scene_consistency(scene, context):
            all_passed = False

        # 验证场景使用合理性
        if not self._validate_scene_usage(scene, context):
            all_passed = False

        return all_passed

    def get_rule_name(self) -> str:
        return "scene_business_rules"

    def _validate_scene_completeness(self, scene: Scene, context: ValidationContext) -> bool:
        """验证场景完整性"""
        # 重要场景应该有详细描述
        if scene.importance == ImportanceLevel.CRITICAL:
            if not scene.detailed_description:
                context.add_warning("关键场景应该有详细描述")

            if not scene.atmosphere.mood_description:
                context.add_warning("关键场景应该有氛围描述")

        # 检查场景基本信息
        if not scene.location.name:
            context.add_warning("场景应该有明确的位置名称")

        return True

    def _validate_scene_consistency(self, scene: Scene, context: ValidationContext) -> bool:
        """验证场景一致性"""
        # 时间和环境的一致性
        if scene.time_setting.time_of_day and scene.environment.lighting:
            # 简单的一致性检查
            if "夜晚" in scene.time_setting.time_of_day and "明亮" in scene.environment.lighting:
                context.add_warning("夜晚场景的光线设定可能不一致")

        # 位置类型和环境的一致性
        if hasattr(scene.location, 'location_type'):
            if scene.location.location_type == "室内" and scene.environment.weather:
                context.add_info("室内场景通常不需要天气设定")

        return True

    def _validate_scene_usage(self, scene: Scene, context: ValidationContext) -> bool:
        """验证场景使用合理性"""
        # 使用次数和章节数的一致性
        if scene.usage_count > 0 and len(scene.chapters_used) == 0:
            context.add_warning("场景有使用次数但没有使用章节记录")

        if len(scene.chapters_used) > scene.usage_count:
            context.add_error("使用章节数不能超过使用次数")
            return False

        return True


class EventBusinessRules(BusinessRuleValidator):
    """事件业务规则验证器"""

    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, Event):
            return True

        event = model
        all_passed = True

        # 验证事件完整性
        if not self._validate_event_completeness(event, context):
            all_passed = False

        # 验证事件逻辑性
        if not self._validate_event_logic(event, context):
            all_passed = False

        # 验证事件影响合理性
        if not self._validate_event_impact(event, context):
            all_passed = False

        return all_passed

    def get_rule_name(self) -> str:
        return "event_business_rules"

    def _validate_event_completeness(self, event: Event, context: ValidationContext) -> bool:
        """验证事件完整性"""
        # 关键事件应该有完整信息
        if event.importance == ImportanceLevel.CRITICAL:
            if not event.outcome:
                context.add_warning("关键事件应该有明确的结果")

            if not event.participants:
                context.add_warning("关键事件应该有参与者")

            if not event.impact.plot_advancement:
                context.add_warning("关键事件应该描述情节推进")

        # 转折点事件的特殊要求
        if event.is_turning_point:
            if event.tension_level < 0.6:
                context.add_warning("转折点事件的紧张度通常应该较高")

            if not event.consequences:
                context.add_warning("转折点事件应该有明确的后果")

        return True

    def _validate_event_logic(self, event: Event, context: ValidationContext) -> bool:
        """验证事件逻辑性"""
        # 检查触发条件和后果的逻辑性
        if event.triggers and event.consequences:
            if len(event.triggers) == 0 and len(event.consequences) > 0:
                context.add_info("事件有后果但没有触发条件，可能需要补充")

        # 检查参与者的合理性
        main_participants = [p for p in event.participants
                           if p.role in ['protagonist', 'antagonist']]
        if len(main_participants) == 0 and event.importance == ImportanceLevel.CRITICAL:
            context.add_warning("关键事件应该有主要参与者（主角或反角）")

        return True

    def _validate_event_impact(self, event: Event, context: ValidationContext) -> bool:
        """验证事件影响合理性"""
        impact = event.impact

        # 影响程度和重要性的一致性
        if event.importance == ImportanceLevel.CRITICAL:
            if impact.plot_impact < 0.6:
                context.add_warning("关键事件的情节影响通常应该较高")

        # 情感影响和紧张度的一致性
        if impact.emotional_impact > 0.8 and event.tension_level < 0.5:
            context.add_warning("高情感影响的事件紧张度通常也应该较高")

        return True


class StoryElementBusinessRules(BusinessRuleValidator):
    """故事元素通用业务规则验证器"""

    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        if not isinstance(model, StoryElement):
            return True

        element = model
        all_passed = True

        # 验证元素基本完整性
        if not self._validate_element_completeness(element, context):
            all_passed = False

        # 验证元素关系合理性
        if not self._validate_element_relationships(element, context):
            all_passed = False

        return all_passed

    def get_rule_name(self) -> str:
        return "story_element_business_rules"

    def _validate_element_completeness(self, element: StoryElement, context: ValidationContext) -> bool:
        """验证元素完整性"""
        # 名称不能为空
        if not element.name or not element.name.strip():
            context.add_error("故事元素名称不能为空")
            return False

        # 重要元素应该有描述
        if element.importance == ImportanceLevel.CRITICAL and not element.description:
            context.add_warning("关键故事元素应该有描述")

        return True

    def _validate_element_relationships(self, element: StoryElement, context: ValidationContext) -> bool:
        """验证元素关系合理性"""
        # 检查关系的有效性
        for relationship in element.relationships:
            if relationship.strength < 0 or relationship.strength > 1:
                context.add_error("关系强度必须在0-1之间")
                return False

            # 检查关系的时间有效性
            if relationship.valid_from and relationship.valid_to:
                if relationship.valid_to < relationship.valid_from:
                    context.add_error("关系结束时间不能早于开始时间")
                    return False

        return True
