"""场景模型

定义故事场景相关的数据模型，包括：
- SceneLocation: 场景位置信息
- SceneTime: 场景时间信息  
- SceneEnvironment: 场景环境信息
- SceneAtmosphere: 场景氛围信息
- Scene: 主要场景模型
- SceneValidator: 场景验证器
"""

from datetime import datetime, time
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID

from pydantic import Field, validator

from .base import BaseModel, ValidationResult, ModelValidator
from .story_element import StoryElement, StoryElementValidator
from .enums import (
    ElementType, LocationType, SceneFunction, ImportanceLevel
)


class SceneLocation(BaseModel):
    """场景位置信息"""
    
    # 基础位置信息
    name: str = Field(default="", description="位置名称")
    location_type: LocationType = Field(default=LocationType.INDOOR, description="位置类型")
    address: Optional[str] = Field(default=None, description="详细地址")
    coordinates: Optional[Tuple[float, float]] = Field(default=None, description="坐标(经度,纬度)")
    
    # 层级关系
    parent_location: Optional[UUID] = Field(default=None, description="父级位置ID")
    sub_locations: List[UUID] = Field(default_factory=list, description="子位置ID列表")
    
    # 位置描述
    size_description: str = Field(default="", description="大小描述")
    layout_description: str = Field(default="", description="布局描述")
    accessibility: List[str] = Field(default_factory=list, description="可达性描述")
    
    @validator('name')
    def validate_name(cls, v):
        if v and len(v) > 100:
            raise ValueError("位置名称不能超过100个字符")
        return v


class SceneTime(BaseModel):
    """场景时间信息"""
    
    # 时间设定
    time_period: str = Field(default="", description="时间段描述")
    season: Optional[str] = Field(default=None, description="季节")
    time_of_day: Optional[str] = Field(default=None, description="一天中的时间")
    specific_time: Optional[time] = Field(default=None, description="具体时间")
    
    # 时间特征
    duration_description: str = Field(default="", description="持续时间描述")
    time_flow: str = Field(default="normal", description="时间流逝感 (slow/normal/fast)")
    temporal_mood: str = Field(default="", description="时间氛围")


class SceneEnvironment(BaseModel):
    """场景环境信息"""
    
    # 天气环境
    weather: Optional[str] = Field(default=None, description="天气状况")
    temperature: Optional[str] = Field(default=None, description="温度描述")
    humidity: Optional[str] = Field(default=None, description="湿度描述")
    
    # 光线环境
    lighting: Optional[str] = Field(default=None, description="光线描述")
    light_source: List[str] = Field(default_factory=list, description="光源列表")
    shadows: str = Field(default="", description="阴影描述")
    
    # 感官环境
    sounds: List[str] = Field(default_factory=list, description="声音列表")
    smells: List[str] = Field(default_factory=list, description="气味列表")
    textures: List[str] = Field(default_factory=list, description="质感列表")
    colors: List[str] = Field(default_factory=list, description="颜色列表")
    
    # 环境特征
    air_quality: Optional[str] = Field(default=None, description="空气质量")
    visibility: Optional[str] = Field(default=None, description="能见度")
    environmental_hazards: List[str] = Field(default_factory=list, description="环境危险")


class SceneAtmosphere(BaseModel):
    """场景氛围信息"""
    
    # 情绪氛围
    emotional_tone: str = Field(default="", description="情绪基调")
    mood_descriptors: List[str] = Field(default_factory=list, description="氛围描述词")
    tension_level: float = Field(default=0.5, description="紧张度 (0.0-1.0)")
    
    # 氛围元素
    symbolic_elements: List[str] = Field(default_factory=list, description="象征元素")
    metaphorical_descriptions: List[str] = Field(default_factory=list, description="隐喻描述")
    
    # 感官印象
    overall_impression: str = Field(default="", description="整体印象")
    dominant_sensory_element: str = Field(default="", description="主导感官元素")
    
    @validator('tension_level')
    def validate_tension_level(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("紧张度必须在0.0-1.0之间")
        return v


class Scene(StoryElement):
    """故事场景模型"""
    
    # 元素类型
    element_type: ElementType = Field(default=ElementType.SCENE, description="元素类型")
    
    # 位置信息
    location: SceneLocation = Field(default_factory=SceneLocation, description="位置信息")
    
    # 时间信息
    time_setting: SceneTime = Field(default_factory=SceneTime, description="时间设定")
    
    # 环境信息
    environment: SceneEnvironment = Field(default_factory=SceneEnvironment, description="环境信息")
    
    # 氛围信息
    atmosphere: SceneAtmosphere = Field(default_factory=SceneAtmosphere, description="氛围信息")
    
    # 功能属性
    scene_function: SceneFunction = Field(default=SceneFunction.SETTING, description="场景功能")
    recurring: bool = Field(default=False, description="是否重复使用")
    
    # 关联元素
    typical_characters: List[UUID] = Field(default_factory=list, description="常见角色ID列表")
    typical_events: List[UUID] = Field(default_factory=list, description="典型事件ID列表")
    
    # 使用统计
    usage_count: int = Field(default=0, description="使用次数")
    chapters_used: List[UUID] = Field(default_factory=list, description="使用章节ID列表")
    
    # 场景详细描述
    detailed_description: str = Field(default="", description="详细描述")
    key_features: List[str] = Field(default_factory=list, description="关键特征")
    hidden_elements: List[str] = Field(default_factory=list, description="隐藏元素")
    
    def __init__(self, **data):
        """初始化场景"""
        # 确保name字段有值
        if 'name' not in data and 'location' in data and hasattr(data['location'], 'name'):
            data['name'] = data['location'].name or "未命名场景"
        elif 'name' not in data:
            data['name'] = "未命名场景"
        
        super().__init__(**data)
    
    def add_character(self, character_id: UUID):
        """添加常见角色"""
        if character_id not in self.typical_characters:
            self.typical_characters.append(character_id)
            self.update_timestamp()
    
    def remove_character(self, character_id: UUID):
        """移除常见角色"""
        if character_id in self.typical_characters:
            self.typical_characters.remove(character_id)
            self.update_timestamp()
    
    def add_event(self, event_id: UUID):
        """添加典型事件"""
        if event_id not in self.typical_events:
            self.typical_events.append(event_id)
            self.update_timestamp()
    
    def remove_event(self, event_id: UUID):
        """移除典型事件"""
        if event_id in self.typical_events:
            self.typical_events.remove(event_id)
            self.update_timestamp()
    
    def use_in_chapter(self, chapter_id: UUID):
        """在章节中使用场景"""
        if chapter_id not in self.chapters_used:
            self.chapters_used.append(chapter_id)
        self.usage_count += 1
        self.update_timestamp()
    
    def get_atmosphere_score(self) -> float:
        """获取氛围评分"""
        score = 0.0
        
        # 基于氛围描述的评分
        if self.atmosphere.emotional_tone:
            score += 0.2
        if self.atmosphere.mood_descriptors:
            score += 0.2
        if self.atmosphere.symbolic_elements:
            score += 0.2
        if self.atmosphere.overall_impression:
            score += 0.2
        
        # 基于环境丰富度的评分
        env_elements = (
            len(self.environment.sounds) +
            len(self.environment.smells) +
            len(self.environment.colors) +
            len(self.environment.textures)
        )
        if env_elements > 0:
            score += min(0.2, env_elements * 0.05)
        
        return min(1.0, score)
    
    def validate_model(self) -> ValidationResult:
        """验证场景模型"""
        result = ValidationResult()
        
        # 验证基础字段
        if not self.name or not self.name.strip():
            result.add_error("场景名称不能为空")
        
        if len(self.name) > 100:
            result.add_error("场景名称不能超过100个字符")
        
        if len(self.description) > 1000:
            result.add_error("场景描述不能超过1000个字符")
        
        if len(self.detailed_description) > 5000:
            result.add_error("详细描述不能超过5000个字符")
        
        # 验证位置信息
        if self.location.name and len(self.location.name) > 100:
            result.add_error("位置名称不能超过100个字符")
        
        # 验证氛围信息
        if self.atmosphere.tension_level < 0.0 or self.atmosphere.tension_level > 1.0:
            result.add_error("紧张度必须在0.0-1.0之间")
        
        # 验证使用统计
        if self.usage_count < 0:
            result.add_error("使用次数不能为负数")
        
        if len(self.chapters_used) > self.usage_count:
            result.add_error("使用章节数不能超过使用次数")
        
        return result


class SceneValidator(ModelValidator):
    """场景验证器"""
    
    def validate(self, model: Scene) -> ValidationResult:
        """验证场景"""
        result = ValidationResult()
        
        # 基础验证
        base_result = model.validate_model()
        result.merge(base_result)
        
        # 业务规则验证
        self._validate_scene_completeness(model, result)
        self._validate_scene_consistency(model, result)
        self._validate_scene_relationships(model, result)
        
        return result
    
    def _validate_scene_completeness(self, model: Scene, result: ValidationResult):
        """验证场景完整性"""
        # 检查关键信息是否完整
        if not model.location.name and not model.name:
            result.add_warning("场景缺少位置名称")
        
        if not model.description and not model.detailed_description:
            result.add_warning("场景缺少描述信息")
        
        # 检查氛围设定
        if (not model.atmosphere.emotional_tone and 
            not model.atmosphere.mood_descriptors and
            not model.atmosphere.overall_impression):
            result.add_warning("场景缺少氛围设定")
    
    def _validate_scene_consistency(self, model: Scene, result: ValidationResult):
        """验证场景一致性"""
        # 检查时间和环境的一致性
        if (model.time_setting.season and 
            model.environment.weather and
            not self._is_weather_season_consistent(
                model.environment.weather, 
                model.time_setting.season
            )):
            result.add_warning("天气与季节设定可能不一致")
        
        # 检查位置类型和环境的一致性
        if (model.location.location_type == LocationType.INDOOR and
            model.environment.weather):
            result.add_warning("室内场景通常不需要天气设定")
    
    def _validate_scene_relationships(self, model: Scene, result: ValidationResult):
        """验证场景关系"""
        # 检查角色和事件的关联
        if model.typical_characters and not model.typical_events:
            result.add_info("场景有常见角色但没有典型事件")
        
        if model.typical_events and not model.typical_characters:
            result.add_info("场景有典型事件但没有常见角色")
    
    def _is_weather_season_consistent(self, weather: str, season: str) -> bool:
        """检查天气和季节是否一致"""
        # 简单的一致性检查
        weather_lower = weather.lower()
        season_lower = season.lower()
        
        # 夏季不应该有雪
        if "夏" in season_lower and ("雪" in weather_lower or "冰" in weather_lower):
            return False
        
        # 冬季不应该太热
        if "冬" in season_lower and ("炎热" in weather_lower or "酷热" in weather_lower):
            return False
        
        return True
