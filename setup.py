#!/usr/bin/env python3
"""笔落App安装配置文件"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# 读取requirements文件
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, "r", encoding="utf-8") as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith("#")
        ]

setup(
    name="bamboofall-app",
    version="1.0.0",
    author="笔落开发团队",
    author_email="<EMAIL>",
    description="AI辅助小说创作工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/bamboofall/bamboofall-app",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Text Processing :: General",
        "Topic :: Multimedia :: Graphics",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-qt>=4.2.0", 
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "build": [
            "pyinstaller>=5.13.0",
            "cx-freeze>=6.15.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "bamboofall=main:main",
        ],
        "gui_scripts": [
            "bamboofall-gui=main:main",
        ]
    },
    include_package_data=True,
    package_data={
        "": ["*.qss", "*.ui", "*.qrc", "*.ts", "*.qm"],
        "resources": ["*"],
    },
    zip_safe=False,
)
