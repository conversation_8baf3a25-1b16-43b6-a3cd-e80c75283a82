"""
GUI集成测试模块
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtTest import QTest

from src.ui.main_window import MainWindow, WelcomeWidget
from src.ui.writing_interface import WritingInterface
from src.ui.ai_assistant import AIAssistantWidget
from src.ui.character_manager import CharacterManagerWidget
from src.ui.scene_manager import SceneManagerWidget
from src.ui.event_manager import EventManagerWidget
from src.ui.settings_dialog import SettingsDialog
from src.ui.project_dialog import ProjectDialog
from src.core.models.project import WritingProject
from src.core.models.character import Character
from src.core.models.scene import Scene
from src.core.models.event import Event


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_project():
    """创建示例项目"""
    return WritingProject(
        name="测试项目",
        author="测试作者",
        description="测试描述"
    )


@pytest.fixture
def sample_character():
    """创建示例角色"""
    return Character(
        name="测试角色",
        full_name="测试角色全名"
    )


class TestMainWindowIntegration:
    """主窗口集成测试"""
    
    def test_main_window_startup(self, qtbot, temp_dir):
        """测试主窗口启动"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage.return_value = Mock()
            
            window = MainWindow()
            qtbot.addWidget(window)
            
            # 验证窗口创建成功
            assert window is not None
            assert window.isVisible() is False  # 初始状态未显示
            
            # 显示窗口
            window.show()
            assert window.isVisible() is True
            
    def test_welcome_widget_functionality(self, qtbot, temp_dir):
        """测试欢迎界面功能"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage.return_value = Mock()
            
            window = MainWindow()
            qtbot.addWidget(window)
            
            # 获取欢迎界面
            welcome_widget = window.findChild(WelcomeWidget)
            assert welcome_widget is not None
            
            # 测试新建项目按钮
            new_project_btn = welcome_widget.new_project_btn
            assert new_project_btn is not None
            
            # 模拟点击新建项目按钮
            with patch.object(window, 'show_new_project_dialog') as mock_dialog:
                qtbot.mouseClick(new_project_btn, Qt.MouseButton.LeftButton)
                mock_dialog.assert_called_once()
                
    def test_project_creation_workflow(self, qtbot, temp_dir):
        """测试项目创建工作流"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage_instance = Mock()
            mock_storage.return_value = mock_storage_instance
            
            window = MainWindow()
            qtbot.addWidget(window)
            
            # 模拟项目创建对话框
            with patch('src.ui.project_dialog.ProjectDialog') as mock_dialog_class:
                mock_dialog = Mock()
                mock_dialog.exec.return_value = 1  # QDialog.Accepted
                mock_dialog.get_project_data.return_value = {
                    'name': '测试项目',
                    'author': '测试作者',
                    'description': '测试描述'
                }
                mock_dialog_class.return_value = mock_dialog
                
                # 触发新建项目
                window.show_new_project_dialog()
                
                # 验证对话框被调用
                mock_dialog_class.assert_called_once()
                mock_dialog.exec.assert_called_once()
                
    def test_project_loading_workflow(self, qtbot, temp_dir, sample_project):
        """测试项目加载工作流"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage_instance = Mock()
            mock_storage_instance.load_project.return_value = sample_project
            mock_storage.return_value = mock_storage_instance
            
            window = MainWindow()
            qtbot.addWidget(window)
            
            # 模拟加载项目
            project_path = "/fake/path/project.json"
            window.load_project(project_path)
            
            # 验证项目被设置
            assert window.current_project == sample_project
            
            # 验证写作界面被创建
            assert window.writing_interface is not None


class TestWritingInterfaceIntegration:
    """写作界面集成测试"""
    
    def test_writing_interface_creation(self, qtbot, sample_project):
        """测试写作界面创建"""
        interface = WritingInterface(sample_project)
        qtbot.addWidget(interface)
        
        assert interface is not None
        assert interface.project == sample_project
        
    def test_text_editor_functionality(self, qtbot, sample_project):
        """测试文本编辑器功能"""
        interface = WritingInterface(sample_project)
        qtbot.addWidget(interface)
        
        # 获取文本编辑器
        text_editor = interface.text_editor
        assert text_editor is not None
        
        # 测试文本输入
        test_text = "这是测试文本"
        text_editor.setPlainText(test_text)
        assert text_editor.toPlainText() == test_text
        
        # 测试文本变化信号
        with patch.object(interface, 'on_text_changed') as mock_handler:
            text_editor.textChanged.emit()
            mock_handler.assert_called_once()
            
    def test_chapter_management(self, qtbot, sample_project):
        """测试章节管理"""
        interface = WritingInterface(sample_project)
        qtbot.addWidget(interface)
        
        # 获取章节列表
        chapter_list = interface.chapter_list
        assert chapter_list is not None
        
        # 测试添加章节
        initial_count = chapter_list.count()
        interface.add_new_chapter()
        assert chapter_list.count() == initial_count + 1
        
    def test_auto_save_functionality(self, qtbot, sample_project):
        """测试自动保存功能"""
        interface = WritingInterface(sample_project)
        qtbot.addWidget(interface)
        
        # 验证自动保存定时器存在
        assert interface.auto_save_timer is not None
        
        # 测试手动保存
        with patch.object(interface, 'save_current_content') as mock_save:
            interface.save_content()
            mock_save.assert_called_once()


class TestAIAssistantIntegration:
    """AI助手集成测试"""
    
    def test_ai_assistant_creation(self, qtbot):
        """测试AI助手创建"""
        assistant = AIAssistantWidget()
        qtbot.addWidget(assistant)
        
        assert assistant is not None
        
    def test_chat_interface(self, qtbot):
        """测试聊天界面"""
        assistant = AIAssistantWidget()
        qtbot.addWidget(assistant)
        
        # 获取聊天组件
        chat_display = assistant.chat_display
        message_input = assistant.message_input
        send_button = assistant.send_button
        
        assert chat_display is not None
        assert message_input is not None
        assert send_button is not None
        
        # 测试消息输入
        test_message = "测试消息"
        message_input.setText(test_message)
        assert message_input.text() == test_message
        
    def test_content_generation(self, qtbot):
        """测试内容生成"""
        assistant = AIAssistantWidget()
        qtbot.addWidget(assistant)
        
        # 获取生成按钮
        generate_button = assistant.generate_button
        assert generate_button is not None
        
        # 模拟内容生成
        with patch.object(assistant, 'start_generation') as mock_generate:
            qtbot.mouseClick(generate_button, Qt.MouseButton.LeftButton)
            mock_generate.assert_called_once()


class TestDataManagersIntegration:
    """数据管理器集成测试"""
    
    def test_character_manager_creation(self, qtbot):
        """测试角色管理器创建"""
        manager = CharacterManagerWidget()
        qtbot.addWidget(manager)
        
        assert manager is not None
        
    def test_character_list_functionality(self, qtbot, sample_character):
        """测试角色列表功能"""
        manager = CharacterManagerWidget()
        qtbot.addWidget(manager)
        
        # 获取角色列表
        character_list = manager.character_list
        assert character_list is not None
        
        # 测试添加角色
        initial_count = character_list.count()
        manager.add_character(sample_character)
        assert character_list.count() == initial_count + 1
        
    def test_scene_manager_creation(self, qtbot):
        """测试场景管理器创建"""
        manager = SceneManagerWidget()
        qtbot.addWidget(manager)
        
        assert manager is not None
        
    def test_event_manager_creation(self, qtbot):
        """测试事件管理器创建"""
        manager = EventManagerWidget()
        qtbot.addWidget(manager)
        
        assert manager is not None


class TestDialogsIntegration:
    """对话框集成测试"""
    
    def test_settings_dialog_creation(self, qtbot):
        """测试设置对话框创建"""
        dialog = SettingsDialog()
        qtbot.addWidget(dialog)
        
        assert dialog is not None
        
    def test_settings_dialog_tabs(self, qtbot):
        """测试设置对话框标签页"""
        dialog = SettingsDialog()
        qtbot.addWidget(dialog)
        
        # 获取标签页组件
        tab_widget = dialog.tab_widget
        assert tab_widget is not None
        assert tab_widget.count() > 0
        
    def test_project_dialog_creation(self, qtbot):
        """测试项目对话框创建"""
        dialog = ProjectDialog()
        qtbot.addWidget(dialog)
        
        assert dialog is not None
        
    def test_project_dialog_validation(self, qtbot):
        """测试项目对话框验证"""
        dialog = ProjectDialog()
        qtbot.addWidget(dialog)
        
        # 获取输入字段
        name_input = dialog.name_input
        author_input = dialog.author_input
        
        assert name_input is not None
        assert author_input is not None
        
        # 测试输入验证
        name_input.setText("测试项目")
        author_input.setText("测试作者")
        
        # 验证数据获取
        project_data = dialog.get_project_data()
        assert project_data['name'] == "测试项目"
        assert project_data['author'] == "测试作者"


class TestKeyboardShortcuts:
    """键盘快捷键测试"""
    
    def test_main_window_shortcuts(self, qtbot, temp_dir):
        """测试主窗口快捷键"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage.return_value = Mock()
            
            window = MainWindow()
            qtbot.addWidget(window)
            
            # 测试Ctrl+N新建项目快捷键
            with patch.object(window, 'show_new_project_dialog') as mock_dialog:
                qtbot.keySequence(window, "Ctrl+N")
                mock_dialog.assert_called_once()
                
    def test_writing_interface_shortcuts(self, qtbot, sample_project):
        """测试写作界面快捷键"""
        interface = WritingInterface(sample_project)
        qtbot.addWidget(interface)
        
        # 测试Ctrl+S保存快捷键
        with patch.object(interface, 'save_content') as mock_save:
            qtbot.keySequence(interface, "Ctrl+S")
            mock_save.assert_called_once()


class TestErrorHandling:
    """错误处理测试"""
    
    def test_storage_error_handling(self, qtbot, temp_dir):
        """测试存储错误处理"""
        with patch('src.ui.main_window.StorageManager') as mock_storage:
            mock_storage.side_effect = Exception("存储初始化失败")
            
            # 应该优雅地处理错误
            try:
                window = MainWindow()
                qtbot.addWidget(window)
            except Exception as e:
                pytest.fail(f"应该优雅地处理存储错误，但抛出了异常: {e}")
                
    def test_ai_service_error_handling(self, qtbot):
        """测试AI服务错误处理"""
        assistant = AIAssistantWidget()
        qtbot.addWidget(assistant)
        
        # 模拟AI服务错误
        with patch.object(assistant, 'ai_service') as mock_service:
            mock_service.generate_content.side_effect = Exception("AI服务错误")
            
            # 应该优雅地处理错误
            assistant.start_generation()
            
            # 验证错误被正确处理（不应该崩溃）
            assert assistant is not None
