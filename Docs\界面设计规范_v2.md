# 笔落App界面设计规范 v2.0

**版本**: 2.0
**创建日期**: 2025-09-12
**修订说明**: 基于用户体验分析的完整界面设计规范
**状态**: 修订版

## 版本修订说明

### v2.0 主要改进
1. **完善界面层次**: 增加工具栏、菜单栏、状态栏设计
2. **响应式设计**: 支持不同屏幕尺寸和窗口大小适配
3. **交互增强**: 添加快捷键、拖拽操作、右键菜单
4. **主题系统**: 支持多主题切换和个性化定制
5. **无障碍支持**: 考虑无障碍访问和国际化需求

---

## 1. 设计原则

### 1.1 核心设计理念
- **以内容为中心**: 界面设计围绕内容创作优化
- **直观易用**: 新用户能够快速上手，减少学习成本
- **高效操作**: 支持键盘快捷键和批量操作
- **美观专业**: 体现文学创作的专业性和艺术性
- **可定制性**: 支持用户个性化定制界面

### 1.2 视觉设计原则
- **层次清晰**: 通过颜色、大小、间距建立清晰的视觉层次
- **一致性**: 保持整个应用的视觉和交互一致性
- **简洁性**: 避免不必要的装饰，专注于功能性
- **可读性**: 确保文本内容的良好可读性
- **品牌识别**: 体现"笔落"品牌的文学气质

### 1.3 交互设计原则
- **反馈及时**: 用户操作后立即提供视觉反馈
- **容错性**: 提供撤销机制和错误恢复
- **渐进式披露**: 根据用户需求逐步展示功能
- **上下文相关**: 根据当前状态提供相关操作
- **快捷高效**: 为高频操作提供快捷方式

---

## 2. 整体布局架构

### 2.1 应用窗口结构
```
┌─────────────────────────────────────────────────────────────┐
│                        标题栏                                │
├─────────────────────────────────────────────────────────────┤
│                        菜单栏                                │
├─────────────────────────────────────────────────────────────┤
│                        工具栏                                │
├─────────────────────────────────────────────────────────────┤
│  侧边栏  │                主内容区                          │
│         │                                                  │
│  导航   │  ┌─────────────────────────────────────────────┐  │
│  面板   │  │              编辑区域                        │  │
│         │  │                                             │  │
│         │  └─────────────────────────────────────────────┘  │
│         │  ┌─────────────────────────────────────────────┐  │
│         │  │              AI交互区                       │  │
│         │  └─────────────────────────────────────────────┘  │
├─────────┼─────────────────────────────────────────────────┤
│         │                    状态栏                        │
└─────────┴─────────────────────────────────────────────────┘
```

### 2.2 布局比例
- **侧边栏**: 20-25% 宽度，可折叠
- **主内容区**: 75-80% 宽度，可调节
- **编辑区域**: 主内容区的 60-70% 高度
- **AI交互区**: 主内容区的 30-40% 高度
- **最小窗口尺寸**: 1024x768
- **推荐窗口尺寸**: 1440x900

### 2.3 响应式设计
```python
# 窗口尺寸断点
BREAKPOINTS = {
    'small': 1024,      # 小屏幕
    'medium': 1440,     # 中等屏幕
    'large': 1920,      # 大屏幕
    'xlarge': 2560      # 超大屏幕
}

# 布局适配规则
LAYOUT_RULES = {
    'small': {
        'sidebar_width': '200px',
        'sidebar_collapsible': True,
        'toolbar_compact': True,
        'font_size': 'small'
    },
    'medium': {
        'sidebar_width': '250px',
        'sidebar_collapsible': False,
        'toolbar_compact': False,
        'font_size': 'medium'
    },
    'large': {
        'sidebar_width': '300px',
        'sidebar_collapsible': False,
        'toolbar_compact': False,
        'font_size': 'large'
    }
}
```

---

## 3. 主界面设计

### 3.1 启动界面
#### 3.1.1 布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    背景图片区域                              │
│                 （体现"笔落"意境）                           │
│                                                             │
│    ┌─────────────────────────────────────────────────┐     │
│    │                操作面板                          │     │
│    │  ┌─────────────┐  ┌─────────────┐               │     │
│    │  │  新建项目    │  │  打开项目    │               │     │
│    │  └─────────────┘  └─────────────┘               │     │
│    │                                                 │     │
│    │              最近项目列表                        │     │
│    │  ┌─────────────────────────────────────────┐   │     │
│    │  │ 项目1  │ 类型  │ 修改时间  │ 进度      │   │     │
│    │  │ 项目2  │ 类型  │ 修改时间  │ 进度      │   │     │
│    │  └─────────────────────────────────────────┘   │     │
│    │                                                 │     │
│    │  ┌─────────┐  ┌─────────┐  ┌─────────┐         │     │
│    │  │  设置   │  │  帮助   │  │  关于   │         │     │
│    │  └─────────┘  └─────────┘  └─────────┘         │     │
│    └─────────────────────────────────────────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 3.1.2 设计要素
- **背景图片**: 体现"笔落"寓意的意境图片，如毛笔、墨迹、纸张等
- **操作面板**: 半透明背景，突出功能按钮
- **按钮设计**: 圆角矩形，渐变色彩，悬停效果
- **最近项目**: 表格形式，支持排序和搜索
- **色彩方案**: 以墨色、纸色为主，体现文学气质

### 3.2 项目创建界面
#### 3.2.1 对话框设计
```
┌─────────────────────────────────────────────────────────────┐
│  新建项目                                            × 关闭  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  项目信息                                                    │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 项目名称: [________________________]               │   │
│  │                                                     │   │
│  │ 作者姓名: [________________________]               │   │
│  │                                                     │   │
│  │ 项目类型: [玄幻小说 ▼]                              │   │
│  │                                                     │   │
│  │ 项目简介:                                           │   │
│  │ ┌─────────────────────────────────────────────┐   │   │
│  │ │                                             │   │   │
│  │ │                                             │   │   │
│  │ │                                             │   │   │
│  │ └─────────────────────────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  创作模板                                                    │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ○ 空白项目                                           │   │
│  │ ○ 玄幻小说模板                                       │   │
│  │ ○ 都市小说模板                                       │   │
│  │ ○ 科幻小说模板                                       │   │
│  │ ○ 自定义模板...                                      │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│                    [取消]  [创建项目]                        │
└─────────────────────────────────────────────────────────────┘
```

#### 3.2.2 交互特性
- **实时验证**: 输入时实时检查项目名称有效性
- **模板预览**: 选择模板时显示模板说明和结构预览
- **智能建议**: 根据项目类型推荐合适的模板
- **快捷键**: 支持 Enter 确认，Esc 取消

---

## 4. 创作界面设计

### 4.1 整体布局
#### 4.1.1 主要区域划分
```
┌─────────────────────────────────────────────────────────────┐
│ 文件 编辑 视图 插入 格式 工具 AI 帮助                         │
├─────────────────────────────────────────────────────────────┤
│ [新建] [保存] [撤销] [重做] │ [粗体] [斜体] │ [AI生成] [优化] │
├─────────────────────────────────────────────────────────────┤
│ 故事结构 │                主编辑区域                         │
│ ┌─────┐ │ ┌─────────────────────────────────────────────┐   │
│ │大纲 │ │ │                                             │   │
│ │主线 │ │ │              章节内容编辑                    │   │
│ │章节 │ │ │                                             │   │
│ └─────┘ │ │                                             │   │
│         │ │                                             │   │
│ 角色管理 │ │                                             │   │
│ ┌─────┐ │ └─────────────────────────────────────────────┘   │
│ │角色1│ │ ┌─────────────────────────────────────────────┐   │
│ │角色2│ │ │              AI交互区域                      │   │
│ │角色3│ │ │ 输入: [_________________________] [发送]    │   │
│ └─────┘ │ │ 模型: [GPT-4 ▼]                            │   │
│         │ │ 输出: AI生成的内容将在这里显示...            │   │
│ 场景事件 │ └─────────────────────────────────────────────┘   │
│ ┌─────┐ │                                                   │
│ │场景 │ │                                                   │
│ │事件 │ │                                                   │
│ └─────┘ │                                                   │
├─────────┼─────────────────────────────────────────────────┤
│ 就绪    │ 字数: 1,234 │ 章节: 第1章 │ 保存: 已保存          │
└─────────┴─────────────────────────────────────────────────┘
```

#### 4.1.2 区域功能说明
- **菜单栏**: 完整的应用功能菜单
- **工具栏**: 常用功能快速访问
- **侧边栏**: 故事结构和元素管理
- **主编辑区**: 章节内容编辑
- **AI交互区**: AI辅助功能
- **状态栏**: 实时状态信息

### 4.2 菜单栏设计
#### 4.2.1 菜单结构
```
文件(F)
├── 新建项目 (Ctrl+N)
├── 打开项目 (Ctrl+O)
├── 保存项目 (Ctrl+S)
├── 另存为... (Ctrl+Shift+S)
├── ──────────
├── 导入
│   ├── 从Word导入...
│   ├── 从文本文件导入...
│   └── 从其他软件导入...
├── 导出
│   ├── 导出为Word... (Ctrl+E)
│   ├── 导出为PDF...
│   ├── 导出为ePub...
│   └── 导出为文本...
├── ──────────
├── 项目设置...
├── 最近项目
└── 退出 (Alt+F4)

编辑(E)
├── 撤销 (Ctrl+Z)
├── 重做 (Ctrl+Y)
├── ──────────
├── 剪切 (Ctrl+X)
├── 复制 (Ctrl+C)
├── 粘贴 (Ctrl+V)
├── ──────────
├── 查找 (Ctrl+F)
├── 替换 (Ctrl+H)
├── 查找下一个 (F3)
├── ──────────
├── 全选 (Ctrl+A)
└── 选择性粘贴...

视图(V)
├── 显示/隐藏侧边栏 (F9)
├── 显示/隐藏工具栏
├── 显示/隐藏状态栏
├── ──────────
├── 专注模式 (F11)
├── 全屏模式 (F12)
├── ──────────
├── 缩放
│   ├── 放大 (Ctrl++)
│   ├── 缩小 (Ctrl+-)
│   └── 重置缩放 (Ctrl+0)
└── 主题
    ├── 默认主题
    ├── 深色主题
    ├── 护眼主题
    └── 自定义主题...

插入(I)
├── 新建章节 (Ctrl+Shift+N)
├── 新建角色...
├── 新建场景...
├── 新建事件...
├── ──────────
├── 插入图片...
├── 插入表格...
└── 插入分页符

格式(O)
├── 字体...
├── 段落...
├── ──────────
├── 粗体 (Ctrl+B)
├── 斜体 (Ctrl+I)
├── 下划线 (Ctrl+U)
├── ──────────
├── 标题样式
│   ├── 标题1 (Ctrl+1)
│   ├── 标题2 (Ctrl+2)
│   └── 标题3 (Ctrl+3)
└── 清除格式

工具(T)
├── 拼写检查 (F7)
├── 字数统计...
├── 查找重复内容...
├── ──────────
├── 版本管理
│   ├── 查看历史版本...
│   ├── 创建版本快照...
│   └── 恢复版本...
├── ──────────
├── 批量操作
│   ├── 批量重命名...
│   ├── 批量导出...
│   └── 批量格式化...
└── 选项... (Ctrl+,)

AI(A)
├── 内容生成
│   ├── 续写内容 (Ctrl+G)
│   ├── 改写内容 (Ctrl+R)
│   ├── 扩写内容 (Ctrl+E)
│   └── 生成灵感 (Ctrl+I)
├── 内容优化
│   ├── 风格统一...
│   ├── 去AI化处理...
│   ├── 逻辑检查...
│   └── 可读性优化...
├── ──────────
├── 模型设置...
├── 使用统计...
└── AI助手面板 (Ctrl+Shift+A)

帮助(H)
├── 用户手册 (F1)
├── 快捷键参考...
├── 视频教程...
├── ──────────
├── 检查更新...
├── 反馈建议...
└── 关于笔落App...
```

### 4.3 工具栏设计
#### 4.3.1 工具栏布局
```
┌─────────────────────────────────────────────────────────────┐
│ [新建] [打开] [保存] │ [撤销] [重做] │ [剪切] [复制] [粘贴] │  │
│                     │               │                     │  │
│ [查找] [替换] │ [粗体] [斜体] [下划线] │ [标题1] [标题2] │    │
│               │                       │                 │    │
│ [AI生成] [AI优化] [灵感] │ [角色] [场景] [事件] │ [设置]     │
└─────────────────────────────────────────────────────────────┘
```

#### 4.3.2 工具栏特性
- **分组显示**: 相关功能按组排列，用分隔符分开
- **图标+文字**: 重要功能显示图标和文字标签
- **悬停提示**: 鼠标悬停显示功能说明和快捷键
- **可定制**: 用户可以自定义工具栏内容和布局
- **响应式**: 窗口较小时自动隐藏部分功能或显示为下拉菜单

---

## 5. 侧边栏设计

### 5.1 故事结构面板
#### 5.1.1 三维度切换
```
┌─────────────────────────────────────┐
│ [大纲] [主线] [章节]                 │
├─────────────────────────────────────┤
│ 当前选择: 大纲维度                   │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📖 开端                          │ │
│ │   ├─ 世界观介绍                  │ │
│ │   ├─ 主角登场                    │ │
│ │   └─ 初始冲突                    │ │
│ │                                 │ │
│ │ 📈 发展                          │ │
│ │   ├─ 能力成长                    │ │
│ │   ├─ 伙伴聚集                    │ │
│ │   └─ 敌人出现                    │ │
│ │                                 │ │
│ │ ⚡ 高潮                          │ │
│ │   ├─ 最终对决                    │ │
│ │   └─ 转折点                      │ │
│ │                                 │ │
│ │ 🎯 结局                          │ │
│ │   ├─ 问题解决                    │ │
│ │   └─ 后续发展                    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [添加节点] [编辑] [删除]             │
└─────────────────────────────────────┘
```

#### 5.1.2 交互特性
- **标签切换**: 点击标签切换不同维度视图
- **树形结构**: 支持展开/折叠节点
- **拖拽排序**: 支持拖拽调整节点顺序
- **右键菜单**: 提供添加、编辑、删除等操作
- **进度指示**: 显示各节点的完成进度

### 5.2 元素管理面板
#### 5.2.1 角色管理
```
┌─────────────────────────────────────┐
│ 角色管理                [+新建角色]  │
├─────────────────────────────────────┤
│ 搜索: [____________] [🔍]           │
│                                     │
│ 主要角色                             │
│ ┌─────────────────────────────────┐ │
│ │ 👤 李逍遥 (主角)                 │ │
│ │    年龄: 18  性别: 男            │ │
│ │    出场: 15次                    │ │
│ │                                 │ │
│ │ 👤 赵灵儿 (女主)                 │ │
│ │    年龄: 16  性别: 女            │ │
│ │    出场: 12次                    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 次要角色                             │
│ ┌─────────────────────────────────┐ │
│ │ 👤 林月如                        │ │
│ │ 👤 阿奴                          │ │
│ │ 👤 酒剑仙                        │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [批量操作] [导入] [导出]             │
└─────────────────────────────────────┘
```

#### 5.2.2 场景和事件管理
- **场景管理**: 类似角色管理的界面结构
- **事件管理**: 按时间线排序显示事件
- **标签过滤**: 支持按标签筛选元素
- **关系视图**: 显示元素间的关联关系

---

## 6. 主编辑区设计

### 6.1 富文本编辑器
#### 6.1.1 编辑器界面
```
┌─────────────────────────────────────────────────────────────┐
│ 第一章 初入江湖                                    [章节设置] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│     月黑风高夜，正是杀人时。                                 │
│                                                             │
│     李逍遥手持长剑，悄悄潜入敌营。他知道今夜是救出师父的     │
│ 最后机会，如果失败了，师父就会被送到更远的地方，到时候再     │
│ 想救人就难如登天了。                                         │
│                                                             │
│     "谁在那里？"一声厉喝传来。                               │
│                                                             │
│     李逍遥心中一惊，难道被发现了？他屏住呼吸，紧贴在墙       │
│ 角，等待着...                                               │
│                                                             │
│ ┌─────────────────────────────────────────────────────┐     │
│ │ 💡 AI建议: 可以在这里添加更多的环境描述，增强紧张感  │     │
│ └─────────────────────────────────────────────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 6.1.2 编辑器特性
- **实时保存**: 自动保存用户输入内容
- **语法高亮**: 对话、描述、动作等不同类型内容高亮显示
- **智能提示**: AI提供写作建议和改进提示
- **字数统计**: 实时显示字数、段落数等统计信息
- **格式工具**: 支持基本的文本格式化功能

### 6.2 章节导航
#### 6.2.1 章节标签页
```
┌─────────────────────────────────────────────────────────────┐
│ [第一章] [第二章] [第三章] [+] [▼]                           │
├─────────────────────────────────────────────────────────────┤
│                     章节内容区域                             │
└─────────────────────────────────────────────────────────────┘
```

#### 6.2.2 导航特性
- **标签页**: 支持多章节同时打开
- **拖拽排序**: 支持拖拽调整章节顺序
- **右键菜单**: 提供关闭、重命名等操作
- **快捷键**: 支持 Ctrl+Tab 切换章节

---

## 7. AI交互区设计

### 7.1 AI对话界面
#### 7.1.1 对话区域
```
┌─────────────────────────────────────────────────────────────┐
│ AI助手                                          [模型: GPT-4] │
├─────────────────────────────────────────────────────────────┤
│ 🤖 你好！我是你的写作助手，有什么可以帮助你的吗？            │
│                                                             │
│ 👤 帮我续写一段李逍遥潜入敌营的情节                          │
│                                                             │
│ 🤖 好的，基于你当前的故事情节，我建议这样续写：              │
│                                                             │
│    就在这时，一个黑影从屋顶跃下，正好落在李逍遥面前。那     │
│    人身穿夜行衣，面罩遮面，手中寒光闪闪的匕首直指李逍遥     │
│    的咽喉。                                                 │
│                                                             │
│    "小子，你是什么人？为何深夜潜入此地？"那人声音低沉，     │
│    透着杀意。                                               │
│                                                             │
│ [采用] [修改] [重新生成] [添加到正文]                        │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 输入: [请输入你的需求...                    ] [发送] [🎤]    │
│                                                             │
│ 快捷功能: [续写] [改写] [扩写] [灵感] [优化] [翻译]          │
└─────────────────────────────────────────────────────────────┘
```

#### 7.1.2 交互特性
- **对话历史**: 保存完整的对话记录
- **快捷功能**: 提供常用的AI功能快捷按钮
- **语音输入**: 支持语音转文字输入
- **结果操作**: 对AI生成的内容提供多种操作选项
- **上下文感知**: AI能够理解当前编辑的内容上下文

### 7.2 AI功能面板
#### 7.2.1 功能分类
```
┌─────────────────────────────────────┐
│ 内容生成                             │
│ ┌─────────────────────────────────┐ │
│ │ [续写内容] [改写内容] [扩写内容]  │ │
│ │ [生成对话] [生成描述] [生成情节]  │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 内容优化                             │
│ ┌─────────────────────────────────┐ │
│ │ [风格统一] [去AI化] [逻辑检查]    │ │
│ │ [语法检查] [可读性] [情感分析]    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 创作辅助                             │
│ ┌─────────────────────────────────┐ │
│ │ [灵感生成] [角色分析] [情节建议]  │ │
│ │ [冲突设计] [结局预测] [主题分析]  │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 模型设置                             │
│ ┌─────────────────────────────────┐ │
│ │ 当前模型: GPT-4                  │ │
│ │ 温度: ████████░░ 0.8             │ │
│ │ 长度: ████████░░ 500             │ │
│ │ [高级设置...]                    │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

---

## 8. 状态栏设计

### 8.1 状态信息显示
```
┌─────────────────────────────────────────────────────────────┐
│ 就绪 │ 字数: 12,345 │ 段落: 56 │ 章节: 第3章 │ 保存: 已保存 │
│      │              │          │            │ 2分钟前      │
│      │ 选中: 123字  │ 行: 45   │ 列: 12     │ AI: 连接正常 │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 状态栏功能
- **应用状态**: 显示当前应用状态（就绪、保存中、AI生成中等）
- **文档统计**: 实时显示字数、段落数、行列位置等
- **保存状态**: 显示最后保存时间和自动保存状态
- **AI状态**: 显示AI服务连接状态和使用情况
- **进度指示**: 长时间操作时显示进度条

---

## 9. 设置界面设计

### 9.1 设置对话框布局
```
┌─────────────────────────────────────────────────────────────┐
│ 设置                                                × 关闭  │
├─────────────────────────────────────────────────────────────┤
│ 分类导航     │                设置内容区域                   │
│ ┌─────────┐ │ ┌─────────────────────────────────────────┐   │
│ │ 通用设置 │ │ │                                         │   │
│ │ AI设置   │ │ │              当前选择的                  │   │
│ │ 编辑器   │ │ │              设置选项内容                │   │
│ │ 外观主题 │ │ │                                         │   │
│ │ 快捷键   │ │ │                                         │   │
│ │ 数据管理 │ │ │                                         │   │
│ │ 高级选项 │ │ │                                         │   │
│ └─────────┘ │ └─────────────────────────────────────────┘   │
│             │                                               │
├─────────────┼─────────────────────────────────────────────┤
│             │              [重置] [应用] [确定] [取消]       │
└─────────────┴─────────────────────────────────────────────┘
```

### 9.2 具体设置页面

#### 9.2.1 通用设置
```
通用设置
┌─────────────────────────────────────────────────────────┐
│ 启动设置                                                 │
│ ☑ 启动时自动打开最后使用的项目                           │
│ ☑ 启动时检查更新                                         │
│ ☐ 启动时显示欢迎界面                                     │
│                                                         │
│ 自动保存                                                 │
│ ☑ 启用自动保存                                           │
│ 保存间隔: [30] 秒                                        │
│ 保存位置: [项目目录/.bamboo/autosave/]                   │
│                                                         │
│ 语言和地区                                               │
│ 界面语言: [简体中文 ▼]                                   │
│ 日期格式: [YYYY-MM-DD ▼]                                │
│ 时间格式: [24小时制 ▼]                                   │
│                                                         │
│ 性能设置                                                 │
│ 内存缓存大小: [████████░░] 100MB                        │
│ 磁盘缓存大小: [████████░░] 500MB                        │
│ ☑ 启用硬件加速                                           │
└─────────────────────────────────────────────────────────┘
```

#### 9.2.2 AI设置
```
AI设置
┌─────────────────────────────────────────────────────────┐
│ 服务商配置                                               │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ OpenAI                                    [已配置] │ │
│ │ API密钥: sk-****************************           │ │
│ │ 默认模型: [gpt-4 ▼]                                │ │
│ │ [测试连接] [编辑] [删除]                            │ │
│ │                                                   │ │
│ │ DeepSeek                                  [未配置] │ │
│ │ [添加配置]                                         │ │
│ │                                                   │ │
│ │ 智谱AI                                    [未配置] │ │
│ │ [添加配置]                                         │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 使用设置                                                 │
│ 默认服务商: [OpenAI ▼]                                   │
│ 生成温度: [████████░░] 0.8                              │
│ 最大长度: [████████░░] 500 tokens                       │
│ 请求超时: [30] 秒                                        │
│                                                         │
│ 配额管理                                                 │
│ ☑ 启用使用限制                                           │
│ 每日最大请求: [100] 次                                   │
│ 每月最大费用: [50] 美元                                  │
│ ☑ 费用预警 (达到80%时提醒)                               │
└─────────────────────────────────────────────────────────┘
```

#### 9.2.3 外观主题
```
外观主题
┌─────────────────────────────────────────────────────────┐
│ 主题选择                                                 │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐         │
│ │ 默认主题 │ │ 深色主题 │ │ 护眼主题 │ │ 自定义   │         │
│ │ ■■■■■ │ │ ████████ │ │ ████████ │ │ ████████ │         │
│ │ ■■■■■ │ │ ████████ │ │ ████████ │ │ ████████ │         │
│ │ [选择]   │ │ [选择]   │ │ [选择]   │ │ [编辑]   │         │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘         │
│                                                         │
│ 字体设置                                                 │
│ 编辑器字体: [Microsoft YaHei ▼] 大小: [14 ▼]            │
│ 界面字体: [Microsoft YaHei ▼] 大小: [12 ▼]              │
│ 代码字体: [Consolas ▼] 大小: [12 ▼]                     │
│                                                         │
│ 颜色设置                                                 │
│ 背景颜色: [████] #FFFFFF                                │
│ 文字颜色: [████] #000000                                │
│ 选中颜色: [████] #0078D4                                │
│ 链接颜色: [████] #0066CC                                │
│                                                         │
│ 布局设置                                                 │
│ 侧边栏宽度: [████████░░] 250px                          │
│ 工具栏大小: [标准 ▼]                                     │
│ ☑ 显示行号                                               │
│ ☑ 显示标尺                                               │
└─────────────────────────────────────────────────────────┘
```

---

## 10. 响应式设计

### 10.1 屏幕适配

#### 10.1.1 断点设计
```python
# 响应式断点
RESPONSIVE_BREAKPOINTS = {
    'xs': 1024,    # 小屏幕 (最小支持)
    'sm': 1280,    # 小屏幕
    'md': 1440,    # 中等屏幕 (推荐)
    'lg': 1920,    # 大屏幕
    'xl': 2560     # 超大屏幕
}

# 布局适配规则
LAYOUT_ADAPTATIONS = {
    'xs': {
        'sidebar': {'width': '200px', 'collapsible': True},
        'toolbar': {'compact': True, 'icon_only': True},
        'ai_panel': {'height': '200px', 'collapsible': True},
        'font_scale': 0.9
    },
    'sm': {
        'sidebar': {'width': '220px', 'collapsible': True},
        'toolbar': {'compact': True, 'icon_text': True},
        'ai_panel': {'height': '250px', 'collapsible': False},
        'font_scale': 1.0
    },
    'md': {
        'sidebar': {'width': '250px', 'collapsible': False},
        'toolbar': {'compact': False, 'full_text': True},
        'ai_panel': {'height': '300px', 'collapsible': False},
        'font_scale': 1.0
    },
    'lg': {
        'sidebar': {'width': '300px', 'collapsible': False},
        'toolbar': {'compact': False, 'full_text': True},
        'ai_panel': {'height': '350px', 'collapsible': False},
        'font_scale': 1.1
    }
}
```

#### 10.1.2 自适应布局
- **流式布局**: 主要区域使用百分比宽度
- **弹性容器**: 使用Flexbox布局适应不同尺寸
- **可折叠面板**: 小屏幕时自动折叠次要面板
- **响应式字体**: 根据屏幕大小调整字体大小

### 10.2 窗口管理

#### 10.2.1 多窗口支持
```
主窗口 (创作界面)
├── 子窗口1 (角色编辑器)
├── 子窗口2 (场景编辑器)
├── 子窗口3 (AI助手)
└── 子窗口4 (项目统计)
```

#### 10.2.2 窗口特性
- **独立窗口**: 支持将面板拆分为独立窗口
- **多显示器**: 支持多显示器扩展
- **窗口记忆**: 记住窗口位置和大小
- **快速切换**: 支持Alt+Tab在窗口间切换

---

## 11. 交互设计

### 11.1 快捷键系统

#### 11.1.1 全局快捷键
```
文件操作:
Ctrl+N          新建项目
Ctrl+O          打开项目
Ctrl+S          保存项目
Ctrl+Shift+S    另存为
Ctrl+W          关闭当前标签
Ctrl+Q          退出应用

编辑操作:
Ctrl+Z          撤销
Ctrl+Y          重做
Ctrl+X          剪切
Ctrl+C          复制
Ctrl+V          粘贴
Ctrl+A          全选
Ctrl+F          查找
Ctrl+H          替换

格式操作:
Ctrl+B          粗体
Ctrl+I          斜体
Ctrl+U          下划线
Ctrl+1/2/3      标题1/2/3

AI操作:
Ctrl+G          AI生成
Ctrl+R          AI改写
Ctrl+E          AI扩写
Ctrl+Shift+A    AI助手面板

视图操作:
F9              显示/隐藏侧边栏
F11             专注模式
F12             全屏模式
Ctrl++          放大
Ctrl+-          缩小
Ctrl+0          重置缩放

导航操作:
Ctrl+Tab        切换标签页
Ctrl+1-9        快速切换到第N个标签
Alt+Left        后退
Alt+Right       前进
```

#### 11.1.2 自定义快捷键
- **快捷键编辑器**: 提供图形化的快捷键设置界面
- **冲突检测**: 自动检测和解决快捷键冲突
- **导入导出**: 支持快捷键配置的导入导出
- **重置功能**: 一键重置为默认快捷键

### 11.2 拖拽操作

#### 11.2.1 支持的拖拽操作
```
文件拖拽:
- 拖拽文件到应用窗口 → 导入文件
- 拖拽图片到编辑器 → 插入图片
- 拖拽文本文件 → 导入内容

元素拖拽:
- 拖拽角色到章节 → 添加角色到章节
- 拖拽场景到章节 → 设置章节场景
- 拖拽事件到时间线 → 调整事件顺序

界面拖拽:
- 拖拽标签页 → 重新排序
- 拖拽面板 → 调整布局
- 拖拽分割线 → 调整面板大小
```

#### 11.2.2 拖拽反馈
- **视觉反馈**: 拖拽时显示半透明预览
- **目标高亮**: 有效拖拽目标高亮显示
- **禁止图标**: 无效拖拽显示禁止图标
- **自动滚动**: 拖拽到边缘时自动滚动

### 11.3 右键菜单

#### 11.3.1 上下文菜单
```
编辑器右键菜单:
├── 撤销 (Ctrl+Z)
├── 重做 (Ctrl+Y)
├── ──────────
├── 剪切 (Ctrl+X)
├── 复制 (Ctrl+C)
├── 粘贴 (Ctrl+V)
├── ──────────
├── 全选 (Ctrl+A)
├── 查找 (Ctrl+F)
├── ──────────
├── AI生成
│   ├── 续写内容
│   ├── 改写内容
│   └── 生成灵感
├── ──────────
├── 插入
│   ├── 插入角色
│   ├── 插入场景
│   └── 插入事件
└── 段落格式...

角色列表右键菜单:
├── 编辑角色...
├── 复制角色
├── 删除角色
├── ──────────
├── 查看关系图
├── 查看出场统计
├── ──────────
├── 导出角色信息
└── 添加到当前章节
```

---

## 12. 无障碍设计

### 12.1 可访问性支持

#### 12.1.1 键盘导航
- **Tab导航**: 支持Tab键在所有可交互元素间导航
- **快捷键**: 为所有主要功能提供键盘快捷键
- **焦点指示**: 清晰的焦点指示器
- **跳过链接**: 提供跳过导航的快捷方式

#### 12.1.2 屏幕阅读器支持
- **语义标记**: 使用正确的HTML语义标签
- **ARIA标签**: 为复杂组件添加ARIA标签
- **文本替代**: 为图像和图标提供文本描述
- **状态通知**: 重要状态变化的语音通知

### 12.2 视觉辅助

#### 12.2.1 高对比度模式
```python
HIGH_CONTRAST_THEME = {
    'background': '#000000',
    'text': '#FFFFFF',
    'accent': '#FFFF00',
    'border': '#FFFFFF',
    'selection': '#0078D4',
    'error': '#FF0000',
    'success': '#00FF00',
    'warning': '#FFA500'
}
```

#### 12.2.2 字体缩放
- **缩放范围**: 50% - 200%
- **保持布局**: 缩放时保持界面布局不变
- **快捷操作**: Ctrl+滚轮快速缩放
- **记忆设置**: 记住用户的缩放偏好

---

## 13. 国际化设计

### 13.1 多语言支持

#### 13.1.1 支持语言
```python
SUPPORTED_LANGUAGES = {
    'zh_CN': '简体中文',
    'zh_TW': '繁体中文',
    'en_US': 'English (US)',
    'en_GB': 'English (UK)',
    'ja_JP': '日本語',
    'ko_KR': '한국어'
}
```

#### 13.1.2 本地化要素
- **界面文本**: 所有界面文本支持多语言
- **日期时间**: 根据地区显示日期时间格式
- **数字格式**: 支持不同的数字和货币格式
- **文本方向**: 支持从右到左的文本方向

### 13.2 文化适配

#### 13.2.1 字体选择
```python
FONT_FAMILIES = {
    'zh_CN': ['Microsoft YaHei', 'SimHei', 'SimSun'],
    'zh_TW': ['Microsoft JhengHei', 'PMingLiU', 'MingLiU'],
    'en_US': ['Segoe UI', 'Arial', 'Helvetica'],
    'ja_JP': ['Yu Gothic', 'Meiryo', 'MS Gothic'],
    'ko_KR': ['Malgun Gothic', 'Dotum', 'Gulim']
}
```

#### 13.2.2 颜色文化
- **中文**: 红色表示喜庆，金色表示富贵
- **西方**: 绿色表示成功，红色表示错误
- **日韩**: 考虑传统色彩的文化含义

---

## 14. 性能优化

### 14.1 界面性能

#### 14.1.1 渲染优化
- **虚拟滚动**: 大列表使用虚拟滚动技术
- **延迟加载**: 非可见区域延迟渲染
- **缓存机制**: 缓存渲染结果避免重复计算
- **批量更新**: 批量处理DOM更新操作

#### 14.1.2 内存管理
- **对象池**: 重用频繁创建的对象
- **事件清理**: 及时清理事件监听器
- **图片优化**: 自动压缩和缓存图片
- **内存监控**: 监控内存使用情况

### 14.2 用户体验优化

#### 14.2.1 加载优化
- **启动画面**: 显示品牌启动画面
- **进度指示**: 长时间操作显示进度
- **预加载**: 预加载可能需要的资源
- **骨架屏**: 内容加载时显示骨架屏

#### 14.2.2 交互优化
- **防抖处理**: 防止重复点击和输入
- **即时反馈**: 操作后立即显示反馈
- **平滑动画**: 使用CSS动画提升体验
- **错误恢复**: 优雅处理错误情况

---

## 15. 总结

### 15.1 设计亮点
1. **三维度故事管理**: 创新的大纲→主线→章节结构管理
2. **AI深度集成**: 无缝的AI辅助创作体验
3. **专业编辑器**: 功能丰富的富文本编辑器
4. **响应式设计**: 适配不同屏幕尺寸的灵活布局
5. **无障碍支持**: 考虑特殊用户群体的访问需求

### 15.2 实施建议
1. **原型制作**: 先制作高保真原型进行用户测试
2. **组件化开发**: 采用组件化方式开发界面
3. **设计系统**: 建立统一的设计系统和组件库
4. **用户测试**: 定期进行用户体验测试和优化
5. **持续改进**: 根据用户反馈持续改进界面设计

### 15.3 技术要求
- **PyQt6**: 使用PyQt6作为主要GUI框架
- **QML**: 复杂界面使用QML进行开发
- **样式表**: 使用QSS进行界面美化
- **图标库**: 使用矢量图标库确保清晰度
- **动画效果**: 适度使用动画提升用户体验

---

*本文档版本: v2.0*
*最后更新: 2025-09-12*