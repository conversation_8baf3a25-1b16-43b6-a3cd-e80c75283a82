# 笔落 - 用户手册

## 📖 目录

1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [项目管理](#项目管理)
4. [创作功能](#创作功能)
5. [AI辅助](#ai辅助)
6. [角色管理](#角色管理)
7. [场景管理](#场景管理)
8. [事件管理](#事件管理)
9. [设置配置](#设置配置)
10. [导出功能](#导出功能)
11. [常见问题](#常见问题)

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11 (64位)
- **内存**: 最低 4GB RAM，推荐 8GB 或以上
- **存储空间**: 至少 500MB 可用空间
- **网络**: 使用AI功能需要互联网连接

### 安装步骤

1. **下载应用程序**
   - 从官方网站下载最新版本的安装包
   - 解压ZIP文件到您选择的目录

2. **运行应用程序**
   - 双击 `笔落.exe` 启动应用程序
   - 首次启动可能需要几秒钟时间

3. **创建第一个项目**
   - 点击"新建项目"按钮
   - 填写项目基本信息
   - 开始您的创作之旅

## 🖥️ 界面介绍

### 主界面布局

笔落采用现代化的多面板布局设计：

```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏 [文件] [编辑] [视图] [工具] [帮助]                │
├─────────────────────────────────────────────────────────┤
│ 工具栏 [新建] [打开] [保存] [导出] [设置]               │
├─────────────┬─────────────────────┬─────────────────────┤
│             │                     │                     │
│   项目树    │      编辑器区域      │    AI助手面板      │
│             │                     │                     │
│ - 角色管理  │   [章节标题]        │  💬 对话界面       │
│ - 场景管理  │   正文编辑区域      │  🎯 内容生成       │
│ - 事件管理  │                     │  ✨ 内容优化       │
│ - 章节大纲  │   [实时字数统计]    │  ⚙️ 参数设置       │
│             │                     │                     │
├─────────────┴─────────────────────┴─────────────────────┤
│ 状态栏 [项目状态] [字数统计] [AI状态] [保存状态]        │
└─────────────────────────────────────────────────────────┘
```

### 核心功能区域

1. **项目树面板** (左侧)
   - 项目结构导航
   - 角色、场景、事件管理
   - 章节大纲组织

2. **编辑器区域** (中央)
   - 富文本编辑器
   - 实时字数统计
   - 章节管理

3. **AI助手面板** (右侧)
   - 智能对话界面
   - 内容生成工具
   - 写作建议

## 📁 项目管理

### 创建新项目

1. **基本信息设置**
   ```
   项目名称: 我的小说
   作者姓名: 您的姓名
   项目类型: 奇幻/现代/科幻/历史/言情/悬疑
   项目描述: 简短的故事概述
   ```

2. **高级设置**
   - 字数目标: 设置总体字数目标
   - 每日目标: 设置每日写作字数
   - 自动保存: 配置自动保存间隔
   - 备份设置: 启用自动备份功能

### 项目结构

```
我的小说项目/
├── 项目设置
├── 角色档案/
│   ├── 主角
│   ├── 配角
│   └── 反派
├── 场景设定/
│   ├── 主要场景
│   └── 次要场景
├── 事件时间线/
│   ├── 开端事件
│   ├── 发展事件
│   └── 高潮事件
└── 章节内容/
    ├── 第一章
    ├── 第二章
    └── ...
```

## ✍️ 创作功能

### 富文本编辑器

笔落提供强大的富文本编辑功能：

- **文本格式化**: 粗体、斜体、下划线、删除线
- **段落设置**: 对齐方式、行间距、段落间距
- **字体设置**: 字体族、字号、颜色
- **特殊功能**: 查找替换、拼写检查、字数统计

### 章节管理

1. **创建章节**
   - 右键点击章节列表
   - 选择"新建章节"
   - 输入章节标题

2. **章节操作**
   - **重命名**: 双击章节名称
   - **移动**: 拖拽调整章节顺序
   - **删除**: 右键选择删除
   - **复制**: 复制章节内容

3. **章节导航**
   - 点击章节名称快速跳转
   - 使用快捷键 Ctrl+PageUp/PageDown 切换章节

### 实时统计

编辑器底部显示实时统计信息：
- **当前章节**: 字数、段落数、页数
- **整体项目**: 总字数、完成进度、预计完成时间
- **写作进度**: 今日字数、本周字数、月度统计

## 🤖 AI辅助

### AI服务配置

笔落支持多个AI服务商：

1. **OpenAI (ChatGPT)**
   - 模型: GPT-4, GPT-3.5-turbo
   - 特点: 创意丰富，语言自然

2. **DeepSeek**
   - 模型: DeepSeek-Chat
   - 特点: 中文优化，逻辑性强

3. **智谱AI (GLM)**
   - 模型: GLM-4
   - 特点: 中文理解好，文学性强

4. **Anthropic (Claude)**
   - 模型: Claude-3-Haiku
   - 特点: 安全可靠，分析能力强

### AI功能使用

#### 1. 内容生成

**角色描述生成**
```
输入: "一个神秘的魔法师"
AI输出: 
"艾瑞克是一位年约四十的魔法师，身材高瘦，总是穿着深蓝色的法袍。
他的眼睛是罕见的银色，仿佛能看透人心。长长的黑发总是随意地束在
脑后，给人一种不羁的感觉。他精通元素魔法，尤其擅长操控雷电..."
```

**场景描述生成**
```
输入: "古老的图书馆"
AI输出:
"这座图书馆建于三百年前，高大的书架直达天花板，上面摆满了各种
古籍。微弱的烛光在书架间摇曳，投下长长的影子。空气中弥漫着羊皮
纸和墨水的味道，偶尔能听到老鼠在角落里窸窸窣窣的声音..."
```

#### 2. 情节发展

- **剧情推进**: 根据当前情节提供下一步发展建议
- **冲突设计**: 生成角色间的冲突和矛盾
- **转折点**: 创造意想不到的剧情转折
- **结局构思**: 提供多种结局可能性

#### 3. 对话生成

```
场景: 主角与反派的对峙
输入: "生成一段紧张的对话"

AI输出:
"你以为你赢了吗？"反派冷笑着说道。
"至少我阻止了你的计划。"主角紧握着剑柄。
"阻止？不，你只是延缓了不可避免的结果。"
"那我们就拭目以待吧。"
```

#### 4. 内容优化

- **语言润色**: 改善文字表达和流畅度
- **情感增强**: 加强情感表达的力度
- **细节丰富**: 添加生动的细节描述
- **风格统一**: 保持全文风格一致性

### AI参数调整

在AI设置面板中，您可以调整以下参数：

- **创意度 (Temperature)**: 0.1-1.0
  - 低值: 更保守、一致的输出
  - 高值: 更有创意、多样的输出

- **最大长度 (Max Tokens)**: 100-4000
  - 控制AI生成内容的长度

- **重试次数**: 1-5
  - 网络异常时的重试次数

## 👥 角色管理

### 创建角色档案

1. **基本信息**
   ```
   姓名: 角色的全名
   别名: 昵称或代号
   年龄: 具体年龄或年龄段
   性别: 男/女/其他
   职业: 角色的职业或身份
   ```

2. **外貌特征**
   ```
   身高体重: 具体数值或描述
   发色眼色: 详细的外貌特征
   特殊标记: 疤痕、纹身、胎记等
   穿着风格: 日常的着装偏好
   ```

3. **性格特质**
   - 主要性格: 选择3-5个核心特质
   - 优点缺点: 平衡的性格设定
   - 恐惧与欲望: 深层心理动机
   - 说话方式: 语言习惯和口头禅

4. **背景故事**
   ```
   出生地: 角色的出生地和成长环境
   家庭背景: 家庭成员和关系
   教育经历: 学习和训练经历
   重要事件: 影响角色的关键事件
   ```

### 角色关系图

笔落提供可视化的角色关系图：

```
    [主角] ←─恋人─→ [女主角]
       ↑                ↓
    师父关系          朋友关系
       ↑                ↓
    [导师] ←─对手─→ [反派角色]
```

- **关系类型**: 亲情、友情、爱情、师徒、敌对等
- **关系强度**: 1-10级，表示关系的紧密程度
- **关系变化**: 记录关系在故事中的发展变化

### 角色出场统计

系统自动统计每个角色的出场情况：
- **出场章节**: 角色出现的所有章节
- **出场频率**: 在整个故事中的活跃度
- **台词统计**: 对话数量和字数
- **重要程度**: 基于出场频率的重要性评级

## 🏞️ 场景管理

### 场景设定

1. **位置信息**
   ```
   场景名称: 场景的名称
   位置类型: 室内/室外/虚拟空间
   地理位置: 具体的地理坐标或描述
   所属区域: 城市、国家、世界设定
   ```

2. **环境描述**
   ```
   物理环境: 建筑结构、地形地貌
   气候条件: 天气、季节、温度
   光线条件: 明暗、光源、色彩
   声音环境: 背景音、特殊声效
   ```

3. **氛围设定**
   - **情绪氛围**: 紧张、温馨、神秘、恐怖等
   - **文化氛围**: 现代、古典、异域、科幻等
   - **社会氛围**: 繁华、荒凉、和谐、混乱等

### 场景关联

- **角色关联**: 记录在此场景出现的角色
- **事件关联**: 发生在此场景的重要事件
- **时间关联**: 场景在不同时间的状态变化
- **情节关联**: 场景在整体情节中的作用

## ⚡ 事件管理

### 事件创建

1. **基本信息**
   ```
   事件名称: 事件的简短描述
   事件类型: 开端/发展/转折/高潮/结局
   重要程度: 1-10级重要性评级
   发生时间: 在故事时间线中的位置
   ```

2. **事件详情**
   ```
   参与角色: 涉及的主要角色
   发生地点: 事件发生的场景
   事件起因: 导致事件发生的原因
   事件经过: 详细的事件过程
   事件结果: 事件的结果和影响
   ```

3. **影响评估**
   - **情节影响**: 对主线情节的推进作用
   - **角色影响**: 对角色发展的影响
   - **世界影响**: 对故事世界的改变
   - **主题影响**: 对主题表达的贡献

### 时间线管理

```
故事开始 ─→ 事件A ─→ 事件B ─→ 转折点 ─→ 高潮 ─→ 结局
    ↓         ↓         ↓         ↓         ↓       ↓
  第1章    第2-3章   第4-5章   第6-7章   第8章   第9章
```

- **线性时间线**: 按时间顺序排列的事件
- **并行事件**: 同时发生的多个事件
- **回忆插叙**: 非线性的时间结构
- **预示伏笔**: 为后续事件埋下的线索

## ⚙️ 设置配置

### 编辑器设置

1. **外观设置**
   ```
   主题: 明亮/暗黑/护眼
   字体: 微软雅黑/宋体/自定义
   字号: 12-24pt
   行间距: 1.0-2.0倍
   ```

2. **编辑行为**
   ```
   自动保存: 1-30分钟间隔
   自动备份: 启用/禁用
   拼写检查: 启用/禁用
   智能标点: 启用/禁用
   ```

### AI服务设置

1. **服务商选择**
   - 默认AI服务商
   - 备用服务商设置
   - 服务商优先级

2. **API配置**
   ```
   API密钥: 您的API密钥
   服务地址: API服务器地址
   超时设置: 30-120秒
   重试次数: 1-5次
   ```

3. **生成参数**
   ```
   创意度: 0.1-1.0
   最大长度: 100-4000 tokens
   温度参数: 控制输出随机性
   ```

### 导出设置

1. **格式选项**
   - TXT: 纯文本格式
   - DOCX: Word文档格式
   - PDF: PDF文档格式
   - EPUB: 电子书格式

2. **导出内容**
   ```
   包含元数据: 作者、标题等信息
   包含目录: 自动生成章节目录
   包含统计: 字数、章节数等统计
   ```

## 📤 导出功能

### 支持格式

1. **TXT格式**
   - 纯文本，兼容性最好
   - 适合进一步编辑和处理
   - 文件小，传输方便

2. **DOCX格式**
   - Microsoft Word格式
   - 保留格式和样式
   - 便于进一步编辑

3. **PDF格式**
   - 适合阅读和打印
   - 格式固定，跨平台兼容
   - 支持目录和书签

4. **EPUB格式**
   - 标准电子书格式
   - 适合电子阅读器
   - 支持响应式布局

### 导出选项

```
导出范围:
□ 整个项目
□ 选定章节
□ 当前章节

包含内容:
☑ 正文内容
☑ 章节标题
☑ 作者信息
□ 角色档案
□ 场景设定
□ 创作笔记

格式设置:
字体: 宋体
字号: 12pt
行间距: 1.5倍
页边距: 2.5cm
```

## ❓ 常见问题

### 安装和启动

**Q: 应用程序无法启动怎么办？**
A: 
1. 确认系统满足最低要求
2. 以管理员身份运行
3. 检查防病毒软件是否误报
4. 重新下载安装包

**Q: 启动速度很慢怎么办？**
A:
1. 关闭不必要的后台程序
2. 确保有足够的内存空间
3. 将应用添加到防病毒软件白名单

### 使用问题

**Q: AI功能无法使用？**
A:
1. 检查网络连接
2. 确认API密钥正确
3. 检查API服务商状态
4. 尝试切换其他服务商

**Q: 文件保存失败？**
A:
1. 检查磁盘空间
2. 确认文件夹权限
3. 尝试另存为其他位置
4. 重启应用程序

**Q: 如何备份我的作品？**
A:
1. 使用"文件→导出项目"功能
2. 定期复制项目文件夹
3. 启用自动备份功能
4. 使用云存储同步

### 性能优化

**Q: 应用程序运行缓慢？**
A:
1. 关闭不需要的功能面板
2. 减少自动保存频率
3. 清理临时文件
4. 重启应用程序

**Q: 内存占用过高？**
A:
1. 关闭长时间不用的项目
2. 减少AI生成历史记录
3. 定期重启应用程序
4. 升级系统内存

---

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- **在线文档**: 访问官方文档网站
- **用户社区**: 加入用户交流群
- **邮件支持**: <EMAIL>
- **问题反馈**: 通过应用内反馈功能

感谢您选择笔落，祝您创作愉快！ ✍️✨
