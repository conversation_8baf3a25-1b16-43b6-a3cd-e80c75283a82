('E:\\project\\bamboofall_py_3\\dist\\笔落.exe',
 False,
 False,
 False,
 'D:\\python3\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 versioninfo.VSVersionInfo(ffi=versioninfo.FixedFileInfo(filevers=(1, 0, 0, 0), prodvers=(1, 0, 0, 0), mask=0x3f, flags=0x0, OS=0x4, fileType=1, subtype=0x0, date=(0, 0)), kids=[versioninfo.StringFileInfo([versioninfo.StringTable('040904B0', [versioninfo.StringStruct('CompanyName', 'BambooFall Team'), versioninfo.StringStruct('FileDescription', 'AI辅助小说创作工具'), versioninfo.StringStruct('FileVersion', '1.0.0'), versioninfo.StringStruct('InternalName', '笔落'), versioninfo.StringStruct('LegalCopyright', 'Copyright © 2025 BambooFall Team'), versioninfo.StringStruct('OriginalFilename', '笔落.exe'), versioninfo.StringStruct('ProductName', '笔落'), versioninfo.StringStruct('ProductVersion', '1.0.0')])]), versioninfo.VarFileInfo([versioninfo.VarStruct('Translation', [1033, 1200])])]),
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\project\\bamboofall_py_3\\build\\bamboofall\\笔落.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\python3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('main', 'E:\\project\\bamboofall_py_3\\src\\main.py', 'PYSOURCE'),
  ('python313.dll', 'D:\\python3\\python313.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\python3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\python3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\python3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python3\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python3\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_imagingft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'D:\\python3\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('jiter\\jiter.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\jiter\\jiter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd', 'D:\\python3\\DLLs\\_zoneinfo.pyd', 'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\python3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\python3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\python3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('mypy\\__init__.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\__init__.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\join.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\meet.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\meet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\erasetype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\erasetype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typevartuples.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typevartuples.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\subtypes.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\subtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\solve.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\solve.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\graph_utils.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\graph_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkmember.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checkmember.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\messages.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\messages.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typetraverser.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typetraverser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\operators.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\operators.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\errors.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\errors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\scope.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\scope.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\error_formatter.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\error_formatter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checker_shared.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checker_shared.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\message_registry.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\message_registry.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typestate.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typestate.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\types_utils.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\types_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checker_state.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checker_state.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\constraints.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\constraints.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\infer.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\infer.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\argmap.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\argmap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\applytype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\applytype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\type_visitor.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\type_visitor.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\state.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\state.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\maptype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\maptype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\expandtype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\expandtype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typeops.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typeops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\copytype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\copytype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\defaults.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\defaults.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\util.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typevars.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typevars.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\types.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\types.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\server\\trigger.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\server\\trigger.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\server\\__init__.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\server\\__init__.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\visitor.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\visitor.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\typeanal.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\typeanal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\tvar_scope.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\tvar_scope.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_typeddict.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal_typeddict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_shared.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal_shared.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_newtype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal_newtype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_namedtuple.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal_namedtuple.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\semanal_enum.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\semanal_enum.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\reachability.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\reachability.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\traverser.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\traverser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\literals.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\literals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\patterns.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\patterns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\mro.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\mro.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\exprtotype.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\exprtotype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\fastparse.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\fastparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\sharedparse.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\sharedparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\constant_fold.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\constant_fold.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\dataclasses.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\plugins\\dataclasses.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checker.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checker.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\treetransform.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\treetransform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkpattern.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checkpattern.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\binder.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\binder.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkexpr.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checkexpr.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\checkstrformat.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\checkstrformat.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\parse.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\parse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\common.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\plugins\\common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\fixup.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\fixup.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\lookup.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\lookup.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugins\\__init__.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\plugins\\__init__.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\plugin.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\plugin.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\options.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\nodes.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\nodes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\strconv.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\strconv.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('mypy\\errorcodes.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\mypy\\errorcodes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp313-win_amd64.pyd',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python3\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python3\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libffi-8.dll', 'D:\\python3\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\python3\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\python3\\DLLs\\libssl-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\python3\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('Docs\\开发实施计划_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\开发实施计划_v2.md',
   'DATA'),
  ('Docs\\开发者指南.md', 'E:\\project\\bamboofall_py_3\\Docs\\开发者指南.md', 'DATA'),
  ('Docs\\技术架构设计_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\技术架构设计_v2.md',
   'DATA'),
  ('Docs\\数据模型设计_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\数据模型设计_v2.md',
   'DATA'),
  ('Docs\\测试计划_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\测试计划_v2.md',
   'DATA'),
  ('Docs\\用户手册.md', 'E:\\project\\bamboofall_py_3\\Docs\\用户手册.md', 'DATA'),
  ('Docs\\界面设计规范_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\界面设计规范_v2.md',
   'DATA'),
  ('Docs\\笔落App需求文档.md',
   'E:\\project\\bamboofall_py_3\\Docs\\笔落App需求文档.md',
   'DATA'),
  ('Docs\\笔落交互界面设计文档.md',
   'E:\\project\\bamboofall_py_3\\Docs\\笔落交互界面设计文档.md',
   'DATA'),
  ('Docs\\编码规范和技术标准.md',
   'E:\\project\\bamboofall_py_3\\Docs\\编码规范和技术标准.md',
   'DATA'),
  ('Docs\\需求规格说明书_v2.md',
   'E:\\project\\bamboofall_py_3\\Docs\\需求规格说明书_v2.md',
   'DATA'),
  ('core\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\__init__.py',
   'DATA'),
  ('core\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\__init__.py',
   'DATA'),
  ('core\\ai\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\__pycache__\\base.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\__pycache__\\base.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\__pycache__\\manager.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\__pycache__\\manager.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__init__.py',
   'DATA'),
  ('core\\ai\\adapters\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\__pycache__\\anthropic_adapter.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__pycache__\\anthropic_adapter.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\__pycache__\\deepseek_adapter.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__pycache__\\deepseek_adapter.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\__pycache__\\openai_adapter.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__pycache__\\openai_adapter.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\__pycache__\\zhipu_adapter.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\__pycache__\\zhipu_adapter.cpython-313.pyc',
   'DATA'),
  ('core\\ai\\adapters\\anthropic_adapter.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\anthropic_adapter.py',
   'DATA'),
  ('core\\ai\\adapters\\deepseek_adapter.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\deepseek_adapter.py',
   'DATA'),
  ('core\\ai\\adapters\\openai_adapter.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\openai_adapter.py',
   'DATA'),
  ('core\\ai\\adapters\\zhipu_adapter.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\adapters\\zhipu_adapter.py',
   'DATA'),
  ('core\\ai\\base.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\base.py',
   'DATA'),
  ('core\\ai\\manager.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\ai\\manager.py',
   'DATA'),
  ('core\\events\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\events\\__init__.py',
   'DATA'),
  ('core\\models\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__init__.py',
   'DATA'),
  ('core\\models\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\base.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\base.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\character.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\character.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\enums.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\enums.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\event.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\event.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\project.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\project.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\scene.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\scene.cpython-313.pyc',
   'DATA'),
  ('core\\models\\__pycache__\\story_element.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\__pycache__\\story_element.cpython-313.pyc',
   'DATA'),
  ('core\\models\\base.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\base.py',
   'DATA'),
  ('core\\models\\character.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\character.py',
   'DATA'),
  ('core\\models\\enums.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\enums.py',
   'DATA'),
  ('core\\models\\event.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\event.py',
   'DATA'),
  ('core\\models\\project.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\project.py',
   'DATA'),
  ('core\\models\\scene.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\scene.py',
   'DATA'),
  ('core\\models\\story_element.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\models\\story_element.py',
   'DATA'),
  ('core\\storage\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__init__.py',
   'DATA'),
  ('core\\storage\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\backup.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\backup.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\cache.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\cache.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\file_storage.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\file_storage.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\manager.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\manager.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\serializer.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\serializer.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\__pycache__\\version_control.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\__pycache__\\version_control.cpython-313.pyc',
   'DATA'),
  ('core\\storage\\backup.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\backup.py',
   'DATA'),
  ('core\\storage\\cache.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\cache.py',
   'DATA'),
  ('core\\storage\\file_storage.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\file_storage.py',
   'DATA'),
  ('core\\storage\\manager.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\manager.py',
   'DATA'),
  ('core\\storage\\serializer.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\serializer.py',
   'DATA'),
  ('core\\storage\\version_control.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\storage\\version_control.py',
   'DATA'),
  ('core\\validation\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__init__.py',
   'DATA'),
  ('core\\validation\\__pycache__\\__init__.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\__pycache__\\business_rules.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\business_rules.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\__pycache__\\exceptions.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\exceptions.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\__pycache__\\relationship_validators.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\relationship_validators.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\__pycache__\\rules.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\rules.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\__pycache__\\validators.cpython-313.pyc',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\__pycache__\\validators.cpython-313.pyc',
   'DATA'),
  ('core\\validation\\business_rules.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\business_rules.py',
   'DATA'),
  ('core\\validation\\exceptions.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\exceptions.py',
   'DATA'),
  ('core\\validation\\relationship_validators.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\relationship_validators.py',
   'DATA'),
  ('core\\validation\\rules.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\rules.py',
   'DATA'),
  ('core\\validation\\validators.py',
   'E:\\project\\bamboofall_py_3\\src\\core\\validation\\validators.py',
   'DATA'),
  ('resources\\README.md',
   'E:\\project\\bamboofall_py_3\\resources\\README.md',
   'DATA'),
  ('resources\\styles\\optimized.qss',
   'E:\\project\\bamboofall_py_3\\resources\\styles\\optimized.qss',
   'DATA'),
  ('ui\\__init__.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\__init__.py',
   'DATA'),
  ('ui\\ai_assistant.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\ai_assistant.py',
   'DATA'),
  ('ui\\character_manager.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\character_manager.py',
   'DATA'),
  ('ui\\event_manager.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\event_manager.py',
   'DATA'),
  ('ui\\main_window.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\main_window.py',
   'DATA'),
  ('ui\\project_dialog.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\project_dialog.py',
   'DATA'),
  ('ui\\scene_manager.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\scene_manager.py',
   'DATA'),
  ('ui\\settings_dialog.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\settings_dialog.py',
   'DATA'),
  ('ui\\writing_interface.py',
   'E:\\project\\bamboofall_py_3\\src\\ui\\writing_interface.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zones', 'D:\\python3\\Lib\\site-packages\\tzdata\\zones', 'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\python3\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\python3\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE.BSD',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\REQUESTED',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\METADATA',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\INSTALLER',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\WHEEL',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\licenses\\LICENSE',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.6.dist-info\\RECORD',
   'D:\\python3\\Lib\\site-packages\\cryptography-45.0.6.dist-info\\RECORD',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'D:\\python3\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\REQUESTED',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\python3\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\python3\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\python3\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\python3\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\python3\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\python3\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('base_library.zip',
   'E:\\project\\bamboofall_py_3\\build\\bamboofall\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('runw.exe',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'D:\\python3\\python313.dll')
