"""数据模型模块

定义应用程序的核心数据模型，包括：
- 写作项目模型
- 故事元素模型
- 角色模型
- 场景模型
- 事件模型
- 章节模型
"""

# 导入枚举类型
from .enums import *

# 导入基础类
from .base import BaseModel, ValidationResult, ModelValidator, VersionedModel

# 导入核心模型
from .project import WritingProject, ProjectSettings, AIConfiguration, ProjectValidator
from .story_element import StoryElement, ElementRelationship, ElementVersion, StoryElementValidator

# 导入角色模型
from .character import (
    Character, CharacterAppearance, CharacterPersonality,
    CharacterBackground, CharacterRelationship, CharacterValidator
)

# 导入场景模型
from .scene import (
    Scene, SceneLocation, SceneTime, SceneEnvironment,
    SceneAtmosphere, SceneValidator
)

# 导入事件模型
from .event import (
    Event, EventParticipation, EventImpactData, EventValidator
)

__all__ = [
    # 枚举类型
    "ProjectType", "ProjectStatus", "ElementType", "ImportanceLevel",
    "Gender", "RelationType", "RelationshipStatus", "LocationType",
    "EventType", "EventCategory", "ParticipationRole", "PlotFunction",
    "ChapterStatus", "ReviewStatus", "ElementRole", "ChangeType",
    "SceneFunction", "EventImpact", "CharacterArcType", "TimeOfDay",
    "Season", "Weather",

    # 基础类
    "BaseModel", "ValidationResult", "ModelValidator", "VersionedModel",

    # 项目模型
    "WritingProject", "ProjectSettings", "AIConfiguration", "ProjectValidator",

    # 故事元素模型
    "StoryElement", "ElementRelationship", "ElementVersion", "StoryElementValidator",

    # 角色模型
    "Character", "CharacterAppearance", "CharacterPersonality",
    "CharacterBackground", "CharacterRelationship", "CharacterValidator",

    # 场景模型
    "Scene", "SceneLocation", "SceneTime", "SceneEnvironment",
    "SceneAtmosphere", "SceneValidator",

    # 事件模型
    "Event", "EventParticipation", "EventImpactData", "EventValidator",
]
