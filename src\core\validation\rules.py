"""字段验证规则

提供各种常用的字段验证规则实现
"""

import re
import os
from typing import Any, List, Optional, Union, Pattern
from uuid import UUID
from datetime import datetime
from enum import Enum
from urllib.parse import urlparse

from .validators import ValidationRule, ValidationContext


class StringValidationRule(ValidationRule):
    """字符串验证规则"""
    
    def __init__(
        self,
        name: str,
        min_length: Optional[int] = None,
        max_length: Optional[int] = None,
        pattern: Optional[Union[str, Pattern]] = None,
        allowed_chars: Optional[str] = None,
        forbidden_chars: Optional[str] = None,
        required: bool = False,
        strip_whitespace: bool = True
    ):
        super().__init__(name, f"字符串验证规则: {name}")
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = re.compile(pattern) if isinstance(pattern, str) else pattern
        self.allowed_chars = allowed_chars
        self.forbidden_chars = forbidden_chars
        self.required = required
        self.strip_whitespace = strip_whitespace
    
    def validate(self, value: Any, context: ValidationContext) -> bool:
        # 处理None值
        if value is None:
            return not self.required
        
        # 转换为字符串
        if not isinstance(value, str):
            value = str(value)
        
        # 去除空白字符
        if self.strip_whitespace:
            value = value.strip()
        
        # 检查是否为空
        if not value and self.required:
            return False
        
        # 如果值为空且不是必需的，则通过验证
        if not value and not self.required:
            return True
        
        # 长度验证
        if self.min_length is not None and len(value) < self.min_length:
            return False
        
        if self.max_length is not None and len(value) > self.max_length:
            return False
        
        # 模式验证
        if self.pattern and not self.pattern.match(value):
            return False
        
        # 允许字符验证
        if self.allowed_chars and not all(c in self.allowed_chars for c in value):
            return False
        
        # 禁止字符验证
        if self.forbidden_chars and any(c in self.forbidden_chars for c in value):
            return False
        
        return True
    
    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        if value is None and self.required:
            return f"字段不能为空"
        
        if isinstance(value, str):
            if self.strip_whitespace:
                value = value.strip()
            
            if self.min_length is not None and len(value) < self.min_length:
                return f"长度不能少于{self.min_length}个字符"
            
            if self.max_length is not None and len(value) > self.max_length:
                return f"长度不能超过{self.max_length}个字符"
            
            if self.pattern and not self.pattern.match(value):
                return f"格式不正确"
            
            if self.forbidden_chars and any(c in self.forbidden_chars for c in value):
                return f"包含非法字符"
        
        return super().get_error_message(value, context)


class NumericValidationRule(ValidationRule):
    """数值验证规则"""
    
    def __init__(
        self,
        name: str,
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        integer_only: bool = False,
        positive_only: bool = False,
        allow_zero: bool = True
    ):
        super().__init__(name, f"数值验证规则: {name}")
        self.min_value = min_value
        self.max_value = max_value
        self.integer_only = integer_only
        self.positive_only = positive_only
        self.allow_zero = allow_zero
    
    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True
        
        # 尝试转换为数值
        try:
            if self.integer_only:
                # 对于整数验证，先转换为float检查是否为整数
                float_value = float(value)
                if float_value != int(float_value):
                    return False
                num_value = int(float_value)
            else:
                num_value = float(value)
        except (ValueError, TypeError):
            return False
        
        # 范围验证
        if self.min_value is not None and num_value < self.min_value:
            return False
        
        if self.max_value is not None and num_value > self.max_value:
            return False
        
        # 正数验证
        if self.positive_only:
            if not self.allow_zero and num_value <= 0:
                return False
            elif self.allow_zero and num_value < 0:
                return False
        
        return True
    
    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        try:
            if self.integer_only:
                num_value = int(value)
            else:
                num_value = float(value)
            
            if self.min_value is not None and num_value < self.min_value:
                return f"不能小于{self.min_value}"
            
            if self.max_value is not None and num_value > self.max_value:
                return f"不能大于{self.max_value}"
            
            if self.positive_only:
                if not self.allow_zero and num_value <= 0:
                    return "必须为正数"
                elif self.allow_zero and num_value < 0:
                    return "不能为负数"
        
        except (ValueError, TypeError):
            if self.integer_only:
                return "必须为整数"
            else:
                return "必须为数值"
        
        return super().get_error_message(value, context)


class DateTimeValidationRule(ValidationRule):
    """日期时间验证规则"""
    
    def __init__(
        self,
        name: str,
        min_date: Optional[datetime] = None,
        max_date: Optional[datetime] = None,
        future_only: bool = False,
        past_only: bool = False
    ):
        super().__init__(name, f"日期时间验证规则: {name}")
        self.min_date = min_date
        self.max_date = max_date
        self.future_only = future_only
        self.past_only = past_only
    
    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True
        
        if not isinstance(value, datetime):
            return False
        
        now = datetime.now()
        
        # 未来/过去验证
        if self.future_only and value <= now:
            return False
        
        if self.past_only and value >= now:
            return False
        
        # 日期范围验证
        if self.min_date and value < self.min_date:
            return False
        
        if self.max_date and value > self.max_date:
            return False
        
        return True
    
    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        if not isinstance(value, datetime):
            return "必须为有效的日期时间"
        
        now = datetime.now()
        
        if self.future_only and value <= now:
            return "必须为未来时间"
        
        if self.past_only and value >= now:
            return "必须为过去时间"
        
        if self.min_date and value < self.min_date:
            return f"不能早于{self.min_date.strftime('%Y-%m-%d %H:%M:%S')}"
        
        if self.max_date and value > self.max_date:
            return f"不能晚于{self.max_date.strftime('%Y-%m-%d %H:%M:%S')}"
        
        return super().get_error_message(value, context)


class ListValidationRule(ValidationRule):
    """列表验证规则"""
    
    def __init__(
        self,
        name: str,
        min_length: Optional[int] = None,
        max_length: Optional[int] = None,
        unique_items: bool = False,
        item_validator: Optional[ValidationRule] = None
    ):
        super().__init__(name, f"列表验证规则: {name}")
        self.min_length = min_length
        self.max_length = max_length
        self.unique_items = unique_items
        self.item_validator = item_validator
    
    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True
        
        if not isinstance(value, (list, tuple)):
            return False
        
        # 长度验证
        if self.min_length is not None and len(value) < self.min_length:
            return False
        
        if self.max_length is not None and len(value) > self.max_length:
            return False
        
        # 唯一性验证
        if self.unique_items and len(value) != len(set(value)):
            return False
        
        # 项目验证
        if self.item_validator:
            for item in value:
                if not self.item_validator.validate(item, context):
                    return False
        
        return True
    
    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        if not isinstance(value, (list, tuple)):
            return "必须为列表类型"
        
        if self.min_length is not None and len(value) < self.min_length:
            return f"至少需要{self.min_length}个元素"
        
        if self.max_length is not None and len(value) > self.max_length:
            return f"最多只能有{self.max_length}个元素"
        
        if self.unique_items and len(value) != len(set(value)):
            return "列表中不能有重复元素"
        
        return super().get_error_message(value, context)


class EnumValidationRule(ValidationRule):
    """枚举验证规则"""

    def __init__(self, name: str, enum_class: type):
        super().__init__(name, f"枚举验证规则: {name}")
        self.enum_class = enum_class

    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True

        # 检查是否为枚举实例
        if isinstance(value, self.enum_class):
            return True

        # 检查是否为有效的枚举值
        try:
            self.enum_class(value)
            return True
        except (ValueError, TypeError):
            return False

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        valid_values = [e.value for e in self.enum_class]
        return f"必须为有效的枚举值，可选值: {valid_values}"


class UUIDValidationRule(ValidationRule):
    """UUID验证规则"""

    def __init__(self, name: str = "uuid_validation"):
        super().__init__(name, "UUID验证规则")

    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True

        if isinstance(value, UUID):
            return True

        if isinstance(value, str):
            try:
                UUID(value)
                return True
            except ValueError:
                return False

        return False

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        return "必须为有效的UUID格式"


class EmailValidationRule(ValidationRule):
    """邮箱验证规则"""

    def __init__(self, name: str = "email_validation"):
        super().__init__(name, "邮箱验证规则")
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )

    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True

        if not isinstance(value, str):
            return False

        return bool(self.email_pattern.match(value.strip()))

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        return "必须为有效的邮箱地址格式"


class URLValidationRule(ValidationRule):
    """URL验证规则"""

    def __init__(self, name: str = "url_validation", allowed_schemes: Optional[List[str]] = None):
        super().__init__(name, "URL验证规则")
        self.allowed_schemes = allowed_schemes or ['http', 'https']

    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True

        if not isinstance(value, str):
            return False

        try:
            parsed = urlparse(value.strip())
            return (
                parsed.scheme in self.allowed_schemes and
                parsed.netloc and
                len(parsed.netloc) > 0 and
                '.' in parsed.netloc  # 确保有域名
            )
        except Exception:
            return False

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        return f"必须为有效的URL格式，支持的协议: {self.allowed_schemes}"


class FilePathValidationRule(ValidationRule):
    """文件路径验证规则"""

    def __init__(
        self,
        name: str = "file_path_validation",
        must_exist: bool = False,
        allowed_extensions: Optional[List[str]] = None
    ):
        super().__init__(name, "文件路径验证规则")
        self.must_exist = must_exist
        self.allowed_extensions = allowed_extensions

    def validate(self, value: Any, context: ValidationContext) -> bool:
        if value is None:
            return True

        if not isinstance(value, str):
            return False

        path = value.strip()

        # 检查文件是否存在
        if self.must_exist and not os.path.exists(path):
            return False

        # 检查文件扩展名
        if self.allowed_extensions:
            _, ext = os.path.splitext(path)
            if ext.lower() not in [e.lower() for e in self.allowed_extensions]:
                return False

        return True

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        if self.must_exist and not os.path.exists(value):
            return "文件路径不存在"

        if self.allowed_extensions:
            _, ext = os.path.splitext(value)
            if ext.lower() not in [e.lower() for e in self.allowed_extensions]:
                return f"文件扩展名必须为: {self.allowed_extensions}"

        return "无效的文件路径"


class CustomValidationRule(ValidationRule):
    """自定义验证规则"""

    def __init__(
        self,
        name: str,
        validator_func: callable,
        error_message: str = "自定义验证失败"
    ):
        super().__init__(name, f"自定义验证规则: {name}")
        self.validator_func = validator_func
        self.error_message = error_message

    def validate(self, value: Any, context: ValidationContext) -> bool:
        try:
            return self.validator_func(value, context)
        except Exception:
            return False

    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        return self.error_message
