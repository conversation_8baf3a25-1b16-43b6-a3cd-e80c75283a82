"""
场景管理界面模块

实现场景的创建、编辑、删除和管理功能
"""

from typing import List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QGroupBox,
    QFormLayout, QLineEdit, QTextEdit, QComboBox,
    QTabWidget, QFrame, QSplitter, QMessageBox,
    QDialog, QDialogButtonBox, QTimeEdit, QDateEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QTime, QDate
from PyQt6.QtGui import QFont

from src.core.models.project import WritingProject
from src.core.models.scene import Scene
from src.core.models.enums import SceneType, TimeOfDay, Weather, Atmosphere


class SceneEditDialog(QDialog):
    """场景编辑对话框"""
    
    def __init__(self, scene: Optional[Scene] = None, parent=None):
        super().__init__(parent)
        self.scene = scene
        self.is_editing = scene is not None
        
        self.setup_ui()
        if self.is_editing:
            self.load_scene_data()
            
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("编辑场景" if self.is_editing else "新建场景")
        self.setModal(True)
        self.setMinimumSize(600, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")
        
        # 环境描述标签页
        environment_tab = self.create_environment_tab()
        tab_widget.addTab(environment_tab, "环境描述")
        
        # 氛围设定标签页
        atmosphere_tab = self.create_atmosphere_tab()
        tab_widget.addTab(atmosphere_tab, "氛围设定")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 场景名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入场景名称")
        layout.addRow("场景名称 *:", self.name_edit)
        
        # 场景类型
        self.type_combo = QComboBox()
        for scene_type in SceneType:
            self.type_combo.addItem(scene_type.value, scene_type)
        layout.addRow("场景类型:", self.type_combo)
        
        # 地点
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("具体地点或位置")
        layout.addRow("地点:", self.location_edit)
        
        # 时间设定
        time_group = QGroupBox("时间设定")
        time_layout = QFormLayout(time_group)
        
        # 日期
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        time_layout.addRow("日期:", self.date_edit)
        
        # 时间
        self.time_edit = QTimeEdit()
        self.time_edit.setTime(QTime.currentTime())
        time_layout.addRow("时间:", self.time_edit)
        
        # 时段
        self.time_of_day_combo = QComboBox()
        for time_of_day in TimeOfDay:
            self.time_of_day_combo.addItem(time_of_day.value, time_of_day)
        time_layout.addRow("时段:", self.time_of_day_combo)
        
        layout.addRow(time_group)
        
        # 场景描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("场景的简要描述")
        layout.addRow("场景描述:", self.description_edit)
        
        return widget
        
    def create_environment_tab(self) -> QWidget:
        """创建环境描述标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 天气
        self.weather_combo = QComboBox()
        for weather in Weather:
            self.weather_combo.addItem(weather.value, weather)
        layout.addRow("天气:", self.weather_combo)
        
        # 温度
        self.temperature_edit = QLineEdit()
        self.temperature_edit.setPlaceholderText("如：25°C、炎热、寒冷等")
        layout.addRow("温度:", self.temperature_edit)
        
        # 地理环境
        self.geography_edit = QTextEdit()
        self.geography_edit.setMaximumHeight(80)
        self.geography_edit.setPlaceholderText("地形、地貌、周围环境等")
        layout.addRow("地理环境:", self.geography_edit)
        
        # 建筑描述
        self.architecture_edit = QTextEdit()
        self.architecture_edit.setMaximumHeight(80)
        self.architecture_edit.setPlaceholderText("建筑物、房间布局、装饰等")
        layout.addRow("建筑描述:", self.architecture_edit)
        
        # 物品道具
        self.objects_edit = QTextEdit()
        self.objects_edit.setMaximumHeight(80)
        self.objects_edit.setPlaceholderText("场景中的重要物品、道具等")
        layout.addRow("物品道具:", self.objects_edit)
        
        # 感官描述
        sensory_group = QGroupBox("感官描述")
        sensory_layout = QFormLayout(sensory_group)
        
        self.visual_edit = QTextEdit()
        self.visual_edit.setMaximumHeight(60)
        self.visual_edit.setPlaceholderText("视觉描述：颜色、光线、景象等")
        sensory_layout.addRow("视觉:", self.visual_edit)
        
        self.auditory_edit = QTextEdit()
        self.auditory_edit.setMaximumHeight(60)
        self.auditory_edit.setPlaceholderText("听觉描述：声音、音乐、噪音等")
        sensory_layout.addRow("听觉:", self.auditory_edit)
        
        self.olfactory_edit = QTextEdit()
        self.olfactory_edit.setMaximumHeight(60)
        self.olfactory_edit.setPlaceholderText("嗅觉描述：气味、香味等")
        sensory_layout.addRow("嗅觉:", self.olfactory_edit)
        
        layout.addRow(sensory_group)
        
        return widget
        
    def create_atmosphere_tab(self) -> QWidget:
        """创建氛围设定标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 氛围类型
        self.atmosphere_combo = QComboBox()
        for atmosphere in Atmosphere:
            self.atmosphere_combo.addItem(atmosphere.value, atmosphere)
        layout.addRow("氛围类型:", self.atmosphere_combo)
        
        # 情绪基调
        self.mood_edit = QLineEdit()
        self.mood_edit.setPlaceholderText("如：紧张、轻松、神秘、浪漫等")
        layout.addRow("情绪基调:", self.mood_edit)
        
        # 氛围描述
        self.atmosphere_description_edit = QTextEdit()
        self.atmosphere_description_edit.setMinimumHeight(100)
        self.atmosphere_description_edit.setPlaceholderText("详细描述场景的氛围和情绪感受")
        layout.addRow("氛围描述:", self.atmosphere_description_edit)
        
        # 象征意义
        self.symbolism_edit = QTextEdit()
        self.symbolism_edit.setMaximumHeight(80)
        self.symbolism_edit.setPlaceholderText("场景的象征意义或隐喻")
        layout.addRow("象征意义:", self.symbolism_edit)
        
        # 对情节的影响
        self.plot_impact_edit = QTextEdit()
        self.plot_impact_edit.setMaximumHeight(80)
        self.plot_impact_edit.setPlaceholderText("这个场景对情节发展的影响")
        layout.addRow("情节影响:", self.plot_impact_edit)
        
        return widget
        
    def load_scene_data(self):
        """加载场景数据"""
        if not self.scene:
            return
            
        # 基本信息
        self.name_edit.setText(self.scene.name)
        self.location_edit.setText(self.scene.location or "")
        self.description_edit.setPlainText(self.scene.description or "")
        
        # 设置场景类型
        for i in range(self.type_combo.count()):
            if self.type_combo.itemData(i) == self.scene.scene_type:
                self.type_combo.setCurrentIndex(i)
                break
                
        # 时间设定
        if self.scene.time_setting:
            time_setting = self.scene.time_setting
            if time_setting.date:
                self.date_edit.setDate(time_setting.date)
            if time_setting.time:
                self.time_edit.setTime(time_setting.time)
            if time_setting.time_of_day:
                for i in range(self.time_of_day_combo.count()):
                    if self.time_of_day_combo.itemData(i) == time_setting.time_of_day:
                        self.time_of_day_combo.setCurrentIndex(i)
                        break
                        
        # 环境描述
        if self.scene.environment:
            env = self.scene.environment
            if env.weather:
                for i in range(self.weather_combo.count()):
                    if self.weather_combo.itemData(i) == env.weather:
                        self.weather_combo.setCurrentIndex(i)
                        break
                        
            self.temperature_edit.setText(env.temperature or "")
            self.geography_edit.setPlainText(env.geography or "")
            self.architecture_edit.setPlainText(env.architecture or "")
            self.objects_edit.setPlainText(env.objects or "")
            
            if env.sensory_details:
                sensory = env.sensory_details
                self.visual_edit.setPlainText(sensory.visual or "")
                self.auditory_edit.setPlainText(sensory.auditory or "")
                self.olfactory_edit.setPlainText(sensory.olfactory or "")
                
        # 氛围设定
        if self.scene.atmosphere_setting:
            atmosphere = self.scene.atmosphere_setting
            if atmosphere.atmosphere:
                for i in range(self.atmosphere_combo.count()):
                    if self.atmosphere_combo.itemData(i) == atmosphere.atmosphere:
                        self.atmosphere_combo.setCurrentIndex(i)
                        break
                        
            self.mood_edit.setText(atmosphere.mood or "")
            self.atmosphere_description_edit.setPlainText(atmosphere.description or "")
            self.symbolism_edit.setPlainText(atmosphere.symbolism or "")
            self.plot_impact_edit.setPlainText(atmosphere.plot_impact or "")
            
    def get_scene_data(self) -> dict:
        """获取场景数据"""
        return {
            'name': self.name_edit.text().strip(),
            'scene_type': self.type_combo.currentData(),
            'location': self.location_edit.text().strip() or None,
            'description': self.description_edit.toPlainText().strip() or None,
            'time_setting': {
                'date': self.date_edit.date().toPython(),
                'time': self.time_edit.time().toPython(),
                'time_of_day': self.time_of_day_combo.currentData(),
            },
            'environment': {
                'weather': self.weather_combo.currentData(),
                'temperature': self.temperature_edit.text().strip() or None,
                'geography': self.geography_edit.toPlainText().strip() or None,
                'architecture': self.architecture_edit.toPlainText().strip() or None,
                'objects': self.objects_edit.toPlainText().strip() or None,
                'sensory_details': {
                    'visual': self.visual_edit.toPlainText().strip() or None,
                    'auditory': self.auditory_edit.toPlainText().strip() or None,
                    'olfactory': self.olfactory_edit.toPlainText().strip() or None,
                }
            },
            'atmosphere_setting': {
                'atmosphere': self.atmosphere_combo.currentData(),
                'mood': self.mood_edit.text().strip() or None,
                'description': self.atmosphere_description_edit.toPlainText().strip() or None,
                'symbolism': self.symbolism_edit.toPlainText().strip() or None,
                'plot_impact': self.plot_impact_edit.toPlainText().strip() or None,
            }
        }


class SceneListWidget(QListWidget):
    """场景列表组件"""
    
    # 信号定义
    scene_selected = pyqtSignal(Scene)
    scene_double_clicked = pyqtSignal(Scene)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scenes: List[Scene] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setAlternatingRowColors(True)
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                alternate-background-color: #f8f9fa;
            }
            
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QListWidget::item:selected {
                background: #007bff;
                color: white;
            }
            
            QListWidget::item:hover {
                background: #e3f2fd;
            }
        """)
        
        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
        
    def add_scene(self, scene: Scene):
        """添加场景"""
        self.scenes.append(scene)
        
        item = QListWidgetItem()
        item.setText(f"{scene.name} ({scene.scene_type.value})")
        item.setData(Qt.ItemDataRole.UserRole, scene)
        
        self.addItem(item)
        
    def remove_scene(self, scene: Scene):
        """移除场景"""
        if scene in self.scenes:
            self.scenes.remove(scene)
            
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == scene:
                self.takeItem(i)
                break
                
    def update_scene(self, scene: Scene):
        """更新场景"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole).id == scene.id:
                item.setText(f"{scene.name} ({scene.scene_type.value})")
                item.setData(Qt.ItemDataRole.UserRole, scene)
                break
                
    def on_item_clicked(self, item: QListWidgetItem):
        """处理项目点击"""
        scene = item.data(Qt.ItemDataRole.UserRole)
        if scene:
            self.scene_selected.emit(scene)
            
    def on_item_double_clicked(self, item: QListWidgetItem):
        """处理项目双击"""
        scene = item.data(Qt.ItemDataRole.UserRole)
        if scene:
            self.scene_double_clicked.emit(scene)


class SceneManagerWidget(QWidget):
    """场景管理主组件"""
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.current_scene: Optional[Scene] = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_scenes()
        
    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        
        # 左侧场景列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 场景列表标题和按钮
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("场景列表"))
        header_layout.addStretch()
        
        self.add_btn = QPushButton("新建")
        self.add_btn.setMaximumWidth(60)
        header_layout.addWidget(self.add_btn)
        
        left_layout.addLayout(header_layout)
        
        # 场景列表
        self.scene_list = SceneListWidget()
        left_layout.addWidget(self.scene_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.duplicate_btn = QPushButton("复制")
        
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.duplicate_btn)
        
        left_layout.addLayout(button_layout)
        
        # 右侧场景详情
        self.scene_detail = QTextEdit()
        self.scene_detail.setReadOnly(True)
        self.scene_detail.setPlaceholderText("选择场景查看详细信息")
        
        # 添加到主布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(self.scene_detail)
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
        
    def setup_connections(self):
        """设置信号连接"""
        self.add_btn.clicked.connect(self.add_scene)
        self.edit_btn.clicked.connect(self.edit_scene)
        self.delete_btn.clicked.connect(self.delete_scene)
        self.duplicate_btn.clicked.connect(self.duplicate_scene)
        
        self.scene_list.scene_selected.connect(self.show_scene_detail)
        self.scene_list.scene_double_clicked.connect(self.edit_scene_from_list)
        
    def load_scenes(self):
        """加载场景列表"""
        # TODO: 从项目中加载场景
        pass
        
    def add_scene(self):
        """添加场景"""
        dialog = SceneEditDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            scene_data = dialog.get_scene_data()
            
            # 验证必填字段
            if not scene_data['name']:
                QMessageBox.warning(self, "验证错误", "场景名称不能为空")
                return
                
            # 创建场景对象
            try:
                scene = Scene(
                    name=scene_data['name'],
                    scene_type=scene_data['scene_type'],
                    **{k: v for k, v in scene_data.items() if k not in ['name', 'scene_type'] and v is not None}
                )
                
                # 添加到列表
                self.scene_list.add_scene(scene)
                
                # TODO: 保存到项目
                
            except Exception as e:
                QMessageBox.critical(self, "创建失败", f"创建场景失败: {e}")
                
    def edit_scene(self):
        """编辑当前选中的场景"""
        if self.current_scene:
            self.edit_scene_from_list(self.current_scene)
            
    def edit_scene_from_list(self, scene: Scene):
        """编辑指定场景"""
        dialog = SceneEditDialog(scene, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            scene_data = dialog.get_scene_data()
            
            # 更新场景数据
            try:
                # TODO: 更新场景对象
                self.scene_list.update_scene(scene)
                self.show_scene_detail(scene)
                
            except Exception as e:
                QMessageBox.critical(self, "更新失败", f"更新场景失败: {e}")
                
    def delete_scene(self):
        """删除场景"""
        if not self.current_scene:
            return
            
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除场景 '{self.current_scene.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.scene_list.remove_scene(self.current_scene)
            self.current_scene = None
            self.scene_detail.clear()
            
    def duplicate_scene(self):
        """复制场景"""
        if not self.current_scene:
            return
            
        # TODO: 实现场景复制功能
        QMessageBox.information(self, "功能开发中", "场景复制功能正在开发中")
        
    def show_scene_detail(self, scene: Scene):
        """显示场景详情"""
        self.current_scene = scene
        
        # 生成详情文本
        detail_text = f"""
<h2>{scene.name}</h2>
<p><strong>场景类型:</strong> {scene.scene_type.value}</p>
"""
        
        if scene.location:
            detail_text += f"<p><strong>地点:</strong> {scene.location}</p>"
            
        if scene.description:
            detail_text += f"<p><strong>描述:</strong> {scene.description}</p>"
            
        # TODO: 添加更多详情信息
        
        self.scene_detail.setHtml(detail_text)
