<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\core\validation\relationship_validators.py: 18%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\core\validation\relationship_validators.py</b>:
            <span class="pc_cov">18%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">182 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">33<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">149<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_fb48bbbbe924ff15_exceptions_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_fb48bbbbe924ff15_rules_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""&#20851;&#31995;&#39564;&#35777;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#23454;&#29616;&#36328;&#27169;&#22411;&#20851;&#32852;&#39564;&#35777;&#21151;&#33021;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Set</span><span class="op">,</span> <span class="nam">Any</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">uuid</span> <span class="key">import</span> <span class="nam">UUID</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="nam">BaseModel</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">character</span> <span class="key">import</span> <span class="nam">Character</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">scene</span> <span class="key">import</span> <span class="nam">Scene</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">event</span> <span class="key">import</span> <span class="nam">Event</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">project</span> <span class="key">import</span> <span class="nam">WritingProject</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">story_element</span> <span class="key">import</span> <span class="nam">StoryElement</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">validators</span> <span class="key">import</span> <span class="nam">RelationshipValidator</span><span class="op">,</span> <span class="nam">ValidationContext</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">class</span> <span class="nam">CharacterRelationshipValidator</span><span class="op">(</span><span class="nam">RelationshipValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="str">"""&#35282;&#33394;&#20851;&#31995;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">        <span class="nam">character</span> <span class="op">=</span> <span class="nam">model</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">        <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#35282;&#33394;&#20851;&#31995;&#30340;&#21452;&#21521;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_bidirectional_consistency</span><span class="op">(</span><span class="nam">character</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20851;&#31995;&#30340;&#36923;&#36753;&#21512;&#29702;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_relationship_logic</span><span class="op">(</span><span class="nam">character</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20851;&#31995;&#32593;&#32476;&#30340;&#23436;&#25972;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_relationship_network</span><span class="op">(</span><span class="nam">character</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">        <span class="key">return</span> <span class="nam">all_passed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_validator_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="key">return</span> <span class="str">"character_relationship_validator"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_bidirectional_consistency</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">character</span><span class="op">:</span> <span class="nam">Character</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#21452;&#21521;&#20851;&#31995;&#19968;&#33268;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#30456;&#20851;&#30340;&#20854;&#20182;&#35282;&#33394;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="nam">other_characters</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'characters'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="key">for</span> <span class="nam">relationship</span> <span class="key">in</span> <span class="nam">character</span><span class="op">.</span><span class="nam">character_relationships</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">            <span class="nam">target_id</span> <span class="op">=</span> <span class="nam">relationship</span><span class="op">.</span><span class="nam">target_character_id</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">            <span class="com"># &#26597;&#25214;&#30446;&#26631;&#35282;&#33394;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">            <span class="nam">target_character</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">            <span class="key">for</span> <span class="nam">other_char</span> <span class="key">in</span> <span class="nam">other_characters</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">                <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">other_char</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span> <span class="key">and</span> <span class="nam">other_char</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">target_id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">                    <span class="nam">target_character</span> <span class="op">=</span> <span class="nam">other_char</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">                    <span class="key">break</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">            <span class="key">if</span> <span class="nam">target_character</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                <span class="com"># &#26816;&#26597;&#30446;&#26631;&#35282;&#33394;&#26159;&#21542;&#26377;&#23545;&#24212;&#30340;&#21453;&#21521;&#20851;&#31995;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="nam">reverse_relationship</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="key">for</span> <span class="nam">target_rel</span> <span class="key">in</span> <span class="nam">target_character</span><span class="op">.</span><span class="nam">character_relationships</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                    <span class="key">if</span> <span class="nam">target_rel</span><span class="op">.</span><span class="nam">target_character_id</span> <span class="op">==</span> <span class="nam">character</span><span class="op">.</span><span class="nam">id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                        <span class="nam">reverse_relationship</span> <span class="op">=</span> <span class="nam">target_rel</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">                        <span class="key">break</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">                <span class="key">if</span> <span class="key">not</span> <span class="nam">reverse_relationship</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">                    <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35282;&#33394;</span><span class="op">{</span><span class="nam">character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#19982;</span><span class="op">{</span><span class="nam">target_character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#30340;&#20851;&#31995;&#32570;&#23569;&#21453;&#21521;&#20851;&#31995;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">                <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">                    <span class="com"># &#26816;&#26597;&#20851;&#31995;&#31867;&#22411;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">                    <span class="key">if</span> <span class="nam">relationship</span><span class="op">.</span><span class="nam">relationship_type</span> <span class="op">!=</span> <span class="nam">reverse_relationship</span><span class="op">.</span><span class="nam">relationship_type</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">                        <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35282;&#33394;</span><span class="op">{</span><span class="nam">character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#19982;</span><span class="op">{</span><span class="nam">target_character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#30340;&#20851;&#31995;&#31867;&#22411;&#19981;&#19968;&#33268;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_relationship_logic</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">character</span><span class="op">:</span> <span class="nam">Character</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#20851;&#31995;&#36923;&#36753;&#21512;&#29702;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#20914;&#31361;&#30340;&#20851;&#31995;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">        <span class="nam">romantic_relationships</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">        <span class="nam">family_relationships</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="key">for</span> <span class="nam">relationship</span> <span class="key">in</span> <span class="nam">character</span><span class="op">.</span><span class="nam">character_relationships</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">            <span class="key">if</span> <span class="nam">relationship</span><span class="op">.</span><span class="nam">relationship_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">'romantic'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                <span class="nam">romantic_relationships</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">relationship</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">            <span class="key">elif</span> <span class="nam">relationship</span><span class="op">.</span><span class="nam">relationship_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">'family'</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="nam">family_relationships</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">relationship</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#22810;&#37325;&#24651;&#29233;&#20851;&#31995;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">        <span class="nam">active_romantic</span> <span class="op">=</span> <span class="op">[</span><span class="nam">r</span> <span class="key">for</span> <span class="nam">r</span> <span class="key">in</span> <span class="nam">romantic_relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                          <span class="key">if</span> <span class="key">not</span> <span class="nam">r</span><span class="op">.</span><span class="nam">relationship_end</span> <span class="key">or</span> <span class="nam">r</span><span class="op">.</span><span class="nam">relationship_end</span> <span class="op">></span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">active_romantic</span><span class="op">)</span> <span class="op">></span> <span class="num">1</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35282;&#33394;</span><span class="op">{</span><span class="nam">character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#21516;&#26102;&#26377;&#22810;&#20010;&#27963;&#36291;&#30340;&#24651;&#29233;&#20851;&#31995;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_relationship_network</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">character</span><span class="op">:</span> <span class="nam">Character</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#20851;&#31995;&#32593;&#32476;&#23436;&#25972;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#23396;&#31435;&#35282;&#33394;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="key">if</span> <span class="nam">character</span><span class="op">.</span><span class="nam">importance</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">'critical'</span> <span class="key">and</span> <span class="nam">len</span><span class="op">(</span><span class="nam">character</span><span class="op">.</span><span class="nam">character_relationships</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20851;&#38190;&#35282;&#33394;</span><span class="op">{</span><span class="nam">character</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#27809;&#26377;&#20219;&#20309;&#20851;&#31995;&#65292;&#21487;&#33021;&#36807;&#20110;&#23396;&#31435;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneEventRelationshipValidator</span><span class="op">(</span><span class="nam">RelationshipValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#20107;&#20214;&#20851;&#31995;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">Scene</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_scene_relationships</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">                <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">        <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">Event</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_event_relationships</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">                <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="key">return</span> <span class="nam">all_passed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_validator_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">        <span class="key">return</span> <span class="str">"scene_event_relationship_validator"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_scene_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">scene</span><span class="op">:</span> <span class="nam">Scene</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;&#20851;&#31995;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#30456;&#20851;&#30340;&#35282;&#33394;&#21644;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">        <span class="nam">characters</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'characters'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="nam">events</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'events'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#22330;&#26223;&#20013;&#30340;&#35282;&#33394;&#26159;&#21542;&#23384;&#22312;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">        <span class="key">for</span> <span class="nam">char_id</span> <span class="key">in</span> <span class="nam">scene</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">            <span class="nam">char_exists</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">c</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span> <span class="key">and</span> <span class="nam">c</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">char_id</span> <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">characters</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">char_exists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">                <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#22330;&#26223;</span><span class="op">{</span><span class="nam">scene</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#24341;&#29992;&#20102;&#19981;&#23384;&#22312;&#30340;&#35282;&#33394;ID: </span><span class="op">{</span><span class="nam">char_id</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#22330;&#26223;&#20013;&#30340;&#20107;&#20214;&#26159;&#21542;&#23384;&#22312;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="key">for</span> <span class="nam">event_id</span> <span class="key">in</span> <span class="nam">scene</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">            <span class="nam">event_exists</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">e</span><span class="op">,</span> <span class="nam">Event</span><span class="op">)</span> <span class="key">and</span> <span class="nam">e</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">event_id</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">events</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">event_exists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">                <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#22330;&#26223;</span><span class="op">{</span><span class="nam">scene</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#24341;&#29992;&#20102;&#19981;&#23384;&#22312;&#30340;&#20107;&#20214;ID: </span><span class="op">{</span><span class="nam">event_id</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_event_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event</span><span class="op">:</span> <span class="nam">Event</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#20107;&#20214;&#20851;&#31995;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#30456;&#20851;&#30340;&#35282;&#33394;&#21644;&#22330;&#26223;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="nam">characters</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'characters'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="nam">scenes</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'scenes'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20107;&#20214;&#21442;&#19982;&#32773;&#26159;&#21542;&#23384;&#22312;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="key">for</span> <span class="nam">participant</span> <span class="key">in</span> <span class="nam">event</span><span class="op">.</span><span class="nam">participants</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="nam">char_id</span> <span class="op">=</span> <span class="nam">participant</span><span class="op">.</span><span class="nam">character_id</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="nam">char_exists</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">c</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span> <span class="key">and</span> <span class="nam">c</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">char_id</span> <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">characters</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">char_exists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">                <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20107;&#20214;</span><span class="op">{</span><span class="nam">event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#24341;&#29992;&#20102;&#19981;&#23384;&#22312;&#30340;&#35282;&#33394;ID: </span><span class="op">{</span><span class="nam">char_id</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20107;&#20214;&#21457;&#29983;&#22330;&#26223;&#26159;&#21542;&#23384;&#22312;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">event</span><span class="op">,</span> <span class="str">'scene_id'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">event</span><span class="op">.</span><span class="nam">scene_id</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">            <span class="nam">scene_exists</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">isinstance</span><span class="op">(</span><span class="nam">s</span><span class="op">,</span> <span class="nam">Scene</span><span class="op">)</span> <span class="key">and</span> <span class="nam">s</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">event</span><span class="op">.</span><span class="nam">scene_id</span> <span class="key">for</span> <span class="nam">s</span> <span class="key">in</span> <span class="nam">scenes</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">scene_exists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">                <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20107;&#20214;</span><span class="op">{</span><span class="nam">event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#24341;&#29992;&#20102;&#19981;&#23384;&#22312;&#30340;&#22330;&#26223;ID: </span><span class="op">{</span><span class="nam">event</span><span class="op">.</span><span class="nam">scene_id</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t"><span class="key">class</span> <span class="nam">ProjectElementRelationshipValidator</span><span class="op">(</span><span class="nam">RelationshipValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="str">"""&#39033;&#30446;&#20803;&#32032;&#20851;&#31995;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">WritingProject</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">        <span class="nam">project</span> <span class="op">=</span> <span class="nam">model</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#39033;&#30446;&#32479;&#35745;&#25968;&#25454;&#19982;&#23454;&#38469;&#20803;&#32032;&#25968;&#37327;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_project_statistics</span><span class="op">(</span><span class="nam">project</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="key">return</span> <span class="nam">all_passed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_validator_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="key">return</span> <span class="str">"project_element_relationship_validator"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_project_statistics</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">project</span><span class="op">:</span> <span class="nam">WritingProject</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#39033;&#30446;&#32479;&#35745;&#25968;&#25454;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#23454;&#38469;&#30340;&#20803;&#32032;&#25968;&#37327;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">        <span class="nam">characters</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'characters'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">        <span class="nam">scenes</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'scenes'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">        <span class="nam">events</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'events'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="nam">actual_character_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="op">[</span><span class="nam">c</span> <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">characters</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">c</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">        <span class="nam">actual_scene_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="op">[</span><span class="nam">s</span> <span class="key">for</span> <span class="nam">s</span> <span class="key">in</span> <span class="nam">scenes</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">s</span><span class="op">,</span> <span class="nam">Scene</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="nam">actual_event_count</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="op">[</span><span class="nam">e</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">events</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">e</span><span class="op">,</span> <span class="nam">Event</span><span class="op">)</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#32479;&#35745;&#25968;&#25454;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="key">if</span> <span class="nam">project</span><span class="op">.</span><span class="nam">character_count</span> <span class="op">!=</span> <span class="nam">actual_character_count</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#39033;&#30446;&#35282;&#33394;&#32479;&#35745;(</span><span class="op">{</span><span class="nam">project</span><span class="op">.</span><span class="nam">character_count</span><span class="op">}</span><span class="fst">)&#19982;&#23454;&#38469;&#25968;&#37327;(</span><span class="op">{</span><span class="nam">actual_character_count</span><span class="op">}</span><span class="fst">)&#19981;&#19968;&#33268;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">        <span class="key">if</span> <span class="nam">project</span><span class="op">.</span><span class="nam">scene_count</span> <span class="op">!=</span> <span class="nam">actual_scene_count</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#39033;&#30446;&#22330;&#26223;&#32479;&#35745;(</span><span class="op">{</span><span class="nam">project</span><span class="op">.</span><span class="nam">scene_count</span><span class="op">}</span><span class="fst">)&#19982;&#23454;&#38469;&#25968;&#37327;(</span><span class="op">{</span><span class="nam">actual_scene_count</span><span class="op">}</span><span class="fst">)&#19981;&#19968;&#33268;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">        <span class="key">if</span> <span class="nam">project</span><span class="op">.</span><span class="nam">event_count</span> <span class="op">!=</span> <span class="nam">actual_event_count</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#39033;&#30446;&#20107;&#20214;&#32479;&#35745;(</span><span class="op">{</span><span class="nam">project</span><span class="op">.</span><span class="nam">event_count</span><span class="op">}</span><span class="fst">)&#19982;&#23454;&#38469;&#25968;&#37327;(</span><span class="op">{</span><span class="nam">actual_event_count</span><span class="op">}</span><span class="fst">)&#19981;&#19968;&#33268;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t"><span class="key">class</span> <span class="nam">TimelineConsistencyValidator</span><span class="op">(</span><span class="nam">RelationshipValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">    <span class="str">"""&#26102;&#38388;&#32447;&#19968;&#33268;&#24615;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#25152;&#26377;&#26377;&#26102;&#38388;&#32447;&#20449;&#24687;&#30340;&#20803;&#32032;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">        <span class="nam">events</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'events'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">        <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20107;&#20214;&#26102;&#38388;&#32447;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_event_timeline</span><span class="op">(</span><span class="nam">events</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">        <span class="key">return</span> <span class="nam">all_passed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_validator_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">        <span class="key">return</span> <span class="str">"timeline_consistency_validator"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_event_timeline</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">events</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">BaseModel</span><span class="op">]</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#20107;&#20214;&#26102;&#38388;&#32447;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="nam">event_list</span> <span class="op">=</span> <span class="op">[</span><span class="nam">e</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">events</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">e</span><span class="op">,</span> <span class="nam">Event</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">        <span class="com"># &#25353;&#26102;&#38388;&#32447;&#20301;&#32622;&#25490;&#24207;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">        <span class="nam">sorted_events</span> <span class="op">=</span> <span class="nam">sorted</span><span class="op">(</span><span class="nam">event_list</span><span class="op">,</span> <span class="nam">key</span><span class="op">=</span><span class="key">lambda</span> <span class="nam">e</span><span class="op">:</span> <span class="nam">e</span><span class="op">.</span><span class="nam">timeline_position</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#26102;&#38388;&#32447;&#20301;&#32622;&#30340;&#21512;&#29702;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">        <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">len</span><span class="op">(</span><span class="nam">sorted_events</span><span class="op">)</span> <span class="op">-</span> <span class="num">1</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">            <span class="nam">current_event</span> <span class="op">=</span> <span class="nam">sorted_events</span><span class="op">[</span><span class="nam">i</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">            <span class="nam">next_event</span> <span class="op">=</span> <span class="nam">sorted_events</span><span class="op">[</span><span class="nam">i</span> <span class="op">+</span> <span class="num">1</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">            <span class="com"># &#26816;&#26597;&#24207;&#21015;&#39034;&#24207;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">            <span class="key">if</span> <span class="nam">current_event</span><span class="op">.</span><span class="nam">sequence_order</span> <span class="op">>=</span> <span class="nam">next_event</span><span class="op">.</span><span class="nam">sequence_order</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">                <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20107;&#20214;</span><span class="op">{</span><span class="nam">current_event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#21644;</span><span class="op">{</span><span class="nam">next_event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#30340;&#24207;&#21015;&#39034;&#24207;&#21487;&#33021;&#26377;&#38382;&#39064;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">            <span class="com"># &#26816;&#26597;&#22240;&#26524;&#20851;&#31995;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="key">if</span> <span class="nam">next_event</span><span class="op">.</span><span class="nam">id</span> <span class="key">in</span> <span class="nam">current_event</span><span class="op">.</span><span class="nam">consequences</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">                <span class="com"># &#21518;&#32493;&#20107;&#20214;&#24212;&#35813;&#22312;&#24403;&#21069;&#20107;&#20214;&#20043;&#21518;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">                <span class="key">if</span> <span class="nam">next_event</span><span class="op">.</span><span class="nam">timeline_position</span> <span class="op">&lt;=</span> <span class="nam">current_event</span><span class="op">.</span><span class="nam">timeline_position</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">                    <span class="nam">context</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#22240;&#26524;&#20851;&#31995;&#38169;&#35823;&#65306;</span><span class="op">{</span><span class="nam">next_event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#24212;&#35813;&#22312;</span><span class="op">{</span><span class="nam">current_event</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#20043;&#21518;&#21457;&#29983;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">                    <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t"><span class="key">class</span> <span class="nam">PlotConsistencyValidator</span><span class="op">(</span><span class="nam">RelationshipValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">    <span class="str">"""&#24773;&#33410;&#19968;&#33268;&#24615;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="com"># &#33719;&#21462;&#25152;&#26377;&#25925;&#20107;&#20803;&#32032;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">        <span class="nam">characters</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'characters'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">        <span class="nam">events</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'events'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="nam">scenes</span> <span class="op">=</span> <span class="nam">context</span><span class="op">.</span><span class="nam">get_related_models</span><span class="op">(</span><span class="str">'scenes'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">        <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#24773;&#33410;&#21457;&#23637;&#30340;&#21512;&#29702;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_plot_development</span><span class="op">(</span><span class="nam">characters</span><span class="op">,</span> <span class="nam">events</span><span class="op">,</span> <span class="nam">scenes</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">            <span class="nam">all_passed</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">        <span class="key">return</span> <span class="nam">all_passed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_validator_name</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">        <span class="key">return</span> <span class="str">"plot_consistency_validator"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_plot_development</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">characters</span><span class="op">,</span> <span class="nam">events</span><span class="op">,</span> <span class="nam">scenes</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">ValidationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#24773;&#33410;&#21457;&#23637;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">        <span class="nam">event_list</span> <span class="op">=</span> <span class="op">[</span><span class="nam">e</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">events</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">e</span><span class="op">,</span> <span class="nam">Event</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#24773;&#33410;&#32467;&#26500;&#30340;&#23436;&#25972;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">        <span class="nam">has_setup</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">e</span><span class="op">.</span><span class="nam">plot_function</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">'setup'</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">event_list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">        <span class="nam">has_climax</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">e</span><span class="op">.</span><span class="nam">is_climax</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">event_list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">        <span class="nam">has_resolution</span> <span class="op">=</span> <span class="nam">any</span><span class="op">(</span><span class="nam">e</span><span class="op">.</span><span class="nam">is_resolution</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">event_list</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">has_setup</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#25925;&#20107;&#32570;&#23569;&#35774;&#32622;/&#38138;&#22443;&#20107;&#20214;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">has_climax</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#25925;&#20107;&#32570;&#23569;&#39640;&#28526;&#20107;&#20214;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">has_resolution</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">            <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#25925;&#20107;&#32570;&#23569;&#35299;&#20915;/&#32467;&#23616;&#20107;&#20214;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#20027;&#35282;&#30340;&#21442;&#19982;&#24230;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">        <span class="nam">character_list</span> <span class="op">=</span> <span class="op">[</span><span class="nam">c</span> <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">characters</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">c</span><span class="op">,</span> <span class="nam">Character</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">        <span class="nam">protagonists</span> <span class="op">=</span> <span class="op">[</span><span class="nam">c</span> <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">character_list</span> <span class="key">if</span> <span class="nam">c</span><span class="op">.</span><span class="nam">story_role</span> <span class="key">and</span> <span class="str">'&#20027;&#35282;'</span> <span class="key">in</span> <span class="nam">c</span><span class="op">.</span><span class="nam">story_role</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">        <span class="key">if</span> <span class="nam">protagonists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">            <span class="key">for</span> <span class="nam">protagonist</span> <span class="key">in</span> <span class="nam">protagonists</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">                <span class="com"># &#26816;&#26597;&#20027;&#35282;&#22312;&#20851;&#38190;&#20107;&#20214;&#20013;&#30340;&#21442;&#19982;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">                <span class="nam">critical_events</span> <span class="op">=</span> <span class="op">[</span><span class="nam">e</span> <span class="key">for</span> <span class="nam">e</span> <span class="key">in</span> <span class="nam">event_list</span> <span class="key">if</span> <span class="nam">e</span><span class="op">.</span><span class="nam">importance</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">'critical'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">                <span class="nam">protagonist_participation</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">                <span class="key">for</span> <span class="nam">event</span> <span class="key">in</span> <span class="nam">critical_events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">                    <span class="key">if</span> <span class="nam">any</span><span class="op">(</span><span class="nam">p</span><span class="op">.</span><span class="nam">character_id</span> <span class="op">==</span> <span class="nam">protagonist</span><span class="op">.</span><span class="nam">id</span> <span class="key">for</span> <span class="nam">p</span> <span class="key">in</span> <span class="nam">event</span><span class="op">.</span><span class="nam">participants</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">                        <span class="nam">protagonist_participation</span> <span class="op">+=</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">                <span class="nam">participation_rate</span> <span class="op">=</span> <span class="nam">protagonist_participation</span> <span class="op">/</span> <span class="nam">len</span><span class="op">(</span><span class="nam">critical_events</span><span class="op">)</span> <span class="key">if</span> <span class="nam">critical_events</span> <span class="key">else</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">                <span class="key">if</span> <span class="nam">participation_rate</span> <span class="op">&lt;</span> <span class="num">0.5</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">                    <span class="nam">context</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20027;&#35282;</span><span class="op">{</span><span class="nam">protagonist</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">&#22312;&#20851;&#38190;&#20107;&#20214;&#20013;&#30340;&#21442;&#19982;&#24230;&#36739;&#20302;(</span><span class="op">{</span><span class="nam">participation_rate</span><span class="op">:</span><span class="fst">.1%</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_fb48bbbbe924ff15_exceptions_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_fb48bbbbe924ff15_rules_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</footer>
</body>
</html>
