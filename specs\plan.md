# Implementation Plan: 笔落App - AI辅助小说创作平台

**Branch**: `002-spec-improvement` | **Date**: 2025-09-12 | **Spec**: `specs/002-/spec.md`
**Input**: Feature specification from `specs/002-/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file (e.g., `CLAUDE.md` for Claude Code, `.github/copilot-instructions.md` for GitHub Copilot, or `GEMINI.md` for Gemini CLI).
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phases 2-4 are executed by other commands:
- Phase 2: /tasks command creates tasks.md
- Phase 3-4: Implementation execution (manual or via tools)

## Summary
笔落App是一个Windows桌面应用程序，使用纯Python开发，为小说作者提供AI辅助创作功能。应用程序将采用本地文件存储（JSON格式），支持项目创建、故事结构管理、AI内容生成、角色场景事件管理等功能。技术栈基于Python 3.11+，使用PyQt6用于GUI，本地文件存储避免数据库依赖，集成多个AI服务商API进行智能内容生成。

## Technical Context
**Language/Version**: Python 3.11+ (Windows兼容版本)  
**Primary Dependencies**: PyQt6 (GUI), requests (API调用), python-dotenv (配置管理), pytest (测试)  
**Storage**: 本地JSON文件存储（无数据库），项目文件使用自定义二进制格式备份  
**Testing**: pytest + pytest-qt (GUI测试)，覆盖率目标 > 80%  
**Target Platform**: Windows 10/11 桌面应用程序
**Project Type**: single (单项目桌面应用)  
**Performance Goals**: 启动时间 < 3秒，界面响应 < 100ms，文件加载 < 2秒（10MB项目）  
**Constraints**: 纯Python实现，无数据库依赖，离线核心功能，AI功能需网络  
**Scale/Scope**: 单个作者使用，支持大型小说项目（50+章节，100+角色），5个并发项目

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 1个主项目 (符合3个最大限制)
- Using framework directly? 是，直接使用PyQt6，无额外封装
- Single data model? 是，使用统一的JSON数据模型，无DTOs
- Avoiding patterns? 是，避免过度设计模式，使用简单直接的业务逻辑

**Architecture**:
- EVERY feature as library? 是，核心功能模块化设计
- Libraries listed: 
  - core: 核心数据模型和业务逻辑
  - ai: AI服务集成和内容生成
  - storage: 文件存储和序列化
  - ui: 用户界面组件
- CLI per library: 主要提供GUI界面，CLI作为辅助工具
- Library docs: 使用标准Python docstring和README文档

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? 是，严格执行TDD流程
- Git commits show tests before implementation? 是，测试先行提交
- Order: Contract→Integration→E2E→Unit strictly followed? 是，按顺序执行
- Real dependencies used? 使用真实文件IO，AI服务使用测试模式
- Integration tests for: 新库、合约变更、共享schema都有集成测试
- FORBIDDEN: 禁止实现先于测试，禁止跳过RED阶段

**Observability**:
- Structured logging included? 是，使用Python logging模块
- Frontend logs → backend? 单机应用，日志统一到文件
- Error context sufficient? 是，提供详细的错误上下文和用户友好提示

**Versioning**:
- Version number assigned? 是，使用语义化版本 MAJOR.MINOR.PATCH
- BUILD increments on every change? 是，每次变更更新版本号
- Breaking changes handled? 是，提供数据迁移工具和版本兼容性检查

## Project Structure

### Documentation (this feature)
```
specs/[###-feature]/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Option 1: Single project (DEFAULT)
src/
├── models/
├── services/
├── cli/
└── lib/

tests/
├── contract/
├── integration/
└── unit/

# Option 2: Web application (when "frontend" + "backend" detected)
backend/
├── src/
│   ├── models/
│   ├── services/
│   └── api/
└── tests/

frontend/
├── src/
│   ├── components/
│   ├── pages/
│   └── services/
└── tests/

# Option 3: Mobile + API (when "iOS/Android" detected)
api/
└── [same as backend above]

ios/ or android/
└── [platform-specific structure]
```

**Structure Decision**: Option 1 (单项目结构) - 桌面应用程序，无需前后端分离

## Phase 0: Outline & Research
1. **Extract unknowns from Technical Context** above:
   - For each NEEDS CLARIFICATION → research task
   - For each dependency → best practices task
   - For each integration → patterns task

2. **Generate and dispatch research agents**:
   ```
   For each unknown in Technical Context:
     Task: "Research {unknown} for {feature context}"
   For each technology choice:
     Task: "Find best practices for {tech} in {domain}"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: [what was chosen]
   - Rationale: [why chosen]
   - Alternatives considered: [what else evaluated]

**Output**: research.md with all NEEDS CLARIFICATION resolved

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Entity name, fields, relationships
   - Validation rules from requirements
   - State transitions if applicable

2. **Generate API contracts** from functional requirements:
   - For each user action → endpoint
   - Use standard REST/GraphQL patterns
   - Output OpenAPI/GraphQL schema to `/contracts/`

3. **Generate contract tests** from contracts:
   - One test file per endpoint
   - Assert request/response schemas
   - Tests must fail (no implementation yet)

4. **Extract test scenarios** from user stories:
   - Each story → integration test scenario
   - Quickstart test = story validation steps

5. **Update agent file incrementally** (O(1) operation):
   - Run `/scripts/update-agent-context.sh [claude|gemini|copilot]` for your AI assistant
   - If exists: Add only NEW tech from current plan
   - Preserve manual additions between markers
   - Update recent changes (keep last 3)
   - Keep under 150 lines for token efficiency
   - Output to repository root

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- 基于数据模型创建核心实体实现任务
- 根据API合约创建接口测试任务
- 从用户故事生成集成测试场景
- 按模块划分开发任务（core, storage, ai, ui）
- 每个功能需求对应一个或多个实现任务

**具体任务分类**:
1. **核心模块任务**: 数据模型实现、业务逻辑验证
2. **存储模块任务**: 文件IO实现、序列化、备份恢复
3. **AI模块任务**: 服务集成、内容生成、优化处理
4. **UI模块任务**: 界面组件、主题管理、用户体验
5. **测试任务**: 单元测试、集成测试、GUI测试
6. **构建部署任务**: 打包、安装程序、文档

**Ordering Strategy**:
- TDD顺序: 测试先行，然后实现
- 依赖顺序: 数据模型 → 核心服务 → AI集成 → UI界面
- 并行标记: 独立模块可以并行开发
- 优先级: 核心功能优先，高级功能后续

**预计输出**: 30-40个编号有序的任务，包含：
- 15-20个核心实现任务
- 10-15个测试任务  
- 5个构建部署任务

**IMPORTANT**: 此阶段由/tasks命令执行，NOT由/plan执行

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| [e.g., 4th project] | [current need] | [why 3 projects insufficient] |
| [e.g., Repository pattern] | [specific problem] | [why direct DB access insufficient] |


## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [x] Phase 0: Research complete (/plan command)
- [x] Phase 1: Design complete (/plan command)
- [x] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [x] Post-Design Constitution Check: PASS
- [x] All NEEDS CLARIFICATION resolved
- [ ] Complexity deviations documented

---
*Based on Constitution v2.1.1 - See `/memory/constitution.md`*