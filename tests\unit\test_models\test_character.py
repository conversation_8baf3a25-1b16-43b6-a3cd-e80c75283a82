"""角色模型测试

测试Character、CharacterAppearance、CharacterPersonality等角色相关模型
"""

import pytest
from datetime import datetime, date
from uuid import uuid4

from src.core.models.character import (
    Character, CharacterAppearance, CharacterPersonality, 
    CharacterBackground, CharacterRelationship, CharacterValidator
)
from src.core.models.enums import Gender, RelationType, RelationshipStatus, ElementType


class TestCharacterAppearance:
    """CharacterAppearance测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        appearance = CharacterAppearance()
        
        assert appearance.age is None
        assert appearance.height is None
        assert appearance.weight is None
        assert appearance.build == ""
        assert appearance.hair_color == ""
        assert appearance.hair_style == ""
        assert appearance.eye_color == ""
        assert appearance.skin_tone == ""
        assert appearance.distinguishing_features == []
        assert appearance.scars_marks == []
        assert appearance.clothing_style == ""
        assert appearance.accessories == []
        assert appearance.overall_description == ""
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        appearance = CharacterAppearance(
            age=25,
            height="175cm",
            weight="65kg",
            build="中等",
            hair_color="黑色",
            eye_color="棕色"
        )
        
        assert appearance.age == 25
        assert appearance.height == "175cm"
        assert appearance.weight == "65kg"
        assert appearance.build == "中等"
        assert appearance.hair_color == "黑色"
        assert appearance.eye_color == "棕色"
    
    def test_validate_age(self):
        """测试年龄验证"""
        # 有效年龄
        appearance = CharacterAppearance(age=30)
        assert appearance.age == 30
        
        # 边界值
        appearance = CharacterAppearance(age=0)
        assert appearance.age == 0
        
        appearance = CharacterAppearance(age=200)
        assert appearance.age == 200
        
        # 无效年龄（负数）
        with pytest.raises(ValueError, match="年龄必须在0-200之间"):
            CharacterAppearance(age=-1)
        
        # 无效年龄（超过200）
        with pytest.raises(ValueError, match="年龄必须在0-200之间"):
            CharacterAppearance(age=201)


class TestCharacterPersonality:
    """CharacterPersonality测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        personality = CharacterPersonality()
        
        assert personality.core_traits == []
        assert personality.positive_traits == []
        assert personality.negative_traits == []
        assert personality.habits == []
        assert personality.mannerisms == []
        assert personality.speech_patterns == []
        assert personality.emotional_tendencies == []
        assert personality.fears == []
        assert personality.desires == []
        assert personality.values == []
        assert personality.beliefs == []
        assert personality.moral_code == ""
        assert personality.skills == []
        assert personality.talents == []
        assert personality.weaknesses == []
        assert personality.personality_summary == ""
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        personality = CharacterPersonality(
            core_traits=["勇敢", "善良"],
            positive_traits=["诚实", "忠诚"],
            negative_traits=["固执", "冲动"],
            personality_summary="一个勇敢善良但有些固执的人"
        )
        
        assert personality.core_traits == ["勇敢", "善良"]
        assert personality.positive_traits == ["诚实", "忠诚"]
        assert personality.negative_traits == ["固执", "冲动"]
        assert personality.personality_summary == "一个勇敢善良但有些固执的人"


class TestCharacterBackground:
    """CharacterBackground测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        background = CharacterBackground()
        
        assert background.birthdate is None
        assert background.birthplace == ""
        assert background.nationality == ""
        assert background.ethnicity == ""
        assert background.family_status == ""
        assert background.parents == []
        assert background.siblings == []
        assert background.children == []
        assert background.education_level == ""
        assert background.schools == []
        assert background.degrees == []
        assert background.current_occupation == ""
        assert background.previous_jobs == []
        assert background.career_goals == ""
        assert background.social_class == ""
        assert background.economic_status == ""
        assert background.political_affiliation == ""
        assert background.life_events == []
        assert background.traumas == []
        assert background.achievements == []
        assert background.background_summary == ""
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        birth_date = date(1990, 5, 15)
        background = CharacterBackground(
            birthdate=birth_date,
            birthplace="北京",
            nationality="中国",
            current_occupation="软件工程师",
            education_level="本科"
        )
        
        assert background.birthdate == birth_date
        assert background.birthplace == "北京"
        assert background.nationality == "中国"
        assert background.current_occupation == "软件工程师"
        assert background.education_level == "本科"


class TestCharacterRelationship:
    """CharacterRelationship测试类"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        target_id = uuid4()
        relationship = CharacterRelationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND
        )
        
        assert relationship.target_character == target_id
        assert relationship.relationship_type == RelationType.FRIEND.value
        assert relationship.relationship_status == RelationshipStatus.ACTIVE
        assert relationship.relationship_name == ""
        assert relationship.description == ""
        assert relationship.intimacy_level == 0.5
        assert relationship.trust_level == 0.5
        assert relationship.conflict_level == 0.0
        assert relationship.relationship_start is None
        assert relationship.relationship_end is None
        assert relationship.relationship_history == []
        assert relationship.mutual_influence == ""
        assert relationship.shared_experiences == []
    
    def test_init_full(self):
        """测试完整初始化"""
        target_id = uuid4()
        start_time = datetime.now()
        
        relationship = CharacterRelationship(
            target_character=target_id,
            relationship_type=RelationType.ROMANTIC,
            relationship_name="恋人",
            description="深爱的恋人",
            intimacy_level=0.9,
            trust_level=0.8,
            conflict_level=0.1,
            relationship_start=start_time
        )
        
        assert relationship.target_character == target_id
        assert relationship.relationship_type == RelationType.ROMANTIC.value
        assert relationship.relationship_name == "恋人"
        assert relationship.description == "深爱的恋人"
        assert relationship.intimacy_level == 0.9
        assert relationship.trust_level == 0.8
        assert relationship.conflict_level == 0.1
        assert relationship.relationship_start == start_time
    
    def test_validate_levels(self):
        """测试关系级别验证"""
        target_id = uuid4()
        
        # 有效级别
        relationship = CharacterRelationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND,
            intimacy_level=0.7,
            trust_level=0.8,
            conflict_level=0.2
        )
        assert relationship.intimacy_level == 0.7
        assert relationship.trust_level == 0.8
        assert relationship.conflict_level == 0.2
        
        # 边界值
        relationship = CharacterRelationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND,
            intimacy_level=0.0,
            trust_level=1.0,
            conflict_level=0.0
        )
        assert relationship.intimacy_level == 0.0
        assert relationship.trust_level == 1.0
        assert relationship.conflict_level == 0.0
        
        # 无效级别（小于0）
        with pytest.raises(ValueError, match="关系级别必须在0.0-1.0之间"):
            CharacterRelationship(
                target_character=target_id,
                relationship_type=RelationType.FRIEND,
                intimacy_level=-0.1
            )
        
        # 无效级别（大于1）
        with pytest.raises(ValueError, match="关系级别必须在0.0-1.0之间"):
            CharacterRelationship(
                target_character=target_id,
                relationship_type=RelationType.FRIEND,
                trust_level=1.1
            )
    
    def test_validate_relationship_duration(self):
        """测试关系持续时间验证"""
        target_id = uuid4()
        start_time = datetime.now()
        end_time = datetime.now()
        
        # 有效时间范围
        relationship = CharacterRelationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND,
            relationship_start=start_time,
            relationship_end=end_time
        )
        assert relationship.relationship_start == start_time
        assert relationship.relationship_end == end_time
        
        # 无效时间范围（结束时间早于开始时间）
        with pytest.raises(ValueError, match="关系结束时间必须晚于开始时间"):
            CharacterRelationship(
                target_character=target_id,
                relationship_type=RelationType.FRIEND,
                relationship_start=end_time,
                relationship_end=start_time
            )


class TestCharacter:
    """Character测试类"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        character = Character(full_name="张三")
        
        assert character.element_type == ElementType.CHARACTER
        assert character.full_name == "张三"
        assert character.nickname == ""
        assert character.title == ""
        assert character.gender == Gender.UNKNOWN
        assert isinstance(character.appearance, CharacterAppearance)
        assert isinstance(character.personality, CharacterPersonality)
        assert isinstance(character.background, CharacterBackground)
        assert character.character_relationships == []
        assert character.story_role == ""
        assert character.character_arc == ""
        assert character.motivation == ""
        assert character.goals == []
        assert character.conflicts == []
        assert character.character_development == []
        assert character.key_moments == []
        assert character.is_alive is True
        assert character.current_location is None
    
    def test_init_full(self):
        """测试完整初始化"""
        character = Character(
            full_name="李小红",
            nickname="小红",
            title="剑客",
            gender=Gender.FEMALE,
            story_role="主角",
            motivation="寻找失踪的父亲",
            is_alive=True
        )
        
        assert character.full_name == "李小红"
        assert character.nickname == "小红"
        assert character.title == "剑客"
        assert character.gender == Gender.FEMALE.value
        assert character.story_role == "主角"
        assert character.motivation == "寻找失踪的父亲"
        assert character.is_alive is True
    
    def test_validate_full_name(self):
        """测试全名验证"""
        # 有效全名
        character = Character(full_name="王五")
        assert character.full_name == "王五"
        
        # 全名会被trim
        character = Character(full_name="  赵六  ")
        assert character.full_name == "赵六"
        
        # 空全名
        with pytest.raises(ValueError, match="角色全名不能为空"):
            Character(full_name="")
        
        # 只有空格的全名
        with pytest.raises(ValueError, match="角色全名不能为空"):
            Character(full_name="   ")
        
        # 超长全名
        with pytest.raises(ValueError, match="角色全名不能超过100个字符"):
            Character(full_name="a" * 101)
    
    def test_add_relationship(self):
        """测试添加关系"""
        character = Character(full_name="主角")
        target_id = uuid4()
        
        character.add_relationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND,
            relationship_name="好友",
            description="童年好友",
            intimacy_level=0.8
        )
        
        assert len(character.character_relationships) == 1
        relationship = character.character_relationships[0]
        assert relationship.target_character == target_id
        assert relationship.relationship_type == RelationType.FRIEND.value
        assert relationship.relationship_name == "好友"
        assert relationship.description == "童年好友"
        assert relationship.intimacy_level == 0.8
        assert relationship.relationship_start is not None
    
    def test_update_relationship(self):
        """测试更新关系"""
        character = Character(full_name="主角")
        target_id = uuid4()
        
        # 先添加关系
        character.add_relationship(target_id, RelationType.FRIEND)
        
        # 更新关系
        success = character.update_relationship(
            target_character=target_id,
            intimacy_level=0.9,
            description="更新的描述"
        )
        
        assert success is True
        relationship = character.get_relationship(target_id)
        assert relationship.intimacy_level == 0.9
        assert relationship.description == "更新的描述"
        
        # 更新不存在的关系
        other_id = uuid4()
        success = character.update_relationship(target_character=other_id, intimacy_level=0.5)
        assert success is False
    
    def test_remove_relationship(self):
        """测试移除关系"""
        character = Character(full_name="主角")
        target_id = uuid4()
        
        # 先添加关系
        character.add_relationship(target_id, RelationType.FRIEND)
        assert len(character.character_relationships) == 1
        
        # 移除关系
        success = character.remove_relationship(target_id)
        assert success is True
        assert len(character.character_relationships) == 0
        
        # 移除不存在的关系
        success = character.remove_relationship(target_id)
        assert success is False
    
    def test_get_relationship(self):
        """测试获取关系"""
        character = Character(full_name="主角")
        target_id = uuid4()
        
        # 获取不存在的关系
        relationship = character.get_relationship(target_id)
        assert relationship is None
        
        # 添加关系后获取
        character.add_relationship(target_id, RelationType.FRIEND)
        relationship = character.get_relationship(target_id)
        assert relationship is not None
        assert relationship.target_character == target_id
        assert relationship.relationship_type == RelationType.FRIEND.value
    
    def test_get_relationships_by_type(self):
        """测试按类型获取关系"""
        character = Character(full_name="主角")
        friend_id = uuid4()
        enemy_id = uuid4()
        
        character.add_relationship(friend_id, RelationType.FRIEND)
        character.add_relationship(enemy_id, RelationType.ENEMY)
        
        friends = character.get_relationships_by_type(RelationType.FRIEND)
        enemies = character.get_relationships_by_type(RelationType.ENEMY)
        lovers = character.get_relationships_by_type(RelationType.ROMANTIC)
        
        assert len(friends) == 1
        assert friends[0].target_character == friend_id
        assert len(enemies) == 1
        assert enemies[0].target_character == enemy_id
        assert len(lovers) == 0
    
    def test_add_character_development(self):
        """测试添加角色发展"""
        character = Character(full_name="主角")
        chapter_id = uuid4()
        
        character.add_character_development(
            chapter_id=chapter_id,
            development="学会了宽恕",
            development_type="emotional_growth"
        )
        
        assert len(character.character_development) == 1
        development = character.character_development[0]
        assert development['chapter_id'] == chapter_id
        assert development['development'] == "学会了宽恕"
        assert development['development_type'] == "emotional_growth"
        assert 'timestamp' in development
    
    def test_add_key_moment(self):
        """测试添加关键时刻"""
        character = Character(full_name="主角")
        chapter_id = uuid4()
        
        character.add_key_moment(
            chapter_id=chapter_id,
            moment="与父亲重逢",
            impact="解开了心结"
        )
        
        assert len(character.key_moments) == 1
        moment = character.key_moments[0]
        assert moment['chapter_id'] == chapter_id
        assert moment['moment'] == "与父亲重逢"
        assert moment['impact'] == "解开了心结"
        assert 'timestamp' in moment
    
    def test_get_display_name(self):
        """测试获取显示名称"""
        # 只有全名
        character = Character(full_name="张三")
        assert character.get_display_name() == "张三"
        
        # 有昵称
        character = Character(full_name="李四", nickname="小四")
        assert character.get_display_name() == "李四 (小四)"
    
    def test_get_age(self):
        """测试获取年龄"""
        character = Character(full_name="测试")
        
        # 没有年龄信息
        assert character.get_age() is None
        
        # 从外貌获取年龄
        character.appearance.age = 25
        assert character.get_age() == 25
        
        # 从生日计算年龄（优先级更高）
        character.background.birthdate = date(1990, 1, 1)
        age = character.get_age()
        assert age is not None
        assert age >= 30  # 假设当前年份大于2020
    
    def test_calculate_relationship_score(self):
        """测试计算关系得分"""
        character = Character(full_name="主角")
        target_id = uuid4()
        
        # 没有关系
        score = character.calculate_relationship_score(target_id)
        assert score == 0.0
        
        # 添加关系
        character.add_relationship(
            target_character=target_id,
            relationship_type=RelationType.FRIEND,
            intimacy_level=0.8,
            trust_level=0.6,
            conflict_level=0.2
        )
        
        score = character.calculate_relationship_score(target_id)
        # (0.8 + 0.6) / 2 - 0.2 / 2 = 0.7 - 0.1 = 0.6
        assert abs(score - 0.6) < 0.001


class TestCharacterValidator:
    """CharacterValidator测试类"""
    
    def test_validate_valid_character(self):
        """测试验证有效角色"""
        character = Character(
            full_name="有效角色",
            nickname="昵称",
            title="称号",
            story_role="主角",
            character_arc="成长弧线",
            motivation="寻找真相"
        )
        
        validator = CharacterValidator()
        result = validator.validate(character)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_invalid_character(self):
        """测试验证无效角色"""
        # 先创建一个有效的角色，然后修改为无效状态
        character = Character(full_name="测试角色")

        # 直接修改字段为无效值（绕过Pydantic验证）
        character.__dict__['full_name'] = ""  # 空全名
        character.__dict__['nickname'] = "a" * 51  # 超长昵称
        character.__dict__['title'] = "b" * 51  # 超长称号
        character.__dict__['story_role'] = "c" * 101  # 超长故事角色
        character.__dict__['character_arc'] = "d" * 501  # 超长角色弧线
        character.__dict__['motivation'] = "e" * 501  # 超长动机
        character.appearance.__dict__['age'] = 250  # 无效年龄

        validator = CharacterValidator()
        result = validator.validate(character)

        assert result.is_valid is False
        assert len(result.errors) > 0
