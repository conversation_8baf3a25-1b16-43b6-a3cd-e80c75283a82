"""备份管理器

提供数据备份和恢复功能，包括：
- 自动备份策略
- 增量备份
- 备份压缩
- 备份验证和恢复
"""

import shutil
import zipfile
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta
from uuid import UUID
import tempfile

from .file_storage import FileStorage
from .serializer import JSONSerializer


class BackupError(Exception):
    """备份错误"""
    pass


class BackupInfo:
    """备份信息"""
    
    def __init__(self, backup_id: str, project_id: UUID, backup_type: str,
                 created_at: datetime, file_path: Path, file_size: int,
                 checksum: str, metadata: Optional[Dict[str, Any]] = None):
        self.backup_id = backup_id
        self.project_id = project_id
        self.backup_type = backup_type
        self.created_at = created_at
        self.file_path = file_path
        self.file_size = file_size
        self.checksum = checksum
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'backup_id': self.backup_id,
            'project_id': str(self.project_id),
            'backup_type': self.backup_type,
            'created_at': self.created_at.isoformat(),
            'file_path': str(self.file_path),
            'file_size': self.file_size,
            'checksum': self.checksum,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupInfo':
        """从字典创建"""
        return cls(
            backup_id=data['backup_id'],
            project_id=UUID(data['project_id']),
            backup_type=data['backup_type'],
            created_at=datetime.fromisoformat(data['created_at']),
            file_path=Path(data['file_path']),
            file_size=data['file_size'],
            checksum=data['checksum'],
            metadata=data.get('metadata', {})
        )


class BackupManager:
    """备份管理器
    
    提供项目数据的备份和恢复功能，包括：
    - 全量备份和增量备份
    - 自动备份策略
    - 备份压缩和验证
    - 备份清理和恢复
    """
    
    def __init__(self, backup_dir: Union[str, Path], file_storage: FileStorage):
        """初始化备份管理器
        
        Args:
            backup_dir: 备份目录
            file_storage: 文件存储管理器
        """
        self.backup_dir = Path(backup_dir)
        self.file_storage = file_storage
        self.serializer = JSONSerializer()
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份索引文件
        self.index_file = self.backup_dir / "backup_index.json"
        self._load_index()
    
    def _load_index(self):
        """加载备份索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    self.backup_index = {
                        backup_id: BackupInfo.from_dict(data)
                        for backup_id, data in index_data.items()
                    }
            except Exception:
                self.backup_index = {}
        else:
            self.backup_index = {}
    
    def _save_index(self):
        """保存备份索引"""
        try:
            index_data = {
                backup_id: info.to_dict()
                for backup_id, info in self.backup_index.items()
            }
            
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            raise BackupError(f"保存备份索引失败: {e}") from e
    
    def create_backup(self, project_id: UUID, backup_type: str = "full",
                     description: str = "") -> BackupInfo:
        """创建备份
        
        Args:
            project_id: 项目ID
            backup_type: 备份类型 (full/incremental/manual)
            description: 备份描述
            
        Returns:
            备份信息
        """
        try:
            # 生成备份ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_id = f"{project_id}_{backup_type}_{timestamp}"
            
            # 创建备份文件路径
            backup_file = self.backup_dir / f"{backup_id}.zip"
            
            # 获取项目路径
            project_path = self.file_storage.base_path / str(project_id)
            if not project_path.exists():
                raise BackupError(f"项目不存在: {project_id}")
            
            # 创建压缩备份
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in project_path.rglob('*'):
                    if file_path.is_file():
                        # 计算相对路径
                        arcname = file_path.relative_to(project_path)
                        zipf.write(file_path, arcname)
            
            # 计算校验和
            checksum = self._calculate_checksum(backup_file)
            
            # 创建备份信息
            backup_info = BackupInfo(
                backup_id=backup_id,
                project_id=project_id,
                backup_type=backup_type,
                created_at=datetime.now(),
                file_path=backup_file,
                file_size=backup_file.stat().st_size,
                checksum=checksum,
                metadata={
                    'description': description,
                    'project_path': str(project_path)
                }
            )
            
            # 添加到索引
            self.backup_index[backup_id] = backup_info
            self._save_index()
            
            return backup_info
            
        except Exception as e:
            raise BackupError(f"创建备份失败: {e}") from e
    
    def restore_backup(self, backup_id: str, target_project_id: Optional[UUID] = None) -> UUID:
        """恢复备份
        
        Args:
            backup_id: 备份ID
            target_project_id: 目标项目ID（可选，默认使用原项目ID）
            
        Returns:
            恢复的项目ID
        """
        try:
            if backup_id not in self.backup_index:
                raise BackupError(f"备份不存在: {backup_id}")
            
            backup_info = self.backup_index[backup_id]
            
            # 验证备份文件
            if not self._verify_backup(backup_info):
                raise BackupError(f"备份文件损坏: {backup_id}")
            
            # 确定目标项目ID
            if target_project_id is None:
                target_project_id = backup_info.project_id
            
            # 创建目标项目路径
            target_path = self.file_storage.base_path / str(target_project_id)
            
            # 如果目标路径存在，先备份
            if target_path.exists():
                backup_existing = target_path.with_suffix('.backup')
                if backup_existing.exists():
                    shutil.rmtree(backup_existing)
                shutil.move(target_path, backup_existing)
            
            try:
                # 解压备份文件
                with zipfile.ZipFile(backup_info.file_path, 'r') as zipf:
                    zipf.extractall(target_path)
                
                # 更新项目配置中的ID（如果需要）
                if target_project_id != backup_info.project_id:
                    self._update_project_id(target_path, target_project_id)
                
                return target_project_id
                
            except Exception as e:
                # 恢复失败，还原原有数据
                if target_path.exists():
                    shutil.rmtree(target_path)
                
                backup_existing = target_path.with_suffix('.backup')
                if backup_existing.exists():
                    shutil.move(backup_existing, target_path)
                
                raise e
            
        except Exception as e:
            raise BackupError(f"恢复备份失败: {e}") from e
    
    def list_backups(self, project_id: Optional[UUID] = None) -> List[BackupInfo]:
        """列出备份
        
        Args:
            project_id: 项目ID（可选，如果提供则只返回该项目的备份）
            
        Returns:
            备份信息列表
        """
        backups = list(self.backup_index.values())
        
        if project_id:
            backups = [backup for backup in backups if backup.project_id == project_id]
        
        # 按创建时间倒序排列
        backups.sort(key=lambda x: x.created_at, reverse=True)
        
        return backups
    
    def delete_backup(self, backup_id: str) -> bool:
        """删除备份
        
        Args:
            backup_id: 备份ID
            
        Returns:
            是否删除成功
        """
        try:
            if backup_id not in self.backup_index:
                return False
            
            backup_info = self.backup_index[backup_id]
            
            # 删除备份文件
            if backup_info.file_path.exists():
                backup_info.file_path.unlink()
            
            # 从索引中移除
            del self.backup_index[backup_id]
            self._save_index()
            
            return True
            
        except Exception:
            return False
    
    def cleanup_old_backups(self, project_id: UUID, keep_count: int = 10,
                           keep_days: int = 30) -> int:
        """清理旧备份
        
        Args:
            project_id: 项目ID
            keep_count: 保留的备份数量
            keep_days: 保留的天数
            
        Returns:
            删除的备份数量
        """
        project_backups = [
            backup for backup in self.backup_index.values()
            if backup.project_id == project_id
        ]
        
        # 按创建时间排序
        project_backups.sort(key=lambda x: x.created_at, reverse=True)
        
        deleted_count = 0
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        for i, backup in enumerate(project_backups):
            # 保留最新的指定数量的备份
            if i < keep_count:
                continue
            
            # 保留指定天数内的备份
            if backup.created_at > cutoff_date:
                continue
            
            # 删除旧备份
            if self.delete_backup(backup.backup_id):
                deleted_count += 1
        
        return deleted_count
    
    def _verify_backup(self, backup_info: BackupInfo) -> bool:
        """验证备份文件"""
        try:
            if not backup_info.file_path.exists():
                return False
            
            # 验证文件大小
            if backup_info.file_path.stat().st_size != backup_info.file_size:
                return False
            
            # 验证校验和
            current_checksum = self._calculate_checksum(backup_info.file_path)
            if current_checksum != backup_info.checksum:
                return False
            
            # 验证ZIP文件完整性
            with zipfile.ZipFile(backup_info.file_path, 'r') as zipf:
                if zipf.testzip() is not None:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _update_project_id(self, project_path: Path, new_project_id: UUID):
        """更新项目配置中的ID"""
        try:
            config_file = project_path / "project.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                config_data['project_id'] = str(new_project_id)
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
        except Exception:
            pass
    
    def get_backup_stats(self) -> Dict[str, Any]:
        """获取备份统计信息"""
        total_backups = len(self.backup_index)
        total_size = sum(backup.file_size for backup in self.backup_index.values())
        
        # 按项目分组统计
        project_stats = {}
        for backup in self.backup_index.values():
            project_id = str(backup.project_id)
            if project_id not in project_stats:
                project_stats[project_id] = {'count': 0, 'size': 0}
            
            project_stats[project_id]['count'] += 1
            project_stats[project_id]['size'] += backup.file_size
        
        return {
            'total_backups': total_backups,
            'total_size_bytes': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'project_stats': project_stats
        }
