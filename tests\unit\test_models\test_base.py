"""基础模型测试

测试BaseModel、ValidationResult、ModelValidator等基础类的功能
"""

import pytest
from datetime import datetime
from uuid import UUID, uuid4

from src.core.models.base import BaseModel, ValidationResult, ModelValidator, VersionedModel


class TestValidationResult:
    """ValidationResult测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        result = ValidationResult()
        assert result.is_valid is True
        assert result.errors == []
        assert result.warnings == []
        assert result.has_errors is False
        assert result.has_warnings is False
    
    def test_add_error(self):
        """测试添加错误"""
        result = ValidationResult()
        result.add_error("测试错误")
        
        assert result.is_valid is False
        assert result.has_errors is True
        assert "测试错误" in result.errors
    
    def test_add_warning(self):
        """测试添加警告"""
        result = ValidationResult()
        result.add_warning("测试警告")
        
        assert result.is_valid is True  # 警告不影响有效性
        assert result.has_warnings is True
        assert "测试警告" in result.warnings
    
    def test_merge(self):
        """测试合并验证结果"""
        result1 = ValidationResult()
        result1.add_error("错误1")
        result1.add_warning("警告1")
        
        result2 = ValidationResult()
        result2.add_error("错误2")
        result2.add_warning("警告2")
        
        result1.merge(result2)
        
        assert result1.is_valid is False
        assert len(result1.errors) == 2
        assert len(result1.warnings) == 2
        assert "错误1" in result1.errors
        assert "错误2" in result1.errors
        assert "警告1" in result1.warnings
        assert "警告2" in result1.warnings


class TestModel(BaseModel):
    """测试用的模型类"""
    name: str = "test"
    value: int = 0


class TestBaseModel:
    """BaseModel测试类"""
    
    def test_init_with_defaults(self):
        """测试默认初始化"""
        model = TestModel()
        
        assert isinstance(model.id, UUID)
        assert isinstance(model.created_at, datetime)
        assert isinstance(model.updated_at, datetime)
        assert model.version == 1
        assert model.custom_fields == {}
        assert model.tags == []
        assert model.name == "test"
        assert model.value == 0
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        test_id = uuid4()
        test_time = datetime.now()
        
        model = TestModel(
            id=test_id,
            created_at=test_time,
            name="custom",
            value=42
        )
        
        assert model.id == test_id
        assert model.created_at == test_time
        assert model.name == "custom"
        assert model.value == 42
    
    def test_update_timestamp(self):
        """测试更新时间戳"""
        model = TestModel()
        original_updated_at = model.updated_at
        original_version = model.version
        
        # 等待一小段时间确保时间戳不同
        import time
        time.sleep(0.01)
        
        model.update_timestamp()
        
        assert model.updated_at > original_updated_at
        assert model.version == original_version + 1
    
    def test_to_dict(self):
        """测试转换为字典"""
        model = TestModel(name="test_dict", value=123)
        data = model.to_dict()
        
        assert isinstance(data, dict)
        assert data['name'] == "test_dict"
        assert data['value'] == 123
        assert 'id' in data
        assert 'created_at' in data
    
    def test_to_json(self):
        """测试转换为JSON"""
        model = TestModel(name="test_json", value=456)
        json_str = model.to_json()
        
        assert isinstance(json_str, str)
        assert '"name": "test_json"' in json_str
        assert '"value": 456' in json_str
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            'name': 'from_dict',
            'value': 789,
            'custom_fields': {'key': 'value'}
        }
        
        model = TestModel.from_dict(data)
        
        assert model.name == 'from_dict'
        assert model.value == 789
        assert model.custom_fields == {'key': 'value'}
    
    def test_from_json(self):
        """测试从JSON创建"""
        json_str = '{"name": "from_json", "value": 999}'
        model = TestModel.from_json(json_str)
        
        assert model.name == 'from_json'
        assert model.value == 999
    
    def test_calculate_checksum(self):
        """测试计算校验和"""
        model1 = TestModel(name="checksum_test", value=100)
        model2 = TestModel(name="checksum_test", value=100)
        model3 = TestModel(name="different", value=100)
        
        checksum1 = model1.calculate_checksum()
        checksum2 = model2.calculate_checksum()
        checksum3 = model3.calculate_checksum()
        
        assert isinstance(checksum1, str)
        assert len(checksum1) == 64  # SHA256 hex digest length
        assert checksum1 == checksum2  # 相同数据应该有相同校验和
        assert checksum1 != checksum3  # 不同数据应该有不同校验和
    
    def test_validate_model(self):
        """测试模型验证"""
        model = TestModel()
        result = model.validate_model()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid is True
    
    def test_clone(self):
        """测试克隆对象"""
        original = TestModel(name="original", value=42)
        original.add_tag("test_tag")
        
        cloned = original.clone()
        
        assert cloned.id != original.id
        assert cloned.name == original.name
        assert cloned.value == original.value
        assert cloned.tags == original.tags
        assert cloned.version == 1  # 新对象版本重置为1
    
    def test_merge_custom_fields(self):
        """测试合并自定义字段"""
        model = TestModel()
        model.custom_fields = {'existing': 'value'}
        original_version = model.version
        
        model.merge_custom_fields({'new': 'field', 'another': 'value'})
        
        assert model.custom_fields == {
            'existing': 'value',
            'new': 'field',
            'another': 'value'
        }
        assert model.version == original_version + 1
    
    def test_tag_operations(self):
        """测试标签操作"""
        model = TestModel()
        
        # 添加标签
        model.add_tag("tag1")
        assert "tag1" in model.tags
        assert model.has_tag("tag1")
        
        # 重复添加不会增加
        model.add_tag("tag1")
        assert model.tags.count("tag1") == 1
        
        # 添加多个标签
        model.add_tag("tag2")
        model.add_tag("tag3")
        assert len(model.tags) == 3
        
        # 移除标签
        model.remove_tag("tag2")
        assert "tag2" not in model.tags
        assert not model.has_tag("tag2")
        assert len(model.tags) == 2


class TestVersionedModel(VersionedModel):
    """测试用的版本化模型"""
    name: str = "versioned_test"


class TestVersionedModelClass:
    """VersionedModel测试类"""
    
    def test_create_version_snapshot(self):
        """测试创建版本快照"""
        model = TestVersionedModel(name="snapshot_test")
        original_version = model.version
        
        model.create_version_snapshot("测试快照")
        
        assert len(model.version_history) == 1
        assert model.version == original_version + 1
        
        snapshot = model.version_history[0]
        assert snapshot['version'] == original_version
        assert snapshot['message'] == "测试快照"
        assert 'timestamp' in snapshot
        assert 'data' in snapshot
    
    def test_get_version_history(self):
        """测试获取版本历史"""
        model = TestVersionedModel()
        model.create_version_snapshot("版本1")
        model.create_version_snapshot("版本2")
        
        history = model.get_version_history()
        
        assert len(history) == 2
        assert history[0]['message'] == "版本1"
        assert history[1]['message'] == "版本2"
        
        # 确保返回的是副本
        history.clear()
        assert len(model.version_history) == 2
    
    def test_restore_version(self):
        """测试恢复版本"""
        model = TestVersionedModel(name="original")
        model.create_version_snapshot("保存原始版本")
        
        # 修改数据
        model.name = "modified"
        model.create_version_snapshot("修改后版本")
        
        # 恢复到原始版本
        success = model.restore_version(1)
        
        assert success is True
        assert model.name == "original"
    
    def test_restore_nonexistent_version(self):
        """测试恢复不存在的版本"""
        model = TestVersionedModel()
        success = model.restore_version(999)
        
        assert success is False


class TestModelValidator:
    """ModelValidator测试类"""
    
    def test_validate_required_string(self):
        """测试验证必需字符串"""
        # 有效字符串
        result = ModelValidator.validate_required_string("valid", "测试字段")
        assert result.is_valid is True
        
        # 空字符串
        result = ModelValidator.validate_required_string("", "测试字段")
        assert result.is_valid is False
        assert "测试字段不能为空" in result.errors
        
        # None值
        result = ModelValidator.validate_required_string(None, "测试字段")
        assert result.is_valid is False
        
        # 超长字符串
        result = ModelValidator.validate_required_string("a" * 101, "测试字段", 100)
        assert result.is_valid is False
        assert "长度不能超过100个字符" in result.errors[0]
    
    def test_validate_optional_string(self):
        """测试验证可选字符串"""
        # None值应该通过
        result = ModelValidator.validate_optional_string(None, "测试字段")
        assert result.is_valid is True
        
        # 空字符串应该通过
        result = ModelValidator.validate_optional_string("", "测试字段")
        assert result.is_valid is True
        
        # 超长字符串应该失败
        result = ModelValidator.validate_optional_string("a" * 101, "测试字段", 100)
        assert result.is_valid is False
    
    def test_validate_range(self):
        """测试验证数值范围"""
        # 有效范围
        result = ModelValidator.validate_range(50, "测试字段", 0, 100)
        assert result.is_valid is True
        
        # 小于最小值
        result = ModelValidator.validate_range(-1, "测试字段", 0, 100)
        assert result.is_valid is False
        assert "不能小于0" in result.errors[0]
        
        # 大于最大值
        result = ModelValidator.validate_range(101, "测试字段", 0, 100)
        assert result.is_valid is False
        assert "不能大于100" in result.errors[0]
    
    def test_validate_list_length(self):
        """测试验证列表长度"""
        # 有效长度
        result = ModelValidator.validate_list_length([1, 2, 3], "测试字段", 1, 5)
        assert result.is_valid is True
        
        # 太短
        result = ModelValidator.validate_list_length([], "测试字段", 1, 5)
        assert result.is_valid is False
        assert "至少需要1个元素" in result.errors[0]
        
        # 太长
        result = ModelValidator.validate_list_length([1, 2, 3, 4, 5, 6], "测试字段", 1, 5)
        assert result.is_valid is False
        assert "最多只能有5个元素" in result.errors[0]
