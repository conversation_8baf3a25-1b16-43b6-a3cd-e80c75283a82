# 笔落App数据模型设计 v2.0

**版本**: 2.0  
**创建日期**: 2025-09-12  
**修订说明**: 基于架构分析的增强数据模型设计  
**状态**: 修订版

## 版本修订说明

### v2.0 主要改进
1. **增强关系设计**: 支持复杂的多对多关系和关系网络
2. **数据一致性机制**: 增加校验和、事务支持、冲突检测
3. **扩展性优化**: 支持自定义字段、标签系统、插件扩展
4. **性能优化**: 优化存储结构，支持分片和索引
5. **版本控制**: 内置版本管理和历史追踪

---

## 1. 核心实体关系图

### 1.1 实体关系概览
```
WritingProject (1) ──┬── (1) StoryOutline
                     ├── (1..*) StoryArc
                     ├── (1..*) StoryChapter
                     ├── (0..*) StoryCharacter
                     ├── (0..*) StoryScene
                     ├── (0..*) StoryEvent
                     ├── (1) AIConfiguration
                     ├── (1) ProjectSettings
                     └── (0..*) ProjectVersion

StoryCharacter (*..*) ──── CharacterRelationship ──── (*..*) StoryCharacter
StoryEvent (*..*) ──────── EventParticipation ────── (*..*) StoryCharacter
StoryChapter (*..*) ────── ChapterElement ─────────── (*..*) StoryElement
StoryElement ←─────────── StoryCharacter, StoryScene, StoryEvent
```

### 1.2 关系类型定义
```python
class RelationType(Enum):
    """关系类型枚举"""
    # 角色关系
    FAMILY = "family"           # 家庭关系
    FRIEND = "friend"           # 朋友关系
    ENEMY = "enemy"             # 敌对关系
    ROMANTIC = "romantic"       # 恋爱关系
    MENTOR = "mentor"           # 师徒关系
    COLLEAGUE = "colleague"     # 同事关系
    
    # 元素关系
    CONTAINS = "contains"       # 包含关系
    TRIGGERS = "triggers"       # 触发关系
    DEPENDS_ON = "depends_on"   # 依赖关系
    CONFLICTS_WITH = "conflicts_with"  # 冲突关系
```

---

## 2. 核心实体定义

### 2.1 WritingProject (小说项目)

#### 2.1.1 基础字段
```python
@dataclass
class WritingProject:
    """小说项目实体"""
    
    # 基础信息
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    author: str = ""
    project_type: ProjectType = ProjectType.FANTASY
    description: str = ""
    cover_image: Optional[str] = None
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_opened_at: Optional[datetime] = None
    
    # 版本信息
    version: str = "1.0.0"
    schema_version: str = "2.0"
    
    # 项目状态
    status: ProjectStatus = ProjectStatus.DRAFT
    word_count: int = 0
    chapter_count: int = 0
    completion_percentage: float = 0.0
    
    # 扩展字段
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    
    # 关联对象
    settings: Optional['ProjectSettings'] = None
    ai_config: Optional['AIConfiguration'] = None
```

#### 2.1.2 验证规则
```python
class ProjectValidator:
    """项目验证器"""
    
    @staticmethod
    def validate_name(name: str) -> ValidationResult:
        """验证项目名称"""
        if not name or not name.strip():
            return ValidationResult(False, "项目名称不能为空")
        
        if len(name) > 100:
            return ValidationResult(False, "项目名称不能超过100个字符")
        
        # 检查非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        if re.search(illegal_chars, name):
            return ValidationResult(False, "项目名称包含非法字符")
        
        return ValidationResult(True)
    
    @staticmethod
    def validate_project_type(project_type: str) -> ValidationResult:
        """验证项目类型"""
        valid_types = [t.value for t in ProjectType]
        if project_type not in valid_types:
            return ValidationResult(False, f"无效的项目类型: {project_type}")
        
        return ValidationResult(True)
```

### 2.2 StoryElement (故事元素基类)

#### 2.2.1 基类定义
```python
@dataclass
class StoryElement:
    """故事元素基类"""
    
    # 基础信息
    element_id: UUID = field(default_factory=uuid4)
    element_type: ElementType = ElementType.UNKNOWN
    name: str = ""
    description: str = ""
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "user"  # user | ai | import
    
    # 分类和标签
    category: str = ""
    tags: List[str] = field(default_factory=list)
    importance: ImportanceLevel = ImportanceLevel.NORMAL
    
    # 关联信息
    related_elements: List[UUID] = field(default_factory=list)
    appearance_count: int = 0
    first_appearance: Optional[UUID] = None  # 首次出现的章节ID
    
    # 扩展字段
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    
    # 版本控制
    version: int = 1
    version_history: List['ElementVersion'] = field(default_factory=list)
```

#### 2.2.2 元素关系
```python
@dataclass
class ElementRelationship:
    """元素关系"""
    
    relationship_id: UUID = field(default_factory=uuid4)
    from_element: UUID = None
    to_element: UUID = None
    relationship_type: RelationType = RelationType.CONTAINS
    strength: float = 1.0  # 关系强度 0.0-1.0
    description: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    
    # 上下文信息
    context: Dict[str, Any] = field(default_factory=dict)
```

### 2.3 StoryCharacter (故事角色)

#### 2.3.1 角色实体
```python
@dataclass
class StoryCharacter(StoryElement):
    """故事角色实体"""
    
    element_type: ElementType = ElementType.CHARACTER
    
    # 基本信息
    full_name: str = ""
    nickname: str = ""
    gender: Gender = Gender.UNKNOWN
    age: Optional[int] = None
    birth_date: Optional[date] = None
    
    # 外貌特征
    appearance: CharacterAppearance = field(default_factory=CharacterAppearance)
    
    # 性格特质
    personality: CharacterPersonality = field(default_factory=CharacterPersonality)
    
    # 背景信息
    background: CharacterBackground = field(default_factory=CharacterBackground)
    
    # 能力属性
    abilities: List[CharacterAbility] = field(default_factory=list)
    skills: List[CharacterSkill] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    
    # 发展轨迹
    character_arc: CharacterArc = field(default_factory=CharacterArc)
    
    # 关系网络
    relationships: List[CharacterRelationship] = field(default_factory=list)
    
    # 出场统计
    chapter_appearances: List[ChapterAppearance] = field(default_factory=list)
    dialogue_count: int = 0
    action_count: int = 0

@dataclass
class CharacterAppearance:
    """角色外貌"""
    height: Optional[str] = None
    weight: Optional[str] = None
    hair_color: Optional[str] = None
    eye_color: Optional[str] = None
    skin_tone: Optional[str] = None
    distinctive_features: List[str] = field(default_factory=list)
    clothing_style: Optional[str] = None
    
@dataclass
class CharacterPersonality:
    """角色性格"""
    core_traits: List[str] = field(default_factory=list)
    motivations: List[str] = field(default_factory=list)
    fears: List[str] = field(default_factory=list)
    values: List[str] = field(default_factory=list)
    flaws: List[str] = field(default_factory=list)
    speech_patterns: List[str] = field(default_factory=list)
    
@dataclass
class CharacterRelationship:
    """角色关系"""
    target_character: UUID = None
    relationship_type: RelationType = RelationType.FRIEND
    strength: float = 1.0
    description: str = ""
    history: str = ""
    current_status: RelationshipStatus = RelationshipStatus.ACTIVE
    development_notes: str = ""
```

### 2.4 StoryScene (故事场景)

#### 2.4.1 场景实体
```python
@dataclass
class StoryScene(StoryElement):
    """故事场景实体"""
    
    element_type: ElementType = ElementType.SCENE
    
    # 地理信息
    location: SceneLocation = field(default_factory=SceneLocation)
    
    # 时间信息
    time_setting: SceneTime = field(default_factory=SceneTime)
    
    # 环境描述
    environment: SceneEnvironment = field(default_factory=SceneEnvironment)
    
    # 氛围设定
    atmosphere: SceneAtmosphere = field(default_factory=SceneAtmosphere)
    
    # 功能属性
    scene_function: SceneFunction = SceneFunction.SETTING
    recurring: bool = False
    accessibility: List[str] = field(default_factory=list)
    
    # 关联元素
    typical_characters: List[UUID] = field(default_factory=list)
    typical_events: List[UUID] = field(default_factory=list)
    
    # 使用统计
    usage_count: int = 0
    chapters_used: List[UUID] = field(default_factory=list)

@dataclass
class SceneLocation:
    """场景位置"""
    name: str = ""
    type: LocationType = LocationType.INDOOR
    address: Optional[str] = None
    coordinates: Optional[Tuple[float, float]] = None
    parent_location: Optional[UUID] = None
    sub_locations: List[UUID] = field(default_factory=list)
    
@dataclass
class SceneEnvironment:
    """场景环境"""
    weather: Optional[str] = None
    temperature: Optional[str] = None
    lighting: Optional[str] = None
    sounds: List[str] = field(default_factory=list)
    smells: List[str] = field(default_factory=list)
    textures: List[str] = field(default_factory=list)
    colors: List[str] = field(default_factory=list)
```

### 2.5 StoryEvent (故事事件)

#### 2.5.1 事件实体
```python
@dataclass
class StoryEvent(StoryElement):
    """故事事件实体"""
    
    element_type: ElementType = ElementType.EVENT
    
    # 事件分类
    event_type: EventType = EventType.SCENE
    event_category: EventCategory = EventCategory.NORMAL
    
    # 时间信息
    timeline_position: float = 0.0  # 在故事时间线中的位置
    duration: Optional[timedelta] = None
    sequence_order: int = 0
    
    # 事件内容
    summary: str = ""
    detailed_description: str = ""
    outcome: str = ""
    
    # 参与者
    participants: List[EventParticipation] = field(default_factory=list)
    
    # 因果关系
    triggers: List[UUID] = field(default_factory=list)  # 触发此事件的事件
    consequences: List[UUID] = field(default_factory=list)  # 此事件导致的事件
    
    # 影响分析
    impact_scope: EventImpact = field(default_factory=EventImpact)
    
    # 场景关联
    location: Optional[UUID] = None  # 发生场景
    
    # 情节功能
    plot_function: PlotFunction = PlotFunction.DEVELOPMENT
    tension_level: float = 0.5  # 紧张度 0.0-1.0
    emotional_impact: float = 0.5  # 情感影响 0.0-1.0

@dataclass
class EventParticipation:
    """事件参与"""
    character_id: UUID = None
    role: ParticipationRole = ParticipationRole.OBSERVER
    involvement_level: float = 1.0  # 参与度 0.0-1.0
    character_state_before: Dict[str, Any] = field(default_factory=dict)
    character_state_after: Dict[str, Any] = field(default_factory=dict)
    
@dataclass
class EventImpact:
    """事件影响"""
    plot_impact: float = 0.0  # 对情节的影响
    character_impact: Dict[UUID, float] = field(default_factory=dict)  # 对角色的影响
    world_impact: float = 0.0  # 对世界观的影响
    theme_impact: float = 0.0  # 对主题的影响
```

### 2.6 StoryChapter (故事章节)

#### 2.6.1 章节实体
```python
@dataclass
class StoryChapter:
    """故事章节实体"""
    
    # 基础信息
    chapter_id: UUID = field(default_factory=uuid4)
    title: str = ""
    subtitle: Optional[str] = None
    order: int = 0
    arc_id: Optional[UUID] = None
    
    # 内容信息
    content: str = ""
    summary: str = ""
    word_count: int = 0
    paragraph_count: int = 0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    last_edited_by: str = "user"
    
    # 创作信息
    ai_assisted: bool = False
    ai_contribution_percentage: float = 0.0
    writing_session_count: int = 0
    
    # 格式和样式
    format_styles: Dict[str, Any] = field(default_factory=dict)
    
    # 章节状态
    status: ChapterStatus = ChapterStatus.DRAFT
    completion_percentage: float = 0.0
    review_status: ReviewStatus = ReviewStatus.PENDING
    
    # 关联元素
    chapter_elements: List[ChapterElement] = field(default_factory=list)
    
    # 版本控制
    version: int = 1
    version_history: List['ChapterVersion'] = field(default_factory=list)
    
    # 统计信息
    reading_time_minutes: float = 0.0
    complexity_score: float = 0.0
    readability_score: float = 0.0

@dataclass
class ChapterElement:
    """章节元素关联"""
    element_id: UUID = None
    element_type: ElementType = ElementType.CHARACTER
    role_in_chapter: ElementRole = ElementRole.MAIN
    first_mention_position: int = 0
    mention_count: int = 0
    importance_in_chapter: float = 1.0
```

---

## 3. 数据存储设计

### 3.1 文件存储结构

#### 3.1.1 优化的目录结构
```
project_name/
├── .bamboo/                    # 系统目录
│   ├── metadata/
│   │   ├── project.json        # 项目元数据
│   │   ├── schema.json         # 数据模式版本
│   │   ├── index.json          # 全局索引
│   │   └── integrity.json      # 数据完整性校验
│   ├── cache/
│   │   ├── search/             # 搜索索引缓存
│   │   ├── relations/          # 关系图缓存
│   │   └── stats/              # 统计数据缓存
│   ├── versions/               # 版本历史
│   │   ├── snapshots/          # 快照备份
│   │   └── deltas/             # 增量变更
│   └── logs/                   # 操作日志
├── content/
│   ├── outline/
│   │   └── outline.json
│   ├── arcs/
│   │   ├── metadata/           # 分卷元数据
│   │   └── content/            # 分卷内容
│   ├── chapters/
│   │   ├── metadata/           # 章节元数据
│   │   ├── content/            # 章节正文（分片存储）
│   │   │   ├── ch001_p001.txt  # 章节1第1段
│   │   │   ├── ch001_p002.txt  # 章节1第2段
│   │   │   └── ...
│   │   └── versions/           # 章节版本历史
│   └── elements/
│       ├── characters/
│       │   ├── metadata/       # 角色元数据
│       │   └── relations/      # 角色关系数据
│       ├── scenes/
│       └── events/
├── assets/
│   ├── images/
│   ├── templates/
│   └── references/
└── exports/
```

#### 3.1.2 数据分片策略
```python
class ContentSharder:
    """内容分片器"""
    
    def __init__(self, max_shard_size: int = 64 * 1024):  # 64KB
        self.max_shard_size = max_shard_size
        
    def shard_content(self, content: str, chapter_id: UUID) -> List[ContentShard]:
        """分片内容"""
        paragraphs = content.split('\n\n')
        shards = []
        current_shard = ContentShard(chapter_id=chapter_id, shard_index=0)
        
        for i, paragraph in enumerate(paragraphs):
            if current_shard.size + len(paragraph) > self.max_shard_size:
                # 保存当前分片，创建新分片
                shards.append(current_shard)
                current_shard = ContentShard(
                    chapter_id=chapter_id, 
                    shard_index=len(shards)
                )
            
            current_shard.add_paragraph(i, paragraph)
        
        if current_shard.paragraphs:
            shards.append(current_shard)
            
        return shards

@dataclass
class ContentShard:
    """内容分片"""
    chapter_id: UUID = None
    shard_index: int = 0
    paragraphs: Dict[int, str] = field(default_factory=dict)
    size: int = 0
    checksum: Optional[str] = None
    
    def add_paragraph(self, index: int, content: str):
        """添加段落"""
        self.paragraphs[index] = content
        self.size += len(content)
        self.checksum = self.calculate_checksum()
```

### 3.2 数据一致性机制

#### 3.2.1 校验和系统
```python
class IntegrityChecker:
    """数据完整性检查器"""
    
    def __init__(self):
        self.hash_algorithm = hashlib.sha256
        
    def calculate_file_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        hasher = self.hash_algorithm()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
        
    def verify_project_integrity(self, project_path: str) -> IntegrityReport:
        """验证项目完整性"""
        report = IntegrityReport()
        
        # 检查必需文件
        required_files = [
            '.bamboo/metadata/project.json',
            '.bamboo/metadata/index.json'
        ]
        
        for file_path in required_files:
            full_path = os.path.join(project_path, file_path)
            if not os.path.exists(full_path):
                report.add_error(f"缺少必需文件: {file_path}")
            else:
                # 验证校验和
                stored_checksum = self.get_stored_checksum(full_path)
                actual_checksum = self.calculate_file_checksum(full_path)
                if stored_checksum != actual_checksum:
                    report.add_error(f"文件校验和不匹配: {file_path}")
        
        return report

@dataclass
class IntegrityReport:
    """完整性报告"""
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    checked_files: int = 0
    
    def add_error(self, message: str):
        self.errors.append(message)
        
    def add_warning(self, message: str):
        self.warnings.append(message)
        
    @property
    def is_valid(self) -> bool:
        return len(self.errors) == 0
```

#### 3.2.2 事务支持
```python
class TransactionManager:
    """事务管理器"""
    
    def __init__(self, project_path: str):
        self.project_path = project_path
        self.transaction_log = []
        self.rollback_data = {}
        
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        transaction_id = uuid4()
        self.begin_transaction(transaction_id)
        
        try:
            yield transaction_id
            self.commit_transaction(transaction_id)
        except Exception as e:
            self.rollback_transaction(transaction_id)
            raise e
            
    def begin_transaction(self, transaction_id: UUID):
        """开始事务"""
        self.transaction_log.append({
            'id': transaction_id,
            'started_at': datetime.now(),
            'operations': []
        })
        
    def record_operation(self, transaction_id: UUID, operation: Operation):
        """记录操作"""
        transaction = self.get_transaction(transaction_id)
        transaction['operations'].append(operation)
        
        # 保存回滚数据
        if operation.type in ['UPDATE', 'DELETE']:
            self.rollback_data[operation.target] = operation.old_value
            
    def commit_transaction(self, transaction_id: UUID):
        """提交事务"""
        transaction = self.get_transaction(transaction_id)
        transaction['committed_at'] = datetime.now()
        transaction['status'] = 'COMMITTED'
        
        # 清理回滚数据
        for op in transaction['operations']:
            if op.target in self.rollback_data:
                del self.rollback_data[op.target]
```

---

## 4. 版本控制系统

### 4.1 版本管理

#### 4.1.1 版本实体
```python
@dataclass
class ProjectVersion:
    """项目版本"""
    version_id: UUID = field(default_factory=uuid4)
    version_number: str = ""
    parent_version: Optional[UUID] = None
    branch_name: str = "main"
    
    # 版本信息
    created_at: datetime = field(default_factory=datetime.now)
    created_by: str = "user"
    commit_message: str = ""
    tags: List[str] = field(default_factory=list)
    
    # 变更信息
    changes: List[VersionChange] = field(default_factory=list)
    files_changed: int = 0
    lines_added: int = 0
    lines_deleted: int = 0
    
    # 快照信息
    snapshot_path: Optional[str] = None
    delta_path: Optional[str] = None
    compressed: bool = False

@dataclass
class VersionChange:
    """版本变更"""
    change_id: UUID = field(default_factory=uuid4)
    change_type: ChangeType = ChangeType.MODIFY
    target_type: str = ""  # chapter, character, scene, event
    target_id: UUID = None
    field_name: Optional[str] = None
    old_value: Any = None
    new_value: Any = None
    change_description: str = ""
```

#### 4.1.2 版本控制器
```python
class VersionController:
    """版本控制器"""
    
    def __init__(self, project_path: str):
        self.project_path = project_path
        self.versions_path = os.path.join(project_path, '.bamboo/versions')
        
    def create_version(self, commit_message: str, branch: str = "main") -> ProjectVersion:
        """创建新版本"""
        # 检测变更
        changes = self.detect_changes()
        
        # 创建版本对象
        version = ProjectVersion(
            version_number=self.generate_version_number(),
            branch_name=branch,
            commit_message=commit_message,
            changes=changes,
            files_changed=len(set(c.target_id for c in changes))
        )
        
        # 创建快照或增量
        if self.should_create_snapshot(version):
            version.snapshot_path = self.create_snapshot(version)
        else:
            version.delta_path = self.create_delta(version)
            
        # 保存版本信息
        self.save_version(version)
        
        return version
        
    def restore_version(self, version_id: UUID) -> bool:
        """恢复到指定版本"""
        version = self.load_version(version_id)
        if not version:
            return False
            
        if version.snapshot_path:
            return self.restore_from_snapshot(version)
        else:
            return self.restore_from_deltas(version)
```

### 4.2 分支管理

#### 4.2.1 分支系统
```python
class BranchManager:
    """分支管理器"""

    def __init__(self, version_controller: VersionController):
        self.version_controller = version_controller
        self.branches: Dict[str, Branch] = {}

    def create_branch(self, branch_name: str, from_version: UUID) -> Branch:
        """创建分支"""
        if branch_name in self.branches:
            raise ValueError(f"分支 {branch_name} 已存在")

        branch = Branch(
            name=branch_name,
            created_from=from_version,
            created_at=datetime.now()
        )

        self.branches[branch_name] = branch
        return branch

    def merge_branch(self, source_branch: str, target_branch: str) -> MergeResult:
        """合并分支"""
        source = self.branches[source_branch]
        target = self.branches[target_branch]

        # 检测冲突
        conflicts = self.detect_conflicts(source, target)

        if conflicts:
            return MergeResult(success=False, conflicts=conflicts)

        # 执行合并
        merge_version = self.perform_merge(source, target)

        return MergeResult(success=True, merge_version=merge_version)

@dataclass
class Branch:
    """分支"""
    name: str = ""
    created_from: UUID = None
    created_at: datetime = field(default_factory=datetime.now)
    head_version: Optional[UUID] = None
    description: str = ""
    active: bool = True
```

---

## 5. 索引和搜索

### 5.1 搜索索引设计

#### 5.1.1 全文索引
```python
class FullTextIndex:
    """全文搜索索引"""

    def __init__(self):
        self.inverted_index: Dict[str, Set[UUID]] = {}
        self.document_store: Dict[UUID, Document] = {}
        self.tokenizer = ChineseTokenizer()

    def add_document(self, doc_id: UUID, content: str, metadata: Dict[str, Any]):
        """添加文档到索引"""
        # 分词
        tokens = self.tokenizer.tokenize(content)

        # 创建文档对象
        doc = Document(
            id=doc_id,
            content=content,
            metadata=metadata,
            tokens=tokens,
            token_count=len(tokens)
        )

        self.document_store[doc_id] = doc

        # 更新倒排索引
        for token in tokens:
            if token not in self.inverted_index:
                self.inverted_index[token] = set()
            self.inverted_index[token].add(doc_id)

    def search(self, query: str, filters: Optional[Dict[str, Any]] = None) -> SearchResult:
        """搜索文档"""
        query_tokens = self.tokenizer.tokenize(query)

        # 获取候选文档
        candidate_docs = self.get_candidate_documents(query_tokens)

        # 应用过滤器
        if filters:
            candidate_docs = self.apply_filters(candidate_docs, filters)

        # 计算相关性得分
        scored_docs = self.calculate_relevance_scores(candidate_docs, query_tokens)

        # 排序并返回结果
        sorted_docs = sorted(scored_docs, key=lambda x: x.score, reverse=True)

        return SearchResult(
            query=query,
            total_results=len(sorted_docs),
            documents=sorted_docs[:50]  # 限制返回结果数量
        )

@dataclass
class Document:
    """文档"""
    id: UUID = None
    content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    tokens: List[str] = field(default_factory=list)
    token_count: int = 0

@dataclass
class SearchResult:
    """搜索结果"""
    query: str = ""
    total_results: int = 0
    documents: List['ScoredDocument'] = field(default_factory=list)
    execution_time: float = 0.0
```

#### 5.1.2 关系索引
```python
class RelationshipIndex:
    """关系索引"""

    def __init__(self):
        self.adjacency_list: Dict[UUID, Set[UUID]] = {}
        self.relationship_data: Dict[Tuple[UUID, UUID], ElementRelationship] = {}

    def add_relationship(self, relationship: ElementRelationship):
        """添加关系"""
        from_id = relationship.from_element
        to_id = relationship.to_element

        # 更新邻接表
        if from_id not in self.adjacency_list:
            self.adjacency_list[from_id] = set()
        self.adjacency_list[from_id].add(to_id)

        # 存储关系数据
        self.relationship_data[(from_id, to_id)] = relationship

    def find_related_elements(self, element_id: UUID, max_depth: int = 2) -> List[UUID]:
        """查找相关元素"""
        visited = set()
        queue = [(element_id, 0)]
        related = []

        while queue:
            current_id, depth = queue.pop(0)

            if current_id in visited or depth > max_depth:
                continue

            visited.add(current_id)

            if depth > 0:  # 不包括起始元素
                related.append(current_id)

            # 添加相邻元素到队列
            if current_id in self.adjacency_list:
                for neighbor in self.adjacency_list[current_id]:
                    if neighbor not in visited:
                        queue.append((neighbor, depth + 1))

        return related

    def find_shortest_path(self, from_element: UUID, to_element: UUID) -> Optional[List[UUID]]:
        """查找最短路径"""
        if from_element == to_element:
            return [from_element]

        visited = set()
        queue = [(from_element, [from_element])]

        while queue:
            current_id, path = queue.pop(0)

            if current_id in visited:
                continue

            visited.add(current_id)

            if current_id == to_element:
                return path

            if current_id in self.adjacency_list:
                for neighbor in self.adjacency_list[current_id]:
                    if neighbor not in visited:
                        queue.append((neighbor, path + [neighbor]))

        return None
```

---

## 6. 数据迁移和兼容性

### 6.1 数据迁移

#### 6.1.1 迁移管理器
```python
class MigrationManager:
    """数据迁移管理器"""

    def __init__(self):
        self.migrations: List[Migration] = []
        self.current_version = "2.0"

    def register_migration(self, migration: Migration):
        """注册迁移"""
        self.migrations.append(migration)
        self.migrations.sort(key=lambda m: m.version)

    def migrate_project(self, project_path: str) -> MigrationResult:
        """迁移项目"""
        # 检查当前版本
        current_schema_version = self.get_schema_version(project_path)

        if current_schema_version == self.current_version:
            return MigrationResult(success=True, message="项目已是最新版本")

        # 查找需要执行的迁移
        required_migrations = self.get_required_migrations(current_schema_version)

        # 创建备份
        backup_path = self.create_backup(project_path)

        try:
            # 执行迁移
            for migration in required_migrations:
                migration.execute(project_path)

            # 更新版本信息
            self.update_schema_version(project_path, self.current_version)

            return MigrationResult(success=True, message="迁移成功完成")

        except Exception as e:
            # 恢复备份
            self.restore_backup(project_path, backup_path)
            return MigrationResult(success=False, message=f"迁移失败: {str(e)}")

@dataclass
class Migration:
    """数据迁移"""
    version: str = ""
    description: str = ""

    def execute(self, project_path: str):
        """执行迁移"""
        raise NotImplementedError

    def rollback(self, project_path: str):
        """回滚迁移"""
        raise NotImplementedError
```

#### 6.1.2 具体迁移示例
```python
class Migration_1_0_to_2_0(Migration):
    """从1.0迁移到2.0"""

    def __init__(self):
        super().__init__(version="2.0", description="添加关系系统和版本控制")

    def execute(self, project_path: str):
        """执行迁移"""
        # 1. 创建新的目录结构
        self.create_new_directories(project_path)

        # 2. 迁移项目元数据
        self.migrate_project_metadata(project_path)

        # 3. 迁移角色数据，添加关系字段
        self.migrate_character_data(project_path)

        # 4. 创建索引文件
        self.create_index_files(project_path)

        # 5. 初始化版本控制
        self.initialize_version_control(project_path)

    def migrate_character_data(self, project_path: str):
        """迁移角色数据"""
        characters_dir = os.path.join(project_path, "content/elements/characters")

        for filename in os.listdir(characters_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(characters_dir, filename)

                with open(file_path, 'r', encoding='utf-8') as f:
                    old_data = json.load(f)

                # 转换为新格式
                new_data = self.convert_character_format(old_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(new_data, f, ensure_ascii=False, indent=2)
```

---

## 7. 性能优化

### 7.1 数据访问优化

#### 7.1.1 延迟加载
```python
class LazyLoadProxy:
    """延迟加载代理"""

    def __init__(self, loader_func: Callable, *args, **kwargs):
        self._loader_func = loader_func
        self._args = args
        self._kwargs = kwargs
        self._loaded = False
        self._data = None

    def __getattr__(self, name):
        if not self._loaded:
            self._data = self._loader_func(*self._args, **self._kwargs)
            self._loaded = True
        return getattr(self._data, name)

    def __call__(self):
        if not self._loaded:
            self._data = self._loader_func(*self._args, **self._kwargs)
            self._loaded = True
        return self._data
```

#### 7.1.2 批量操作
```python
class BatchProcessor:
    """批量处理器"""

    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.pending_operations: List[Operation] = []

    def add_operation(self, operation: Operation):
        """添加操作"""
        self.pending_operations.append(operation)

        if len(self.pending_operations) >= self.batch_size:
            self.flush()

    def flush(self):
        """执行批量操作"""
        if not self.pending_operations:
            return

        # 按类型分组操作
        grouped_ops = self.group_operations_by_type(self.pending_operations)

        # 批量执行
        for op_type, operations in grouped_ops.items():
            self.execute_batch(op_type, operations)

        self.pending_operations.clear()
```

---

## 8. 总结

### 8.1 设计优势
1. **灵活的关系模型**: 支持复杂的多对多关系和动态关系网络
2. **强数据一致性**: 校验和、事务支持、冲突检测保障数据完整性
3. **高性能访问**: 多级索引、分片存储、延迟加载优化性能
4. **版本控制**: 内置版本管理支持分支、合并、回滚
5. **可扩展性**: 自定义字段、标签系统、插件接口支持扩展

### 8.2 实施建议
1. **分阶段实施**: 先实现核心实体，再添加关系和版本控制
2. **性能测试**: 在大数据量下测试索引和搜索性能
3. **数据迁移**: 提供完整的数据迁移工具和向后兼容性
4. **文档维护**: 保持数据模型文档与代码同步

---

*本文档版本: v2.0*
*最后更新: 2025-09-12*
