#!/usr/bin/env python3
"""笔落App主程序入口

负责应用程序的启动、初始化和主事件循环。
"""

import sys
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from PyQt6.QtGui import QIcon

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# from config.settings import AppSettings
# from utils.logging_config import setup_logging
from ui.main_window import MainWindow, show_splash_screen


def setup_application() -> QApplication:
    """设置应用程序基本配置"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("笔落App")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("笔落开发团队")
    app.setOrganizationDomain("bamboofall.com")
    
    # 设置应用程序图标
    icon_path = Path(__file__).parent.parent / "resources" / "icons" / "app.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app


def setup_internationalization(app: QApplication):
    """设置国际化支持"""
    # TODO: 实现国际化支持
    # settings = AppSettings()
    # locale = settings.get_language()

    translator = QTranslator()
    locale_path = Path(__file__).parent.parent / "resources" / "locales"

    # if translator.load(QLocale(locale), "bamboofall", "_", str(locale_path)):
    #     app.installTranslator(translator)
    pass


def main():
    """主函数"""
    try:
        # 设置日志
        # setup_logging()
        # logger = logging.getLogger(__name__)
        # logger.info("启动笔落App...")
        print("启动笔落App...")

        # 创建应用程序
        app = setup_application()

        # 设置国际化
        setup_internationalization(app)

        # 显示启动画面
        splash = show_splash_screen(app)

        # 创建主窗口
        main_window = MainWindow()

        # 延迟显示主窗口
        from PyQt6.QtCore import QTimer
        def show_main_window():
            splash.close()
            main_window.show()

        QTimer.singleShot(2000, show_main_window)

        print("应用程序启动完成")

        # 运行主事件循环
        sys.exit(app.exec())

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
