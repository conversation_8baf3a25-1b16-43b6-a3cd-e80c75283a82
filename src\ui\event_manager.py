"""
事件管理界面模块

实现事件的创建、编辑、删除和管理功能
"""

from typing import List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QGroupBox,
    QFormLayout, QLineEdit, QTextEdit, QComboBox,
    QTabWidget, QFrame, QSplitter, QMessageBox,
    QDialog, QDialogButtonBox, QSpinBox, QDateTimeEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont

from src.core.models.project import WritingProject
from src.core.models.event import Event
from src.core.models.enums import EventType, EventImportance


class EventEditDialog(QDialog):
    """事件编辑对话框"""
    
    def __init__(self, event: Optional[Event] = None, parent=None):
        super().__init__(parent)
        self.event = event
        self.is_editing = event is not None
        
        self.setup_ui()
        if self.is_editing:
            self.load_event_data()
            
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("编辑事件" if self.is_editing else "新建事件")
        self.setModal(True)
        self.setMinimumSize(600, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")
        
        # 详细描述标签页
        detail_tab = self.create_detail_tab()
        tab_widget.addTab(detail_tab, "详细描述")
        
        # 影响分析标签页
        impact_tab = self.create_impact_tab()
        tab_widget.addTab(impact_tab, "影响分析")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 事件名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入事件名称")
        layout.addRow("事件名称 *:", self.name_edit)
        
        # 事件类型
        self.type_combo = QComboBox()
        for event_type in EventType:
            self.type_combo.addItem(event_type.value, event_type)
        layout.addRow("事件类型:", self.type_combo)
        
        # 重要程度
        self.importance_combo = QComboBox()
        for importance in EventImportance:
            self.importance_combo.addItem(importance.value, importance)
        layout.addRow("重要程度:", self.importance_combo)
        
        # 时间设定
        time_group = QGroupBox("时间设定")
        time_layout = QFormLayout(time_group)
        
        # 开始时间
        self.start_time_edit = QDateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime())
        self.start_time_edit.setCalendarPopup(True)
        time_layout.addRow("开始时间:", self.start_time_edit)
        
        # 结束时间
        self.end_time_edit = QDateTimeEdit()
        self.end_time_edit.setDateTime(QDateTime.currentDateTime())
        self.end_time_edit.setCalendarPopup(True)
        time_layout.addRow("结束时间:", self.end_time_edit)
        
        # 持续时间（分钟）
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(0, 999999)
        self.duration_spin.setSuffix(" 分钟")
        time_layout.addRow("持续时间:", self.duration_spin)
        
        layout.addRow(time_group)
        
        # 事件简介
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("事件的简要描述")
        layout.addRow("事件简介:", self.description_edit)
        
        return widget
        
    def create_detail_tab(self) -> QWidget:
        """创建详细描述标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 触发条件
        self.trigger_edit = QTextEdit()
        self.trigger_edit.setMaximumHeight(80)
        self.trigger_edit.setPlaceholderText("事件的触发条件或原因")
        layout.addRow("触发条件:", self.trigger_edit)
        
        # 参与角色
        self.participants_edit = QTextEdit()
        self.participants_edit.setMaximumHeight(80)
        self.participants_edit.setPlaceholderText("参与此事件的角色")
        layout.addRow("参与角色:", self.participants_edit)
        
        # 发生地点
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("事件发生的地点")
        layout.addRow("发生地点:", self.location_edit)
        
        # 详细过程
        self.process_edit = QTextEdit()
        self.process_edit.setMinimumHeight(120)
        self.process_edit.setPlaceholderText("事件的详细发生过程")
        layout.addRow("详细过程:", self.process_edit)
        
        # 结果
        self.outcome_edit = QTextEdit()
        self.outcome_edit.setMaximumHeight(80)
        self.outcome_edit.setPlaceholderText("事件的结果或后果")
        layout.addRow("事件结果:", self.outcome_edit)
        
        # 相关物品
        self.related_items_edit = QTextEdit()
        self.related_items_edit.setMaximumHeight(60)
        self.related_items_edit.setPlaceholderText("与事件相关的物品或道具")
        layout.addRow("相关物品:", self.related_items_edit)
        
        return widget
        
    def create_impact_tab(self) -> QWidget:
        """创建影响分析标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 对主角的影响
        self.protagonist_impact_edit = QTextEdit()
        self.protagonist_impact_edit.setMaximumHeight(80)
        self.protagonist_impact_edit.setPlaceholderText("事件对主角的影响")
        layout.addRow("对主角影响:", self.protagonist_impact_edit)
        
        # 对其他角色的影响
        self.character_impact_edit = QTextEdit()
        self.character_impact_edit.setMaximumHeight(80)
        self.character_impact_edit.setPlaceholderText("事件对其他角色的影响")
        layout.addRow("对角色影响:", self.character_impact_edit)
        
        # 对情节的影响
        self.plot_impact_edit = QTextEdit()
        self.plot_impact_edit.setMaximumHeight(80)
        self.plot_impact_edit.setPlaceholderText("事件对整体情节的影响")
        layout.addRow("对情节影响:", self.plot_impact_edit)
        
        # 对世界观的影响
        self.world_impact_edit = QTextEdit()
        self.world_impact_edit.setMaximumHeight(80)
        self.world_impact_edit.setPlaceholderText("事件对世界观设定的影响")
        layout.addRow("对世界观影响:", self.world_impact_edit)
        
        # 伏笔和暗示
        self.foreshadowing_edit = QTextEdit()
        self.foreshadowing_edit.setMaximumHeight(80)
        self.foreshadowing_edit.setPlaceholderText("事件中的伏笔和暗示")
        layout.addRow("伏笔暗示:", self.foreshadowing_edit)
        
        # 后续发展
        self.follow_up_edit = QTextEdit()
        self.follow_up_edit.setMaximumHeight(80)
        self.follow_up_edit.setPlaceholderText("事件可能引发的后续发展")
        layout.addRow("后续发展:", self.follow_up_edit)
        
        # 象征意义
        self.symbolism_edit = QTextEdit()
        self.symbolism_edit.setMaximumHeight(80)
        self.symbolism_edit.setPlaceholderText("事件的象征意义或主题表达")
        layout.addRow("象征意义:", self.symbolism_edit)
        
        return widget
        
    def load_event_data(self):
        """加载事件数据"""
        if not self.event:
            return
            
        # 基本信息
        self.name_edit.setText(self.event.name)
        self.description_edit.setPlainText(self.event.description or "")
        
        # 设置事件类型
        for i in range(self.type_combo.count()):
            if self.type_combo.itemData(i) == self.event.event_type:
                self.type_combo.setCurrentIndex(i)
                break
                
        # 设置重要程度
        for i in range(self.importance_combo.count()):
            if self.importance_combo.itemData(i) == self.event.importance:
                self.importance_combo.setCurrentIndex(i)
                break
                
        # 时间设定
        if self.event.timeline:
            timeline = self.event.timeline
            if timeline.start_time:
                self.start_time_edit.setDateTime(QDateTime.fromString(timeline.start_time.isoformat(), Qt.DateFormat.ISODate))
            if timeline.end_time:
                self.end_time_edit.setDateTime(QDateTime.fromString(timeline.end_time.isoformat(), Qt.DateFormat.ISODate))
            if timeline.duration_minutes:
                self.duration_spin.setValue(timeline.duration_minutes)
                
        # 详细信息
        if self.event.details:
            details = self.event.details
            self.trigger_edit.setPlainText(details.trigger_conditions or "")
            self.participants_edit.setPlainText(details.participants or "")
            self.location_edit.setText(details.location or "")
            self.process_edit.setPlainText(details.detailed_process or "")
            self.outcome_edit.setPlainText(details.outcome or "")
            self.related_items_edit.setPlainText(details.related_items or "")
            
        # 影响分析
        if self.event.impact:
            impact = self.event.impact
            self.protagonist_impact_edit.setPlainText(impact.on_protagonist or "")
            self.character_impact_edit.setPlainText(impact.on_other_characters or "")
            self.plot_impact_edit.setPlainText(impact.on_plot or "")
            self.world_impact_edit.setPlainText(impact.on_world or "")
            self.foreshadowing_edit.setPlainText(impact.foreshadowing or "")
            self.follow_up_edit.setPlainText(impact.follow_up_events or "")
            self.symbolism_edit.setPlainText(impact.symbolism or "")
            
    def get_event_data(self) -> dict:
        """获取事件数据"""
        return {
            'name': self.name_edit.text().strip(),
            'event_type': self.type_combo.currentData(),
            'importance': self.importance_combo.currentData(),
            'description': self.description_edit.toPlainText().strip() or None,
            'timeline': {
                'start_time': self.start_time_edit.dateTime().toPython(),
                'end_time': self.end_time_edit.dateTime().toPython(),
                'duration_minutes': self.duration_spin.value() if self.duration_spin.value() > 0 else None,
            },
            'details': {
                'trigger_conditions': self.trigger_edit.toPlainText().strip() or None,
                'participants': self.participants_edit.toPlainText().strip() or None,
                'location': self.location_edit.text().strip() or None,
                'detailed_process': self.process_edit.toPlainText().strip() or None,
                'outcome': self.outcome_edit.toPlainText().strip() or None,
                'related_items': self.related_items_edit.toPlainText().strip() or None,
            },
            'impact': {
                'on_protagonist': self.protagonist_impact_edit.toPlainText().strip() or None,
                'on_other_characters': self.character_impact_edit.toPlainText().strip() or None,
                'on_plot': self.plot_impact_edit.toPlainText().strip() or None,
                'on_world': self.world_impact_edit.toPlainText().strip() or None,
                'foreshadowing': self.foreshadowing_edit.toPlainText().strip() or None,
                'follow_up_events': self.follow_up_edit.toPlainText().strip() or None,
                'symbolism': self.symbolism_edit.toPlainText().strip() or None,
            }
        }


class EventListWidget(QListWidget):
    """事件列表组件"""
    
    # 信号定义
    event_selected = pyqtSignal(Event)
    event_double_clicked = pyqtSignal(Event)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.events: List[Event] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setAlternatingRowColors(True)
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                alternate-background-color: #f8f9fa;
            }
            
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QListWidget::item:selected {
                background: #007bff;
                color: white;
            }
            
            QListWidget::item:hover {
                background: #e3f2fd;
            }
        """)
        
        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
        
    def add_event(self, event: Event):
        """添加事件"""
        self.events.append(event)
        
        item = QListWidgetItem()
        item.setText(f"{event.name} ({event.event_type.value})")
        item.setData(Qt.ItemDataRole.UserRole, event)
        
        self.addItem(item)
        
    def remove_event(self, event: Event):
        """移除事件"""
        if event in self.events:
            self.events.remove(event)
            
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == event:
                self.takeItem(i)
                break
                
    def update_event(self, event: Event):
        """更新事件"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole).id == event.id:
                item.setText(f"{event.name} ({event.event_type.value})")
                item.setData(Qt.ItemDataRole.UserRole, event)
                break
                
    def on_item_clicked(self, item: QListWidgetItem):
        """处理项目点击"""
        event = item.data(Qt.ItemDataRole.UserRole)
        if event:
            self.event_selected.emit(event)
            
    def on_item_double_clicked(self, item: QListWidgetItem):
        """处理项目双击"""
        event = item.data(Qt.ItemDataRole.UserRole)
        if event:
            self.event_double_clicked.emit(event)


class EventManagerWidget(QWidget):
    """事件管理主组件"""
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.current_event: Optional[Event] = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_events()
        
    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        
        # 左侧事件列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 事件列表标题和按钮
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("事件列表"))
        header_layout.addStretch()
        
        self.add_btn = QPushButton("新建")
        self.add_btn.setMaximumWidth(60)
        header_layout.addWidget(self.add_btn)
        
        left_layout.addLayout(header_layout)
        
        # 事件列表
        self.event_list = EventListWidget()
        left_layout.addWidget(self.event_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.duplicate_btn = QPushButton("复制")
        
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.duplicate_btn)
        
        left_layout.addLayout(button_layout)
        
        # 右侧事件详情
        self.event_detail = QTextEdit()
        self.event_detail.setReadOnly(True)
        self.event_detail.setPlaceholderText("选择事件查看详细信息")
        
        # 添加到主布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(self.event_detail)
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
        
    def setup_connections(self):
        """设置信号连接"""
        self.add_btn.clicked.connect(self.add_event)
        self.edit_btn.clicked.connect(self.edit_event)
        self.delete_btn.clicked.connect(self.delete_event)
        self.duplicate_btn.clicked.connect(self.duplicate_event)
        
        self.event_list.event_selected.connect(self.show_event_detail)
        self.event_list.event_double_clicked.connect(self.edit_event_from_list)
        
    def load_events(self):
        """加载事件列表"""
        # TODO: 从项目中加载事件
        pass
        
    def add_event(self):
        """添加事件"""
        dialog = EventEditDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            event_data = dialog.get_event_data()
            
            # 验证必填字段
            if not event_data['name']:
                QMessageBox.warning(self, "验证错误", "事件名称不能为空")
                return
                
            # 创建事件对象
            try:
                event = Event(
                    name=event_data['name'],
                    event_type=event_data['event_type'],
                    importance=event_data['importance'],
                    **{k: v for k, v in event_data.items() if k not in ['name', 'event_type', 'importance'] and v is not None}
                )
                
                # 添加到列表
                self.event_list.add_event(event)
                
                # TODO: 保存到项目
                
            except Exception as e:
                QMessageBox.critical(self, "创建失败", f"创建事件失败: {e}")
                
    def edit_event(self):
        """编辑当前选中的事件"""
        if self.current_event:
            self.edit_event_from_list(self.current_event)
            
    def edit_event_from_list(self, event: Event):
        """编辑指定事件"""
        dialog = EventEditDialog(event, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            event_data = dialog.get_event_data()
            
            # 更新事件数据
            try:
                # TODO: 更新事件对象
                self.event_list.update_event(event)
                self.show_event_detail(event)
                
            except Exception as e:
                QMessageBox.critical(self, "更新失败", f"更新事件失败: {e}")
                
    def delete_event(self):
        """删除事件"""
        if not self.current_event:
            return
            
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除事件 '{self.current_event.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.event_list.remove_event(self.current_event)
            self.current_event = None
            self.event_detail.clear()
            
    def duplicate_event(self):
        """复制事件"""
        if not self.current_event:
            return
            
        # TODO: 实现事件复制功能
        QMessageBox.information(self, "功能开发中", "事件复制功能正在开发中")
        
    def show_event_detail(self, event: Event):
        """显示事件详情"""
        self.current_event = event
        
        # 生成详情文本
        detail_text = f"""
<h2>{event.name}</h2>
<p><strong>事件类型:</strong> {event.event_type.value}</p>
<p><strong>重要程度:</strong> {event.importance.value}</p>
"""
        
        if event.description:
            detail_text += f"<p><strong>描述:</strong> {event.description}</p>"
            
        # TODO: 添加更多详情信息
        
        self.event_detail.setHtml(detail_text)
