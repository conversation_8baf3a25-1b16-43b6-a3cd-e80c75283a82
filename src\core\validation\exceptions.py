"""验证异常类

定义验证过程中可能出现的各种异常和错误类型
"""

from typing import List, Dict, Any, Optional
from enum import Enum


class ValidationSeverity(Enum):
    """验证严重程度"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationIssue:
    """验证问题"""
    
    def __init__(
        self,
        severity: ValidationSeverity,
        message: str,
        field_name: Optional[str] = None,
        rule_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.severity = severity
        self.message = message
        self.field_name = field_name
        self.rule_name = rule_name
        self.context = context or {}
    
    def __str__(self) -> str:
        parts = [f"[{self.severity.value.upper()}]"]
        if self.field_name:
            parts.append(f"字段 '{self.field_name}':")
        parts.append(self.message)
        if self.rule_name:
            parts.append(f"(规则: {self.rule_name})")
        return " ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "severity": self.severity.value,
            "message": self.message,
            "field_name": self.field_name,
            "rule_name": self.rule_name,
            "context": self.context
        }


class ValidationError(ValidationIssue):
    """验证错误"""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        rule_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(ValidationSeverity.ERROR, message, field_name, rule_name, context)


class ValidationWarning(ValidationIssue):
    """验证警告"""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        rule_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(ValidationSeverity.WARNING, message, field_name, rule_name, context)


class ValidationInfo(ValidationIssue):
    """验证信息"""
    
    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        rule_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(ValidationSeverity.INFO, message, field_name, rule_name, context)


class ValidationException(Exception):
    """验证异常基类"""
    
    def __init__(self, issues: List[ValidationIssue]):
        self.issues = issues
        self.errors = [issue for issue in issues if issue.severity == ValidationSeverity.ERROR]
        self.warnings = [issue for issue in issues if issue.severity == ValidationSeverity.WARNING]
        self.infos = [issue for issue in issues if issue.severity == ValidationSeverity.INFO]
        
        # 构建错误消息
        error_messages = [str(error) for error in self.errors]
        super().__init__("\n".join(error_messages) if error_messages else "验证失败")
    
    @property
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    @property
    def has_infos(self) -> bool:
        """是否有信息"""
        return len(self.infos) > 0
    
    def get_summary(self) -> Dict[str, Any]:
        """获取验证结果摘要"""
        return {
            "total_issues": len(self.issues),
            "errors": len(self.errors),
            "warnings": len(self.warnings),
            "infos": len(self.infos),
            "issues": [issue.to_dict() for issue in self.issues]
        }


class BusinessRuleViolation(ValidationException):
    """业务规则违反异常"""
    
    def __init__(self, rule_name: str, message: str, context: Optional[Dict[str, Any]] = None):
        error = ValidationError(message, rule_name=rule_name, context=context)
        super().__init__([error])
        self.rule_name = rule_name


class RelationshipValidationError(ValidationException):
    """关系验证错误"""
    
    def __init__(self, relationship_type: str, message: str, context: Optional[Dict[str, Any]] = None):
        error = ValidationError(message, rule_name=f"{relationship_type}_relationship", context=context)
        super().__init__([error])
        self.relationship_type = relationship_type


class FieldValidationError(ValidationException):
    """字段验证错误"""
    
    def __init__(self, field_name: str, message: str, rule_name: Optional[str] = None):
        error = ValidationError(message, field_name=field_name, rule_name=rule_name)
        super().__init__([error])
        self.field_name = field_name
