"""角色模型

定义角色相关的数据模型，包括：
- Character: 角色主模型
- CharacterAppearance: 角色外貌
- CharacterPersonality: 角色性格
- CharacterBackground: 角色背景
- CharacterRelationship: 角色关系
"""

from datetime import datetime, date
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import Field, validator

from .base import BaseModel, ValidationResult, ModelValidator
from .story_element import StoryElement, StoryElementValidator
from .enums import Gender, RelationType, RelationshipStatus, ImportanceLevel, ElementType


class CharacterAppearance(BaseModel):
    """角色外貌"""
    
    # 基本信息
    age: Optional[int] = Field(default=None, description="年龄")
    height: Optional[str] = Field(default=None, description="身高")
    weight: Optional[str] = Field(default=None, description="体重")
    build: str = Field(default="", description="体型")
    
    # 外貌特征
    hair_color: str = Field(default="", description="发色")
    hair_style: str = Field(default="", description="发型")
    eye_color: str = Field(default="", description="眼色")
    skin_tone: str = Field(default="", description="肤色")
    
    # 特殊特征
    distinguishing_features: List[str] = Field(default_factory=list, description="显著特征")
    scars_marks: List[str] = Field(default_factory=list, description="疤痕标记")
    
    # 服装风格
    clothing_style: str = Field(default="", description="服装风格")
    accessories: List[str] = Field(default_factory=list, description="配饰")
    
    # 其他描述
    overall_description: str = Field(default="", description="整体描述")
    
    @validator('age')
    def validate_age(cls, v):
        if v is not None and (v < 0 or v > 200):
            raise ValueError("年龄必须在0-200之间")
        return v


class CharacterPersonality(BaseModel):
    """角色性格"""
    
    # 核心特质
    core_traits: List[str] = Field(default_factory=list, description="核心性格特质")
    positive_traits: List[str] = Field(default_factory=list, description="积极特质")
    negative_traits: List[str] = Field(default_factory=list, description="消极特质")
    
    # 行为模式
    habits: List[str] = Field(default_factory=list, description="习惯")
    mannerisms: List[str] = Field(default_factory=list, description="言行举止")
    speech_patterns: List[str] = Field(default_factory=list, description="说话方式")
    
    # 情感特征
    emotional_tendencies: List[str] = Field(default_factory=list, description="情感倾向")
    fears: List[str] = Field(default_factory=list, description="恐惧")
    desires: List[str] = Field(default_factory=list, description="欲望")
    
    # 价值观
    values: List[str] = Field(default_factory=list, description="价值观")
    beliefs: List[str] = Field(default_factory=list, description="信念")
    moral_code: str = Field(default="", description="道德准则")
    
    # 能力特长
    skills: List[str] = Field(default_factory=list, description="技能")
    talents: List[str] = Field(default_factory=list, description="天赋")
    weaknesses: List[str] = Field(default_factory=list, description="弱点")
    
    # 性格总结
    personality_summary: str = Field(default="", description="性格总结")


class CharacterBackground(BaseModel):
    """角色背景"""
    
    # 基本信息
    birthdate: Optional[date] = Field(default=None, description="出生日期")
    birthplace: str = Field(default="", description="出生地")
    nationality: str = Field(default="", description="国籍")
    ethnicity: str = Field(default="", description="种族")
    
    # 家庭背景
    family_status: str = Field(default="", description="家庭状况")
    parents: List[str] = Field(default_factory=list, description="父母")
    siblings: List[str] = Field(default_factory=list, description="兄弟姐妹")
    children: List[str] = Field(default_factory=list, description="子女")
    
    # 教育经历
    education_level: str = Field(default="", description="教育水平")
    schools: List[str] = Field(default_factory=list, description="就读学校")
    degrees: List[str] = Field(default_factory=list, description="学位")
    
    # 职业经历
    current_occupation: str = Field(default="", description="当前职业")
    previous_jobs: List[str] = Field(default_factory=list, description="以往工作")
    career_goals: str = Field(default="", description="职业目标")
    
    # 社会关系
    social_class: str = Field(default="", description="社会阶层")
    economic_status: str = Field(default="", description="经济状况")
    political_affiliation: str = Field(default="", description="政治倾向")
    
    # 重要经历
    life_events: List[Dict[str, Any]] = Field(default_factory=list, description="人生重要事件")
    traumas: List[str] = Field(default_factory=list, description="创伤经历")
    achievements: List[str] = Field(default_factory=list, description="成就")
    
    # 背景总结
    background_summary: str = Field(default="", description="背景总结")


class CharacterRelationship(BaseModel):
    """角色关系"""
    
    # 关系基础信息
    target_character: UUID = Field(..., description="目标角色ID")
    relationship_type: RelationType = Field(..., description="关系类型")
    relationship_status: RelationshipStatus = Field(default=RelationshipStatus.ACTIVE, description="关系状态")
    
    # 关系描述
    relationship_name: str = Field(default="", description="关系名称")
    description: str = Field(default="", description="关系描述")
    
    # 关系强度和重要性
    intimacy_level: float = Field(default=0.5, description="亲密度 (0.0-1.0)")
    trust_level: float = Field(default=0.5, description="信任度 (0.0-1.0)")
    conflict_level: float = Field(default=0.0, description="冲突度 (0.0-1.0)")
    
    # 时间信息
    relationship_start: Optional[datetime] = Field(default=None, description="关系开始时间")
    relationship_end: Optional[datetime] = Field(default=None, description="关系结束时间")
    
    # 关系历史
    relationship_history: List[Dict[str, Any]] = Field(default_factory=list, description="关系发展历史")
    
    # 关系影响
    mutual_influence: str = Field(default="", description="相互影响")
    shared_experiences: List[str] = Field(default_factory=list, description="共同经历")
    
    @validator('intimacy_level', 'trust_level', 'conflict_level')
    def validate_levels(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("关系级别必须在0.0-1.0之间")
        return v
    
    @validator('relationship_end')
    def validate_relationship_duration(cls, v, values):
        if v and 'relationship_start' in values and values['relationship_start']:
            if v <= values['relationship_start']:
                raise ValueError("关系结束时间必须晚于开始时间")
        return v


class Character(StoryElement):
    """角色模型"""

    # 基础信息
    element_type: ElementType = Field(default=ElementType.CHARACTER, description="元素类型")
    full_name: str = Field(..., description="全名")
    nickname: str = Field(default="", description="昵称")
    title: str = Field(default="", description="称号")
    gender: Gender = Field(default=Gender.UNKNOWN, description="性别")

    def __init__(self, **data):
        # 如果提供了full_name但没有name，使用full_name作为name
        if 'full_name' in data and 'name' not in data:
            data['name'] = data['full_name']
        super().__init__(**data)
    
    # 角色详细信息
    appearance: CharacterAppearance = Field(default_factory=CharacterAppearance, description="外貌")
    personality: CharacterPersonality = Field(default_factory=CharacterPersonality, description="性格")
    background: CharacterBackground = Field(default_factory=CharacterBackground, description="背景")
    
    # 角色关系
    character_relationships: List[CharacterRelationship] = Field(default_factory=list, description="角色关系")
    
    # 角色在故事中的作用
    story_role: str = Field(default="", description="故事角色")
    character_arc: str = Field(default="", description="角色弧线")
    motivation: str = Field(default="", description="动机")
    goals: List[str] = Field(default_factory=list, description="目标")
    conflicts: List[str] = Field(default_factory=list, description="冲突")
    
    # 角色发展
    character_development: List[Dict[str, Any]] = Field(default_factory=list, description="角色发展")
    key_moments: List[Dict[str, Any]] = Field(default_factory=list, description="关键时刻")
    
    # 角色状态
    is_alive: bool = Field(default=True, description="是否存活")
    current_location: Optional[UUID] = Field(default=None, description="当前位置")
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if not v or not v.strip():
            raise ValueError("角色全名不能为空")
        
        if len(v) > 100:
            raise ValueError("角色全名不能超过100个字符")
        
        return v.strip()
    
    def _validate_specific(self, result: ValidationResult):
        """角色特定验证"""
        # 调用父类验证
        super()._validate_specific(result)
        
        # 验证全名
        if not self.full_name or not self.full_name.strip():
            result.add_error("角色全名不能为空")
        elif len(self.full_name) > 100:
            result.add_error("角色全名不能超过100个字符")
        
        # 验证昵称
        if self.nickname and len(self.nickname) > 50:
            result.add_error("昵称不能超过50个字符")
        
        # 验证称号
        if self.title and len(self.title) > 50:
            result.add_error("称号不能超过50个字符")
        
        # 验证故事角色
        if self.story_role and len(self.story_role) > 100:
            result.add_error("故事角色描述不能超过100个字符")
        
        # 验证角色弧线
        if self.character_arc and len(self.character_arc) > 500:
            result.add_error("角色弧线描述不能超过500个字符")
        
        # 验证动机
        if self.motivation and len(self.motivation) > 500:
            result.add_error("动机描述不能超过500个字符")
        
        # 验证关系
        for i, relationship in enumerate(self.character_relationships):
            rel_result = relationship.validate_model()
            if not rel_result.is_valid:
                for error in rel_result.errors:
                    result.add_error(f"关系{i+1}: {error}")
    
    def add_relationship(self, target_character: UUID, relationship_type: RelationType,
                        relationship_name: str = "", description: str = "",
                        intimacy_level: float = 0.5, trust_level: float = 0.5,
                        conflict_level: float = 0.0):
        """添加角色关系"""
        relationship = CharacterRelationship(
            target_character=target_character,
            relationship_type=relationship_type,
            relationship_name=relationship_name,
            description=description,
            intimacy_level=intimacy_level,
            trust_level=trust_level,
            conflict_level=conflict_level,
            relationship_start=datetime.now()
        )
        
        self.character_relationships.append(relationship)
        self.update_timestamp()
    
    def update_relationship(self, target_character: UUID, **kwargs):
        """更新角色关系"""
        for relationship in self.character_relationships:
            if relationship.target_character == target_character:
                for key, value in kwargs.items():
                    if hasattr(relationship, key):
                        setattr(relationship, key, value)
                self.update_timestamp()
                return True
        return False
    
    def remove_relationship(self, target_character: UUID):
        """移除角色关系"""
        original_count = len(self.character_relationships)
        self.character_relationships = [
            rel for rel in self.character_relationships
            if rel.target_character != target_character
        ]

        if len(self.character_relationships) != original_count:
            self.update_timestamp()
            return True
        return False
    
    def get_relationship(self, target_character: UUID) -> Optional[CharacterRelationship]:
        """获取与指定角色的关系"""
        for relationship in self.character_relationships:
            if relationship.target_character == target_character:
                return relationship
        return None

    def get_relationships_by_type(self, relationship_type: RelationType) -> List[CharacterRelationship]:
        """获取指定类型的关系"""
        return [rel for rel in self.character_relationships if rel.relationship_type == relationship_type.value]
    
    def add_character_development(self, chapter_id: UUID, development: str, 
                                 development_type: str = "growth"):
        """添加角色发展记录"""
        development_record = {
            'chapter_id': chapter_id,
            'development': development,
            'development_type': development_type,
            'timestamp': datetime.now().isoformat()
        }
        
        self.character_development.append(development_record)
        self.update_timestamp()
    
    def add_key_moment(self, chapter_id: UUID, moment: str, impact: str = ""):
        """添加关键时刻"""
        key_moment = {
            'chapter_id': chapter_id,
            'moment': moment,
            'impact': impact,
            'timestamp': datetime.now().isoformat()
        }
        
        self.key_moments.append(key_moment)
        self.update_timestamp()
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.nickname:
            return f"{self.full_name} ({self.nickname})"
        return self.full_name
    
    def get_age(self) -> Optional[int]:
        """获取年龄"""
        if self.background.birthdate:
            today = date.today()
            return today.year - self.background.birthdate.year - (
                (today.month, today.day) < (self.background.birthdate.month, self.background.birthdate.day)
            )
        return self.appearance.age
    
    def calculate_relationship_score(self, target_character: UUID) -> float:
        """计算与目标角色的关系得分"""
        relationship = self.get_relationship(target_character)
        if not relationship:
            return 0.0
        
        # 综合亲密度、信任度，减去冲突度
        score = (relationship.intimacy_level + relationship.trust_level) / 2 - relationship.conflict_level / 2
        return max(0.0, min(1.0, score))


class CharacterValidator(StoryElementValidator):
    """角色验证器"""
    
    def validate(self, model: Character) -> ValidationResult:
        """验证角色"""
        result = ValidationResult()
        
        # 基础验证
        base_result = super().validate(model)
        result.merge(base_result)
        
        # 角色特定验证
        name_result = self.validate_required_string(model.full_name, "角色全名", 100)
        result.merge(name_result)
        
        nickname_result = self.validate_optional_string(model.nickname, "昵称", 50)
        result.merge(nickname_result)
        
        title_result = self.validate_optional_string(model.title, "称号", 50)
        result.merge(title_result)
        
        story_role_result = self.validate_optional_string(model.story_role, "故事角色", 100)
        result.merge(story_role_result)
        
        arc_result = self.validate_optional_string(model.character_arc, "角色弧线", 500)
        result.merge(arc_result)
        
        motivation_result = self.validate_optional_string(model.motivation, "动机", 500)
        result.merge(motivation_result)
        
        # 验证外貌
        if model.appearance.age is not None:
            if model.appearance.age < 0 or model.appearance.age > 200:
                result.add_error("年龄必须在0-200之间")
        
        # 验证关系
        for i, relationship in enumerate(model.character_relationships):
            rel_result = self._validate_character_relationship(relationship)
            if not rel_result.is_valid:
                for error in rel_result.errors:
                    result.add_error(f"关系{i+1}: {error}")
        
        return result
    
    def _validate_character_relationship(self, relationship: CharacterRelationship) -> ValidationResult:
        """验证角色关系"""
        result = ValidationResult()
        
        # 验证关系级别
        for field_name, field_value in [
            ('intimacy_level', relationship.intimacy_level),
            ('trust_level', relationship.trust_level),
            ('conflict_level', relationship.conflict_level)
        ]:
            if field_value < 0.0 or field_value > 1.0:
                result.add_error(f"{field_name}必须在0.0-1.0之间")
        
        # 验证时间范围
        if (relationship.relationship_start and relationship.relationship_end and 
            relationship.relationship_end <= relationship.relationship_start):
            result.add_error("关系结束时间必须晚于开始时间")
        
        # 验证描述长度
        if relationship.description and len(relationship.description) > 500:
            result.add_error("关系描述不能超过500个字符")
        
        if relationship.relationship_name and len(relationship.relationship_name) > 100:
            result.add_error("关系名称不能超过100个字符")
        
        return result
