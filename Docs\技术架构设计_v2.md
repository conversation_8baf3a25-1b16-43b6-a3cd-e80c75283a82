# 笔落App技术架构设计 v2.0

**版本**: 2.0  
**创建日期**: 2025-09-12  
**修订说明**: 基于性能分析和改进建议的优化架构  
**状态**: 修订版

## 版本修订说明

### v2.0 主要改进
1. **混合存储方案**: 优化文件存储结构，提升性能和可靠性
2. **缓存层设计**: 增加多级缓存机制，提升响应速度
3. **索引系统**: 实现全文搜索和快速查询能力
4. **事件系统**: 支持组件间解耦通信和插件扩展
5. **AI集成优化**: 增强AI服务管理和性能优化

---

## 1. 整体架构概览

### 1.1 架构原则
- **模块化设计**: 清晰的模块边界和职责分离
- **高内聚低耦合**: 模块内部紧密协作，模块间松散耦合
- **可扩展性**: 支持功能扩展和插件机制
- **高性能**: 优化数据访问和用户体验
- **可维护性**: 清晰的代码结构和文档

### 1.2 技术栈
```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│            PyQt6 + QML                  │
├─────────────────────────────────────────┤
│              Service Layer              │
│     Business Logic + Event System      │
├─────────────────────────────────────────┤
│               Core Layer                │
│        Models + Validation              │
├─────────────────────────────────────────┤
│              Storage Layer              │
│    File System + Cache + Index         │
├─────────────────────────────────────────┤
│            Integration Layer            │
│         AI Services + External         │
└─────────────────────────────────────────┘
```

### 1.3 核心模块划分
- **core**: 数据模型、业务逻辑、验证规则
- **storage**: 文件存储、缓存管理、索引系统
- **ai**: AI服务集成、内容生成、优化处理
- **ui**: 用户界面、组件、主题管理
- **events**: 事件系统、消息传递、插件支持
- **utils**: 工具函数、配置管理、日志系统

---

## 2. 存储架构设计

### 2.1 混合存储方案

#### 2.1.1 文件存储结构
```
project_name/
├── .bamboo/                    # 系统文件夹
│   ├── metadata/
│   │   ├── project.json        # 项目元数据
│   │   ├── index.json          # 全局索引
│   │   ├── cache.json          # 缓存配置
│   │   └── version.json        # 版本信息
│   ├── cache/                  # 缓存文件
│   │   ├── search_index/       # 搜索索引
│   │   ├── thumbnails/         # 缩略图缓存
│   │   └── temp/               # 临时文件
│   ├── backups/                # 备份文件
│   │   ├── auto/               # 自动备份
│   │   └── manual/             # 手动备份
│   └── logs/                   # 日志文件
├── content/                    # 内容数据
│   ├── outline/
│   │   └── outline.json        # 故事大纲
│   ├── arcs/                   # 故事主线
│   │   ├── arc_001.json
│   │   └── arc_002.json
│   ├── chapters/               # 章节内容
│   │   ├── metadata/           # 章节元数据
│   │   │   ├── chapter_001.json
│   │   │   └── chapter_002.json
│   │   └── content/            # 章节正文
│   │       ├── chapter_001.txt
│   │       └── chapter_002.txt
│   └── elements/               # 故事元素
│       ├── characters/         # 角色数据
│       ├── scenes/             # 场景数据
│       └── events/             # 事件数据
├── assets/                     # 资源文件
│   ├── images/                 # 图片资源
│   ├── templates/              # 模板文件
│   └── references/             # 参考资料
└── exports/                    # 导出文件
    ├── txt/
    ├── pdf/
    └── epub/
```

#### 2.1.2 数据分层策略
```python
# 数据访问层次
Level 1: Memory Cache (最快访问)
├── 当前编辑内容
├── 最近访问的章节
└── 常用角色/场景信息

Level 2: Disk Cache (快速访问)
├── 索引数据
├── 缩略图
└── 预处理内容

Level 3: File Storage (标准访问)
├── 完整章节内容
├── 元素详细信息
└── 历史版本数据

Level 4: Compressed Archive (归档访问)
├── 旧版本备份
├── 导出文件
└── 日志归档
```

### 2.2 缓存系统设计

#### 2.2.1 多级缓存架构
```python
class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.memory_cache = MemoryCache(max_size=100MB)
        self.disk_cache = DiskCache(max_size=500MB)
        self.index_cache = IndexCache()
        
    def get(self, key: str, cache_level: CacheLevel = CacheLevel.AUTO):
        """获取缓存数据"""
        # 1. 尝试内存缓存
        if data := self.memory_cache.get(key):
            return data
            
        # 2. 尝试磁盘缓存
        if data := self.disk_cache.get(key):
            self.memory_cache.set(key, data)  # 提升到内存
            return data
            
        # 3. 从文件系统加载
        if data := self.load_from_storage(key):
            self.disk_cache.set(key, data)
            return data
            
        return None
```

#### 2.2.2 缓存策略
- **LRU淘汰**: 最近最少使用的数据优先淘汰
- **智能预加载**: 基于用户行为预测预加载数据
- **分级存储**: 根据数据重要性和访问频率分级缓存
- **自动清理**: 定期清理过期和无效缓存

### 2.3 索引系统设计

#### 2.3.1 全文搜索索引
```python
class SearchIndexManager:
    """搜索索引管理器"""
    
    def __init__(self):
        self.text_index = TextIndex()      # 全文索引
        self.element_index = ElementIndex() # 元素索引
        self.relation_index = RelationIndex() # 关系索引
        
    def build_index(self, project: WritingProject):
        """构建项目索引"""
        # 1. 文本内容索引
        for chapter in project.chapters:
            self.text_index.add_document(
                doc_id=chapter.id,
                content=chapter.content,
                metadata={
                    'title': chapter.title,
                    'arc': chapter.arc_id,
                    'created_at': chapter.created_at
                }
            )
            
        # 2. 元素索引
        for character in project.characters:
            self.element_index.add_element(
                element_id=character.id,
                element_type='character',
                searchable_fields={
                    'name': character.name,
                    'description': character.description,
                    'traits': character.personality
                }
            )
```

#### 2.3.2 索引类型
- **倒排索引**: 支持快速全文搜索
- **B+树索引**: 支持范围查询和排序
- **哈希索引**: 支持精确匹配查询
- **关系索引**: 支持复杂关联查询

---

## 3. 事件系统架构

### 3.1 事件驱动设计

#### 3.1.1 事件总线
```python
class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.subscribers: Dict[str, List[Callable]] = {}
        self.event_queue = asyncio.Queue()
        
    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(handler)
        
    def publish(self, event: Event):
        """发布事件"""
        self.event_queue.put_nowait(event)
        
    async def process_events(self):
        """处理事件队列"""
        while True:
            event = await self.event_queue.get()
            await self._dispatch_event(event)
```

#### 3.1.2 核心事件类型
```python
# 数据事件
class DataEvent(Event):
    PROJECT_CREATED = "project.created"
    PROJECT_OPENED = "project.opened"
    CHAPTER_MODIFIED = "chapter.modified"
    CHARACTER_ADDED = "character.added"

# UI事件
class UIEvent(Event):
    WINDOW_FOCUSED = "ui.window.focused"
    TAB_SWITCHED = "ui.tab.switched"
    SEARCH_REQUESTED = "ui.search.requested"

# AI事件
class AIEvent(Event):
    GENERATION_STARTED = "ai.generation.started"
    GENERATION_COMPLETED = "ai.generation.completed"
    MODEL_SWITCHED = "ai.model.switched"
```

### 3.2 插件系统支持

#### 3.2.1 插件接口
```python
class Plugin:
    """插件基类"""
    
    def __init__(self, name: str, version: str):
        self.name = name
        self.version = version
        self.enabled = False
        
    def initialize(self, app_context: AppContext):
        """插件初始化"""
        pass
        
    def on_event(self, event: Event):
        """事件处理"""
        pass
        
    def cleanup(self):
        """插件清理"""
        pass
```

#### 3.2.2 插件管理器
```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.plugins: Dict[str, Plugin] = {}
        
    def load_plugin(self, plugin_path: str):
        """加载插件"""
        plugin = self._import_plugin(plugin_path)
        self.plugins[plugin.name] = plugin
        plugin.initialize(self.app_context)
        
    def enable_plugin(self, plugin_name: str):
        """启用插件"""
        if plugin := self.plugins.get(plugin_name):
            plugin.enabled = True
            self.event_bus.subscribe_all(plugin.on_event)
```

---

## 4. AI集成架构

### 4.1 AI服务抽象层

#### 4.1.1 统一接口设计
```python
class AIService(ABC):
    """AI服务抽象基类"""
    
    @abstractmethod
    async def generate_content(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> GenerationResult:
        """生成内容"""
        pass
        
    @abstractmethod
    async def optimize_content(
        self, 
        content: str, 
        optimization_type: OptimizationType
    ) -> OptimizationResult:
        """优化内容"""
        pass
        
    @abstractmethod
    def get_usage_stats(self) -> UsageStats:
        """获取使用统计"""
        pass
```

#### 4.1.2 服务适配器
```python
class OpenAIAdapter(AIService):
    """OpenAI服务适配器"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        self.rate_limiter = RateLimiter(requests_per_minute=60)
        
    async def generate_content(self, prompt: str, context: GenerationContext, options: GenerationOptions):
        await self.rate_limiter.acquire()
        
        messages = self._build_messages(prompt, context)
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=options.temperature,
            max_tokens=options.max_tokens
        )
        
        return GenerationResult(
            content=response.choices[0].message.content,
            usage=response.usage,
            model=self.model
        )
```

### 4.2 AI服务管理

#### 4.2.1 服务池管理
```python
class AIServicePool:
    """AI服务池"""
    
    def __init__(self):
        self.services: Dict[str, AIService] = {}
        self.default_service: Optional[str] = None
        self.load_balancer = LoadBalancer()
        
    def add_service(self, name: str, service: AIService):
        """添加服务"""
        self.services[name] = service
        if not self.default_service:
            self.default_service = name
            
    async def generate(self, request: GenerationRequest) -> GenerationResult:
        """生成内容"""
        service_name = request.preferred_service or self.default_service
        service = self.services[service_name]
        
        try:
            return await service.generate_content(
                request.prompt, 
                request.context, 
                request.options
            )
        except Exception as e:
            # 故障转移到其他服务
            return await self._fallback_generate(request, exclude=service_name)
```

#### 4.2.2 请求队列和批处理
```python
class AIRequestManager:
    """AI请求管理器"""
    
    def __init__(self, service_pool: AIServicePool):
        self.service_pool = service_pool
        self.request_queue = asyncio.PriorityQueue()
        self.batch_processor = BatchProcessor()
        
    async def submit_request(self, request: GenerationRequest) -> Future[GenerationResult]:
        """提交请求"""
        future = asyncio.Future()
        priority_request = PriorityRequest(
            priority=request.priority,
            request=request,
            future=future
        )
        
        await self.request_queue.put(priority_request)
        return future
        
    async def process_requests(self):
        """处理请求队列"""
        while True:
            # 批量获取请求
            batch = await self._get_batch()
            
            # 并行处理
            tasks = [
                self.service_pool.generate(req.request) 
                for req in batch
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 返回结果
            for req, result in zip(batch, results):
                if isinstance(result, Exception):
                    req.future.set_exception(result)
                else:
                    req.future.set_result(result)
```

### 4.3 成本控制和监控

#### 4.3.1 使用统计
```python
class UsageTracker:
    """使用统计跟踪器"""
    
    def __init__(self):
        self.daily_usage = defaultdict(int)
        self.monthly_usage = defaultdict(int)
        self.cost_tracker = CostTracker()
        
    def record_usage(self, service: str, tokens: int, cost: float):
        """记录使用情况"""
        today = datetime.now().date()
        month = today.replace(day=1)
        
        self.daily_usage[f"{service}:{today}"] += tokens
        self.monthly_usage[f"{service}:{month}"] += tokens
        self.cost_tracker.add_cost(service, cost)
        
    def get_usage_report(self, period: str = "daily") -> UsageReport:
        """获取使用报告"""
        if period == "daily":
            return self._generate_daily_report()
        elif period == "monthly":
            return self._generate_monthly_report()
```

#### 4.3.2 配额管理
```python
class QuotaManager:
    """配额管理器"""
    
    def __init__(self):
        self.quotas: Dict[str, Quota] = {}
        
    def set_quota(self, service: str, quota: Quota):
        """设置配额"""
        self.quotas[service] = quota
        
    def check_quota(self, service: str, tokens: int) -> bool:
        """检查配额"""
        quota = self.quotas.get(service)
        if not quota:
            return True
            
        current_usage = self.usage_tracker.get_current_usage(service)
        return current_usage + tokens <= quota.limit
        
    def reserve_quota(self, service: str, tokens: int) -> bool:
        """预留配额"""
        if self.check_quota(service, tokens):
            self.usage_tracker.reserve_tokens(service, tokens)
            return True
        return False
```

---

## 5. 性能优化策略

### 5.1 内存管理

#### 5.1.1 对象池模式
```python
class ObjectPool:
    """对象池"""
    
    def __init__(self, factory: Callable, max_size: int = 100):
        self.factory = factory
        self.pool = []
        self.max_size = max_size
        
    def acquire(self):
        """获取对象"""
        if self.pool:
            return self.pool.pop()
        return self.factory()
        
    def release(self, obj):
        """释放对象"""
        if len(self.pool) < self.max_size:
            obj.reset()  # 重置对象状态
            self.pool.append(obj)
```

#### 5.1.2 懒加载机制
```python
class LazyLoader:
    """懒加载器"""
    
    def __init__(self, loader_func: Callable):
        self.loader_func = loader_func
        self._loaded = False
        self._data = None
        
    def __call__(self):
        if not self._loaded:
            self._data = self.loader_func()
            self._loaded = True
        return self._data
        
    def invalidate(self):
        """使缓存失效"""
        self._loaded = False
        self._data = None
```

### 5.2 异步处理

#### 5.2.1 异步文件操作
```python
class AsyncFileManager:
    """异步文件管理器"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def read_file(self, path: str) -> str:
        """异步读取文件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self._read_file_sync, 
            path
        )
        
    async def write_file(self, path: str, content: str):
        """异步写入文件"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self.executor, 
            self._write_file_sync, 
            path, 
            content
        )
```

#### 5.2.2 后台任务管理
```python
class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self):
        self.tasks: Set[asyncio.Task] = set()
        
    def submit_task(self, coro) -> asyncio.Task:
        """提交后台任务"""
        task = asyncio.create_task(coro)
        self.tasks.add(task)
        task.add_done_callback(self.tasks.discard)
        return task
        
    async def shutdown(self):
        """关闭所有任务"""
        for task in self.tasks:
            task.cancel()
        await asyncio.gather(*self.tasks, return_exceptions=True)
```

---

## 6. 安全架构

### 6.1 数据安全

#### 6.1.1 加密存储
```python
class SecureStorage:
    """安全存储"""
    
    def __init__(self, encryption_key: bytes):
        self.cipher = Fernet(encryption_key)
        
    def encrypt_data(self, data: str) -> bytes:
        """加密数据"""
        return self.cipher.encrypt(data.encode())
        
    def decrypt_data(self, encrypted_data: bytes) -> str:
        """解密数据"""
        return self.cipher.decrypt(encrypted_data).decode()
        
    def store_sensitive(self, key: str, value: str):
        """存储敏感信息"""
        encrypted_value = self.encrypt_data(value)
        self._store_to_keyring(key, encrypted_value)
```

#### 6.1.2 输入验证
```python
class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_project_name(name: str) -> bool:
        """验证项目名称"""
        if not name or len(name) > 100:
            return False
        # 检查非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        return not re.search(illegal_chars, name)
        
    @staticmethod
    def sanitize_content(content: str) -> str:
        """清理内容"""
        # 移除潜在的恶意脚本
        content = re.sub(r'<script.*?</script>', '', content, flags=re.IGNORECASE)
        return content
```

### 6.2 API安全

#### 6.2.1 密钥管理
```python
class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self, secure_storage: SecureStorage):
        self.secure_storage = secure_storage
        
    def store_api_key(self, service: str, api_key: str):
        """存储API密钥"""
        key_name = f"api_key_{service}"
        self.secure_storage.store_sensitive(key_name, api_key)
        
    def get_api_key(self, service: str) -> Optional[str]:
        """获取API密钥"""
        key_name = f"api_key_{service}"
        return self.secure_storage.get_sensitive(key_name)
        
    def validate_api_key(self, service: str, api_key: str) -> bool:
        """验证API密钥格式"""
        patterns = {
            'openai': r'^sk-[a-zA-Z0-9]{48}$',
            'deepseek': r'^sk-[a-zA-Z0-9]{32}$',
        }
        pattern = patterns.get(service)
        return bool(pattern and re.match(pattern, api_key))
```

---

## 7. 监控和诊断

### 7.1 日志系统

#### 7.1.1 结构化日志
```python
class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.setup_handlers()
        
    def log_event(self, event_type: str, **kwargs):
        """记录事件"""
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'data': kwargs
        }
        self.logger.info(json.dumps(log_data))
        
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能数据"""
        self.log_event('performance', 
                      operation=operation, 
                      duration=duration, 
                      **kwargs)
```

### 7.2 性能监控

#### 7.2.1 性能指标收集
```python
class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        
    @contextmanager
    def measure(self, operation: str):
        """测量操作耗时"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.metrics[operation].append(duration)
            
    def get_stats(self, operation: str) -> Dict[str, float]:
        """获取统计信息"""
        durations = self.metrics[operation]
        if not durations:
            return {}
            
        return {
            'count': len(durations),
            'avg': sum(durations) / len(durations),
            'min': min(durations),
            'max': max(durations),
            'p95': self._percentile(durations, 0.95)
        }
```

---

## 8. 部署架构

### 8.1 打包策略

#### 8.1.1 模块化打包
```python
# 打包配置
PACKAGE_CONFIG = {
    'core_modules': [
        'src.core',
        'src.storage', 
        'src.events'
    ],
    'optional_modules': [
        'src.ai',
        'src.plugins'
    ],
    'resources': [
        'assets/',
        'templates/',
        'locales/'
    ]
}
```

#### 8.1.2 增量更新
```python
class UpdateManager:
    """更新管理器"""
    
    def __init__(self):
        self.current_version = self.get_current_version()
        
    async def check_updates(self) -> Optional[UpdateInfo]:
        """检查更新"""
        latest_version = await self.fetch_latest_version()
        if self.is_newer_version(latest_version, self.current_version):
            return UpdateInfo(
                version=latest_version,
                download_url=self.get_download_url(latest_version),
                changelog=await self.fetch_changelog(latest_version)
            )
        return None
        
    async def apply_update(self, update_info: UpdateInfo):
        """应用更新"""
        # 下载更新包
        update_package = await self.download_update(update_info.download_url)
        
        # 备份当前版本
        await self.backup_current_version()
        
        # 应用更新
        await self.install_update(update_package)
        
        # 验证更新
        if not await self.verify_update():
            await self.rollback_update()
            raise UpdateError("Update verification failed")
```

### 8.2 配置管理

#### 8.2.1 环境配置
```python
class ConfigManager:
    """配置管理器"""

    def __init__(self):
        self.config_path = self.get_config_path()
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            'app': {
                'theme': 'default',
                'language': 'zh_CN',
                'auto_save_interval': 30
            },
            'storage': {
                'cache_size': 100,
                'backup_interval': 300,
                'compression_enabled': True
            },
            'ai': {
                'default_service': 'openai',
                'timeout': 30,
                'retry_count': 3
            }
        }

        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                return self.merge_config(default_config, user_config)

        return default_config
```

#### 8.2.2 运行时配置
```python
class RuntimeConfig:
    """运行时配置"""

    def __init__(self):
        self.debug_mode = os.getenv('BAMBOO_DEBUG', 'false').lower() == 'true'
        self.log_level = os.getenv('BAMBOO_LOG_LEVEL', 'INFO')
        self.data_dir = os.getenv('BAMBOO_DATA_DIR', self.get_default_data_dir())

    def get_default_data_dir(self) -> str:
        """获取默认数据目录"""
        if sys.platform == 'win32':
            return os.path.join(os.getenv('APPDATA'), 'BambooFall')
        else:
            return os.path.join(os.path.expanduser('~'), '.bamboofall')
```

---

## 9. 总结

### 9.1 架构优势
1. **高性能**: 多级缓存和索引系统确保快速响应
2. **可扩展**: 事件系统和插件机制支持功能扩展
3. **可靠性**: 完善的备份和恢复机制保障数据安全
4. **易维护**: 清晰的模块划分和接口设计
5. **用户友好**: 优化的存储结构和异步处理提升体验

### 9.2 技术风险控制
1. **性能风险**: 通过缓存和索引系统缓解大数据量性能问题
2. **可靠性风险**: 多重备份和故障恢复机制保障数据安全
3. **扩展性风险**: 事件驱动架构支持灵活的功能扩展
4. **维护风险**: 标准化的代码结构和文档降低维护成本

### 9.3 实施建议
1. **分阶段实施**: 先实现核心架构，再逐步添加高级功能
2. **性能测试**: 在每个阶段进行性能基准测试
3. **代码审查**: 严格的代码审查确保架构一致性
4. **文档维护**: 及时更新架构文档和API文档

---

*本文档版本: v2.0*
*最后更新: 2025-09-12*
