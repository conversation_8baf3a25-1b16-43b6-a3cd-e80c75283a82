"""
主窗口测试模块
"""

import pytest
import sys
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

from src.ui.main_window import MainWindow, WelcomeWidget
from src.core.models.project import WritingProject


@pytest.fixture(scope="module")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication(sys.argv)
    else:
        app = QApplication.instance()
    yield app


@pytest.fixture
def main_window(qapp):
    """创建主窗口实例"""
    window = MainWindow()
    yield window
    window.close()


@pytest.fixture
def welcome_widget(qapp):
    """创建欢迎界面实例"""
    widget = WelcomeWidget()
    yield widget


class TestWelcomeWidget:
    """欢迎界面测试类"""
    
    def test_welcome_widget_creation(self, welcome_widget):
        """测试欢迎界面创建"""
        assert welcome_widget is not None
        assert welcome_widget.windowTitle() == ""
        
    def test_welcome_widget_ui_elements(self, welcome_widget):
        """测试欢迎界面UI元素"""
        # 检查主要组件是否存在
        assert welcome_widget.recent_list is not None
        
    def test_signal_emission(self, welcome_widget, qtbot):
        """测试信号发射"""
        with qtbot.waitSignal(welcome_widget.new_project_requested, timeout=1000):
            # 模拟点击新建项目按钮
            new_btn = welcome_widget.findChild(type(welcome_widget), "PrimaryButton")
            if new_btn:
                QTest.mouseClick(new_btn, Qt.MouseButton.LeftButton)


class TestMainWindow:
    """主窗口测试类"""
    
    def test_main_window_creation(self, main_window):
        """测试主窗口创建"""
        assert main_window is not None
        assert "笔落 BambooFall" in main_window.windowTitle()
        
    def test_main_window_initial_state(self, main_window):
        """测试主窗口初始状态"""
        assert main_window.current_project is None
        assert main_window.writing_interface is None
        assert main_window.stacked_widget is not None
        assert main_window.welcome_widget is not None
        
    def test_main_window_size(self, main_window):
        """测试主窗口尺寸"""
        assert main_window.minimumSize().width() >= 1024
        assert main_window.minimumSize().height() >= 768
        
    @patch('src.ui.main_window.ProjectDialog')
    def test_show_new_project_dialog(self, mock_dialog, main_window):
        """测试显示新建项目对话框"""
        # 模拟对话框
        mock_instance = Mock()
        mock_instance.exec.return_value = mock_instance.DialogCode.Accepted
        mock_instance.get_project_data.return_value = {
            'name': 'Test Project',
            'author': 'Test Author',
            'genre': 'FANTASY',
            'description': 'Test Description'
        }
        mock_dialog.return_value = mock_instance
        
        # 调用方法
        main_window.show_new_project_dialog()
        
        # 验证对话框被创建
        mock_dialog.assert_called_once()
        
    @patch('src.ui.main_window.QFileDialog')
    def test_show_open_project_dialog(self, mock_file_dialog, main_window):
        """测试显示打开项目对话框"""
        # 模拟文件对话框
        mock_file_dialog.getOpenFileName.return_value = ("/path/to/project.bamboo", "")
        
        with patch.object(main_window, 'open_project') as mock_open:
            main_window.show_open_project_dialog()
            mock_open.assert_called_once_with("/path/to/project.bamboo")
            
    @patch('src.ui.main_window.SettingsDialog')
    def test_show_settings_dialog(self, mock_dialog, main_window):
        """测试显示设置对话框"""
        mock_instance = Mock()
        mock_dialog.return_value = mock_instance
        
        main_window.show_settings_dialog()
        
        mock_dialog.assert_called_once()
        mock_instance.exec.assert_called_once()
        
    @patch('src.core.storage.manager.StorageManager')
    def test_create_new_project(self, mock_storage, main_window):
        """测试创建新项目"""
        # 模拟存储管理器
        mock_storage_instance = Mock()
        mock_storage_instance.save_project.return_value = "/path/to/project"
        mock_storage.return_value = mock_storage_instance
        
        project_data = {
            'name': 'Test Project',
            'author': 'Test Author',
            'genre': 'FANTASY',
            'description': 'Test Description'
        }
        
        with patch.object(main_window, 'open_writing_interface') as mock_open:
            main_window.create_new_project(project_data)
            mock_open.assert_called_once()
            
    def test_close_event_no_project(self, main_window, qtbot):
        """测试关闭事件（无项目）"""
        # 模拟关闭事件
        from PyQt6.QtGui import QCloseEvent
        event = QCloseEvent()
        
        main_window.closeEvent(event)
        
        # 事件应该被接受
        assert event.isAccepted()
        
    @patch('src.ui.main_window.QMessageBox')
    def test_close_event_with_project(self, mock_msgbox, main_window):
        """测试关闭事件（有项目）"""
        # 设置当前项目
        main_window.current_project = Mock()
        main_window.writing_interface = Mock()
        
        # 模拟用户选择保存
        mock_msgbox.question.return_value = mock_msgbox.StandardButton.Save
        
        from PyQt6.QtGui import QCloseEvent
        event = QCloseEvent()
        
        with patch.object(main_window.storage_manager, 'save_project'):
            main_window.closeEvent(event)
            
        # 验证消息框被显示
        mock_msgbox.question.assert_called_once()


class TestMainWindowIntegration:
    """主窗口集成测试"""
    
    def test_welcome_to_writing_workflow(self, main_window):
        """测试从欢迎界面到创作界面的工作流"""
        # 初始状态应该显示欢迎界面
        assert main_window.stacked_widget.currentWidget() == main_window.welcome_widget
        
        # 模拟项目创建
        mock_project = Mock(spec=WritingProject)
        mock_project.name = "Test Project"
        
        with patch.object(main_window, 'open_writing_interface') as mock_open:
            main_window.open_writing_interface(mock_project)
            mock_open.assert_called_once_with(mock_project)
            
    def test_signal_connections(self, main_window):
        """测试信号连接"""
        # 验证欢迎界面信号已连接
        welcome_widget = main_window.welcome_widget
        
        # 检查信号是否已连接（通过检查接收者数量）
        assert welcome_widget.new_project_requested.receivers() > 0
        assert welcome_widget.open_project_requested.receivers() > 0
        assert welcome_widget.settings_requested.receivers() > 0
        assert welcome_widget.project_selected.receivers() > 0
