"""
版本控制管理器测试模块
"""

import pytest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime
from uuid import uuid4

from src.core.storage.version_control import (
    VersionControlManager, VersionInfo, ChangeType, VersionControlError
)
from src.core.models.project import WritingProject


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def version_manager(temp_dir):
    """创建版本控制管理器实例"""
    return VersionControlManager(temp_dir)


@pytest.fixture
def sample_project():
    """创建示例项目"""
    return WritingProject(
        name="测试项目",
        author="测试作者",
        description="测试描述"
    )


class TestVersionInfo:
    """版本信息测试类"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        version_info = VersionInfo(
            version_id="v1.0.0",
            entity_id="test-entity",
            entity_type="project"
        )
        
        assert version_info.version_id == "v1.0.0"
        assert version_info.entity_id == "test-entity"
        assert version_info.entity_type == "project"
        assert version_info.created_at is not None
        assert version_info.change_type == ChangeType.UPDATE
        assert version_info.description == ""
        assert version_info.author == ""
        assert version_info.data_snapshot == {}
        assert version_info.parent_version is None
        
    def test_init_full(self):
        """测试完整初始化"""
        created_at = datetime.now()
        data_snapshot = {"name": "测试项目"}
        
        version_info = VersionInfo(
            version_id="v1.0.0",
            entity_id="test-entity",
            entity_type="project",
            created_at=created_at,
            change_type=ChangeType.CREATE,
            description="初始版本",
            author="测试作者",
            data_snapshot=data_snapshot,
            parent_version="v0.9.0"
        )
        
        assert version_info.created_at == created_at
        assert version_info.change_type == ChangeType.CREATE
        assert version_info.description == "初始版本"
        assert version_info.author == "测试作者"
        assert version_info.data_snapshot == data_snapshot
        assert version_info.parent_version == "v0.9.0"
        
    def test_to_dict(self):
        """测试转换为字典"""
        version_info = VersionInfo(
            version_id="v1.0.0",
            entity_id="test-entity",
            entity_type="project"
        )
        
        data = version_info.to_dict()
        
        assert data["version_id"] == "v1.0.0"
        assert data["entity_id"] == "test-entity"
        assert data["entity_type"] == "project"
        assert "created_at" in data
        assert "change_type" in data
        
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "version_id": "v1.0.0",
            "entity_id": "test-entity",
            "entity_type": "project",
            "created_at": "2024-01-01T12:00:00",
            "change_type": "CREATE",
            "description": "初始版本"
        }
        
        version_info = VersionInfo.from_dict(data)
        
        assert version_info.version_id == "v1.0.0"
        assert version_info.entity_id == "test-entity"
        assert version_info.change_type == ChangeType.CREATE
        assert version_info.description == "初始版本"


class TestVersionControlManager:
    """版本控制管理器测试类"""
    
    def test_init(self, temp_dir):
        """测试初始化"""
        manager = VersionControlManager(temp_dir)
        
        assert manager.version_dir == Path(temp_dir)
        assert manager.version_dir.exists()
        
    def test_create_version(self, version_manager, sample_project):
        """测试创建版本"""
        version_info = version_manager.create_version(
            entity_id=str(sample_project.id),
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE,
            description="初始版本",
            author="测试作者"
        )
        
        assert version_info is not None
        assert version_info.entity_id == str(sample_project.id)
        assert version_info.entity_type == "project"
        assert version_info.change_type == ChangeType.CREATE
        assert version_info.description == "初始版本"
        assert version_info.author == "测试作者"
        assert version_info.data_snapshot["name"] == sample_project.name
        
    def test_get_version_history(self, version_manager, sample_project):
        """测试获取版本历史"""
        entity_id = str(sample_project.id)
        
        # 创建几个版本
        v1 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE,
            description="初始版本"
        )
        
        # 修改项目并创建新版本
        sample_project.description = "更新的描述"
        v2 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.UPDATE,
            description="更新描述"
        )
        
        # 获取版本历史
        history = version_manager.get_version_history(entity_id)
        
        assert len(history) >= 2
        assert any(v.version_id == v1.version_id for v in history)
        assert any(v.version_id == v2.version_id for v in history)
        
        # 验证按时间排序（最新的在前）
        assert history[0].created_at >= history[1].created_at
        
    def test_get_version(self, version_manager, sample_project):
        """测试获取特定版本"""
        entity_id = str(sample_project.id)
        
        # 创建版本
        version_info = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE
        )
        
        # 获取版本
        retrieved_version = version_manager.get_version(version_info.version_id)
        
        assert retrieved_version is not None
        assert retrieved_version.version_id == version_info.version_id
        assert retrieved_version.entity_id == entity_id
        
    def test_restore_version(self, version_manager, sample_project):
        """测试恢复版本"""
        entity_id = str(sample_project.id)
        original_name = sample_project.name
        
        # 创建初始版本
        v1 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE
        )
        
        # 修改项目并创建新版本
        sample_project.name = "修改后的名称"
        v2 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.UPDATE
        )
        
        # 恢复到初始版本
        restored_data = version_manager.restore_version(v1.version_id)
        
        assert restored_data is not None
        assert restored_data["name"] == original_name
        
    def test_delete_version(self, version_manager, sample_project):
        """测试删除版本"""
        entity_id = str(sample_project.id)
        
        # 创建版本
        version_info = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE
        )
        
        # 验证版本存在
        assert version_manager.get_version(version_info.version_id) is not None
        
        # 删除版本
        version_manager.delete_version(version_info.version_id)
        
        # 验证版本已删除
        assert version_manager.get_version(version_info.version_id) is None
        
    def test_cleanup_old_versions(self, version_manager, sample_project):
        """测试清理旧版本"""
        entity_id = str(sample_project.id)
        
        # 创建多个版本
        versions = []
        for i in range(5):
            sample_project.description = f"版本 {i}"
            version = version_manager.create_version(
                entity_id=entity_id,
                entity_type="project",
                data_snapshot=sample_project.model_dump(),
                change_type=ChangeType.UPDATE
            )
            versions.append(version)
            
        # 清理，只保留最新的2个版本
        deleted_count = version_manager.cleanup_old_versions(entity_id, keep_count=2)
        
        assert deleted_count >= 3
        
        # 验证只剩下2个版本
        remaining_history = version_manager.get_version_history(entity_id)
        assert len(remaining_history) == 2
        
    def test_get_version_diff(self, version_manager, sample_project):
        """测试获取版本差异"""
        entity_id = str(sample_project.id)
        
        # 创建初始版本
        v1 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.CREATE
        )
        
        # 修改项目并创建新版本
        sample_project.description = "更新的描述"
        sample_project.author = "新作者"
        v2 = version_manager.create_version(
            entity_id=entity_id,
            entity_type="project",
            data_snapshot=sample_project.model_dump(),
            change_type=ChangeType.UPDATE
        )
        
        # 获取差异
        diff = version_manager.get_version_diff(v1.version_id, v2.version_id)
        
        assert diff is not None
        assert "description" in diff
        assert "author" in diff
        
    def test_get_statistics(self, version_manager, sample_project):
        """测试获取统计信息"""
        entity_id = str(sample_project.id)
        
        # 创建一些版本
        for i in range(3):
            version_manager.create_version(
                entity_id=entity_id,
                entity_type="project",
                data_snapshot=sample_project.model_dump(),
                change_type=ChangeType.UPDATE
            )
            
        stats = version_manager.get_statistics()
        
        assert stats["total_versions"] >= 3
        assert stats["total_entities"] >= 1
        assert "entity_types" in stats
        
    def test_error_handling_invalid_version_id(self, version_manager):
        """测试无效版本ID错误处理"""
        with pytest.raises(VersionControlError):
            version_manager.get_version("nonexistent-version")
            
    def test_error_handling_restore_nonexistent_version(self, version_manager):
        """测试恢复不存在版本的错误处理"""
        with pytest.raises(VersionControlError):
            version_manager.restore_version("nonexistent-version")
            
    def test_error_handling_invalid_entity_id(self, version_manager):
        """测试无效实体ID错误处理"""
        # 获取不存在实体的版本历史应该返回空列表
        history = version_manager.get_version_history("nonexistent-entity")
        assert history == []
        
    def test_version_id_generation(self, version_manager, sample_project):
        """测试版本ID生成"""
        entity_id = str(sample_project.id)
        
        # 创建多个版本
        versions = []
        for i in range(3):
            version = version_manager.create_version(
                entity_id=entity_id,
                entity_type="project",
                data_snapshot=sample_project.model_dump(),
                change_type=ChangeType.UPDATE
            )
            versions.append(version)
            
        # 验证版本ID是唯一的
        version_ids = [v.version_id for v in versions]
        assert len(set(version_ids)) == 3
        
        # 验证版本ID格式
        for version_id in version_ids:
            assert isinstance(version_id, str)
            assert len(version_id) > 0
            
    def test_concurrent_version_creation(self, version_manager, sample_project):
        """测试并发版本创建"""
        import threading
        
        entity_id = str(sample_project.id)
        results = []
        errors = []
        
        def create_version(index):
            try:
                sample_project.description = f"并发版本 {index}"
                version = version_manager.create_version(
                    entity_id=entity_id,
                    entity_type="project",
                    data_snapshot=sample_project.model_dump(),
                    change_type=ChangeType.UPDATE,
                    description=f"并发创建版本 {index}"
                )
                results.append(version)
            except Exception as e:
                errors.append(e)
                
        # 创建多个线程同时创建版本
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_version, args=(i,))
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证结果
        assert len(errors) == 0, f"并发版本创建出现错误: {errors}"
        assert len(results) == 5, "应该创建5个版本"
        
        # 验证每个版本都是唯一的
        version_ids = [r.version_id for r in results]
        assert len(set(version_ids)) == 5, "版本ID应该是唯一的"
