"""
AI服务基础类测试
"""

import pytest
from datetime import datetime
from uuid import uuid4

from src.core.ai.base import (
    AIProvider, GenerationType, GenerationContext, GenerationOptions,
    GenerationResult, StreamChunk, PromptTemplate, PromptManager,
    AIServiceError, AuthenticationError, RateLimitError
)


class TestAIProvider:
    """测试AI服务提供商枚举"""
    
    def test_provider_values(self):
        """测试提供商值"""
        assert AIProvider.OPENAI.value == "openai"
        assert AIProvider.DEEPSEEK.value == "deepseek"
        assert AIProvider.ZHIPU.value == "zhipu"
        assert AIProvider.ANTHROPIC.value == "anthropic"


class TestGenerationType:
    """测试生成类型枚举"""
    
    def test_generation_types(self):
        """测试生成类型"""
        assert GenerationType.CHARACTER_DESCRIPTION.value == "character_description"
        assert GenerationType.SCENE_DESCRIPTION.value == "scene_description"
        assert GenerationType.PLOT_DEVELOPMENT.value == "plot_development"
        assert GenerationType.DIALOGUE.value == "dialogue"
        assert GenerationType.CONTENT_OPTIMIZATION.value == "content_optimization"


class TestGenerationContext:
    """测试生成上下文"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        assert context.content_type == GenerationType.CHARACTER_DESCRIPTION
        assert context.project_id is None
        assert context.character_ids is None
        assert context.scene_ids is None
        assert context.event_ids is None
        assert context.existing_content is None
        assert context.style_preferences is None
        assert context.constraints is None
    
    def test_init_full(self):
        """测试完整初始化"""
        project_id = uuid4()
        character_ids = [uuid4(), uuid4()]
        
        context = GenerationContext(
            content_type=GenerationType.DIALOGUE,
            project_id=project_id,
            character_ids=character_ids,
            existing_content="现有内容",
            style_preferences={"tone": "formal"},
            constraints={"max_length": 1000}
        )
        
        assert context.content_type == GenerationType.DIALOGUE
        assert context.project_id == project_id
        assert context.character_ids == character_ids
        assert context.existing_content == "现有内容"
        assert context.style_preferences == {"tone": "formal"}
        assert context.constraints == {"max_length": 1000}


class TestGenerationOptions:
    """测试生成选项"""
    
    def test_default_values(self):
        """测试默认值"""
        options = GenerationOptions()
        assert options.max_tokens == 2000
        assert options.temperature == 0.7
        assert options.top_p == 1.0
        assert options.frequency_penalty == 0.0
        assert options.presence_penalty == 0.0
        assert options.stream is False
        assert options.timeout == 30
        assert options.max_retries == 3
    
    def test_custom_values(self):
        """测试自定义值"""
        options = GenerationOptions(
            max_tokens=1000,
            temperature=0.5,
            stream=True,
            timeout=60
        )
        assert options.max_tokens == 1000
        assert options.temperature == 0.5
        assert options.stream is True
        assert options.timeout == 60


class TestGenerationResult:
    """测试生成结果"""
    
    def test_init(self):
        """测试初始化"""
        result_id = uuid4()
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        options = GenerationOptions()
        created_at = datetime.now()
        
        result = GenerationResult(
            id=result_id,
            content="生成的内容",
            provider=AIProvider.OPENAI,
            model="gpt-4",
            usage={"total_tokens": 100},
            finish_reason="stop",
            created_at=created_at,
            generation_time=1.5,
            context=context,
            options=options,
            metadata={"test": "data"}
        )
        
        assert result.id == result_id
        assert result.content == "生成的内容"
        assert result.provider == AIProvider.OPENAI
        assert result.model == "gpt-4"
        assert result.usage == {"total_tokens": 100}
        assert result.finish_reason == "stop"
        assert result.created_at == created_at
        assert result.generation_time == 1.5
        assert result.context == context
        assert result.options == options
        assert result.metadata == {"test": "data"}


class TestStreamChunk:
    """测试流式响应块"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        chunk_id = uuid4()
        chunk = StreamChunk(
            id=chunk_id,
            content="累积内容",
            delta="新增内容"
        )
        
        assert chunk.id == chunk_id
        assert chunk.content == "累积内容"
        assert chunk.delta == "新增内容"
        assert chunk.finish_reason is None
        assert chunk.usage is None
    
    def test_init_full(self):
        """测试完整初始化"""
        chunk_id = uuid4()
        chunk = StreamChunk(
            id=chunk_id,
            content="累积内容",
            delta="新增内容",
            finish_reason="stop",
            usage={"total_tokens": 50}
        )
        
        assert chunk.id == chunk_id
        assert chunk.content == "累积内容"
        assert chunk.delta == "新增内容"
        assert chunk.finish_reason == "stop"
        assert chunk.usage == {"total_tokens": 50}


class TestPromptTemplate:
    """测试提示词模板"""
    
    def test_init(self):
        """测试初始化"""
        template = PromptTemplate(
            "请为{name}创建描述，年龄{age}",
            ["name", "age"]
        )
        
        assert template.template == "请为{name}创建描述，年龄{age}"
        assert template.variables == ["name", "age"]
    
    def test_format(self):
        """测试格式化"""
        template = PromptTemplate(
            "请为{name}创建描述，年龄{age}",
            ["name", "age"]
        )
        
        result = template.format(name="张三", age=25)
        assert result == "请为张三创建描述，年龄25"
    
    def test_validate_variables_success(self):
        """测试变量验证成功"""
        template = PromptTemplate(
            "请为{name}创建描述，年龄{age}",
            ["name", "age"]
        )
        
        assert template.validate_variables(name="张三", age=25) is True
        assert template.validate_variables(name="张三", age=25, extra="额外") is True
    
    def test_validate_variables_failure(self):
        """测试变量验证失败"""
        template = PromptTemplate(
            "请为{name}创建描述，年龄{age}",
            ["name", "age"]
        )
        
        assert template.validate_variables(name="张三") is False
        assert template.validate_variables(age=25) is False
        assert template.validate_variables() is False


class TestPromptManager:
    """测试提示词管理器"""
    
    def test_init(self):
        """测试初始化"""
        manager = PromptManager()
        
        # 检查默认模板是否加载
        assert manager.get_template(GenerationType.CHARACTER_DESCRIPTION) is not None
        assert manager.get_template(GenerationType.SCENE_DESCRIPTION) is not None
        assert manager.get_template(GenerationType.PLOT_DEVELOPMENT) is not None
        assert manager.get_template(GenerationType.DIALOGUE) is not None
        assert manager.get_template(GenerationType.CONTENT_OPTIMIZATION) is not None
    
    def test_get_template(self):
        """测试获取模板"""
        manager = PromptManager()
        
        template = manager.get_template(GenerationType.CHARACTER_DESCRIPTION)
        assert template is not None
        assert isinstance(template, PromptTemplate)
        
        # 测试不存在的模板
        assert manager.get_template(GenerationType.STORY_OUTLINE) is None
    
    def test_add_template(self):
        """测试添加模板"""
        manager = PromptManager()
        
        new_template = PromptTemplate("新模板 {param}", ["param"])
        manager.add_template(GenerationType.STORY_OUTLINE, new_template)
        
        retrieved = manager.get_template(GenerationType.STORY_OUTLINE)
        assert retrieved == new_template
    
    def test_format_prompt_success(self):
        """测试格式化提示词成功"""
        manager = PromptManager()
        
        result = manager.format_prompt(
            GenerationType.CHARACTER_DESCRIPTION,
            name="张三",
            gender="男",
            age=25,
            style="现代都市"
        )
        
        assert "张三" in result
        assert "男" in result
        assert "25" in result
        assert "现代都市" in result
    
    def test_format_prompt_missing_template(self):
        """测试格式化不存在的模板"""
        manager = PromptManager()
        
        with pytest.raises(ValueError, match="未找到生成类型"):
            manager.format_prompt(GenerationType.STORY_OUTLINE, param="value")
    
    def test_format_prompt_missing_variables(self):
        """测试格式化缺少变量"""
        manager = PromptManager()
        
        with pytest.raises(ValueError, match="模板变量不完整"):
            manager.format_prompt(
                GenerationType.CHARACTER_DESCRIPTION,
                name="张三"  # 缺少其他必需变量
            )


class TestAIServiceErrors:
    """测试AI服务错误"""
    
    def test_ai_service_error(self):
        """测试基础AI服务错误"""
        error = AIServiceError("测试错误", AIProvider.OPENAI, "test_code")
        
        assert str(error) == "测试错误"
        assert error.provider == AIProvider.OPENAI
        assert error.error_code == "test_code"
    
    def test_authentication_error(self):
        """测试认证错误"""
        error = AuthenticationError("认证失败", AIProvider.ANTHROPIC)
        
        assert str(error) == "认证失败"
        assert error.provider == AIProvider.ANTHROPIC
        assert isinstance(error, AIServiceError)
    
    def test_rate_limit_error(self):
        """测试速率限制错误"""
        error = RateLimitError("速率限制", AIProvider.DEEPSEEK, retry_after=60)
        
        assert str(error) == "速率限制"
        assert error.provider == AIProvider.DEEPSEEK
        assert error.retry_after == 60
        assert isinstance(error, AIServiceError)
