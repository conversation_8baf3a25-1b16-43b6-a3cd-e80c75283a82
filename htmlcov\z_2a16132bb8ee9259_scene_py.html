<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\core\models\scene.py: 89%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\core\models\scene.py</b>:
            <span class="pc_cov">89%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">166 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">147<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">19<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_2a16132bb8ee9259_project_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_2a16132bb8ee9259_story_element_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""&#22330;&#26223;&#27169;&#22411;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#23450;&#20041;&#25925;&#20107;&#22330;&#26223;&#30456;&#20851;&#30340;&#25968;&#25454;&#27169;&#22411;&#65292;&#21253;&#25324;&#65306;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">- SceneLocation: &#22330;&#26223;&#20301;&#32622;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">- SceneTime: &#22330;&#26223;&#26102;&#38388;&#20449;&#24687;  </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="str">- SceneEnvironment: &#22330;&#26223;&#29615;&#22659;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="str">- SceneAtmosphere: &#22330;&#26223;&#27675;&#22260;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="str">- Scene: &#20027;&#35201;&#22330;&#26223;&#27169;&#22411;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="str">- SceneValidator: &#22330;&#26223;&#39564;&#35777;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">time</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">Tuple</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">from</span> <span class="nam">uuid</span> <span class="key">import</span> <span class="nam">UUID</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">pydantic</span> <span class="key">import</span> <span class="nam">Field</span><span class="op">,</span> <span class="nam">validator</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="nam">BaseModel</span><span class="op">,</span> <span class="nam">ValidationResult</span><span class="op">,</span> <span class="nam">ModelValidator</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">story_element</span> <span class="key">import</span> <span class="nam">StoryElement</span><span class="op">,</span> <span class="nam">StoryElementValidator</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">enums</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">ElementType</span><span class="op">,</span> <span class="nam">LocationType</span><span class="op">,</span> <span class="nam">SceneFunction</span><span class="op">,</span> <span class="nam">ImportanceLevel</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneLocation</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#20301;&#32622;&#20449;&#24687;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="com"># &#22522;&#30784;&#20301;&#32622;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">name</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20301;&#32622;&#21517;&#31216;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">location_type</span><span class="op">:</span> <span class="nam">LocationType</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="nam">LocationType</span><span class="op">.</span><span class="nam">INDOOR</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20301;&#32622;&#31867;&#22411;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">address</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#35814;&#32454;&#22320;&#22336;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">coordinates</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">Tuple</span><span class="op">[</span><span class="nam">float</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#22352;&#26631;(&#32463;&#24230;,&#32428;&#24230;)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="com"># &#23618;&#32423;&#20851;&#31995;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="nam">parent_location</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">UUID</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#29238;&#32423;&#20301;&#32622;ID"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">    <span class="nam">sub_locations</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">UUID</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#23376;&#20301;&#32622;ID&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="com"># &#20301;&#32622;&#25551;&#36848;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="nam">size_description</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#22823;&#23567;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">layout_description</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#24067;&#23616;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="nam">accessibility</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#21487;&#36798;&#24615;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="op">@</span><span class="nam">validator</span><span class="op">(</span><span class="str">'name'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_name</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="key">if</span> <span class="nam">v</span> <span class="key">and</span> <span class="nam">len</span><span class="op">(</span><span class="nam">v</span><span class="op">)</span> <span class="op">></span> <span class="num">100</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"&#20301;&#32622;&#21517;&#31216;&#19981;&#33021;&#36229;&#36807;100&#20010;&#23383;&#31526;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneTime</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#26102;&#38388;&#20449;&#24687;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">    <span class="com"># &#26102;&#38388;&#35774;&#23450;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="nam">time_period</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26102;&#38388;&#27573;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">    <span class="nam">season</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#23395;&#33410;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="nam">time_of_day</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#19968;&#22825;&#20013;&#30340;&#26102;&#38388;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">    <span class="nam">specific_time</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">time</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20855;&#20307;&#26102;&#38388;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="com"># &#26102;&#38388;&#29305;&#24449;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">    <span class="nam">duration_description</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#25345;&#32493;&#26102;&#38388;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">    <span class="nam">time_flow</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">"normal"</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26102;&#38388;&#27969;&#36893;&#24863; (slow/normal/fast)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">    <span class="nam">temporal_mood</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26102;&#38388;&#27675;&#22260;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneEnvironment</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#29615;&#22659;&#20449;&#24687;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="com"># &#22825;&#27668;&#29615;&#22659;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">    <span class="nam">weather</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#22825;&#27668;&#29366;&#20917;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="nam">temperature</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#28201;&#24230;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">    <span class="nam">humidity</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#28287;&#24230;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="com"># &#20809;&#32447;&#29615;&#22659;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">    <span class="nam">lighting</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20809;&#32447;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">    <span class="nam">light_source</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20809;&#28304;&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">    <span class="nam">shadows</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#38452;&#24433;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="com"># &#24863;&#23448;&#29615;&#22659;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">    <span class="nam">sounds</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#22768;&#38899;&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="nam">smells</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#27668;&#21619;&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">textures</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#36136;&#24863;&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">colors</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#39068;&#33394;&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="com"># &#29615;&#22659;&#29305;&#24449;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">    <span class="nam">air_quality</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#31354;&#27668;&#36136;&#37327;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="nam">visibility</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">None</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#33021;&#35265;&#24230;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="nam">environmental_hazards</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#29615;&#22659;&#21361;&#38505;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneAtmosphere</span><span class="op">(</span><span class="nam">BaseModel</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#27675;&#22260;&#20449;&#24687;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="com"># &#24773;&#32490;&#27675;&#22260;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">    <span class="nam">emotional_tone</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#24773;&#32490;&#22522;&#35843;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="nam">mood_descriptors</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#27675;&#22260;&#25551;&#36848;&#35789;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">    <span class="nam">tension_level</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0.5</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#32039;&#24352;&#24230; (0.0-1.0)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="com"># &#27675;&#22260;&#20803;&#32032;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="nam">symbolic_elements</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#35937;&#24449;&#20803;&#32032;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="nam">metaphorical_descriptions</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#38544;&#21947;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">    <span class="com"># &#24863;&#23448;&#21360;&#35937;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="nam">overall_impression</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#25972;&#20307;&#21360;&#35937;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="nam">dominant_sensory_element</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20027;&#23548;&#24863;&#23448;&#20803;&#32032;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="op">@</span><span class="nam">validator</span><span class="op">(</span><span class="str">'tension_level'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_tension_level</span><span class="op">(</span><span class="nam">cls</span><span class="op">,</span> <span class="nam">v</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">        <span class="key">if</span> <span class="nam">v</span> <span class="op">&lt;</span> <span class="num">0.0</span> <span class="key">or</span> <span class="nam">v</span> <span class="op">></span> <span class="num">1.0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"&#32039;&#24352;&#24230;&#24517;&#39035;&#22312;0.0-1.0&#20043;&#38388;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">        <span class="key">return</span> <span class="nam">v</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t"><span class="key">class</span> <span class="nam">Scene</span><span class="op">(</span><span class="nam">StoryElement</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="str">"""&#25925;&#20107;&#22330;&#26223;&#27169;&#22411;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">    <span class="com"># &#20803;&#32032;&#31867;&#22411;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">    <span class="nam">element_type</span><span class="op">:</span> <span class="nam">ElementType</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="nam">ElementType</span><span class="op">.</span><span class="nam">SCENE</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20803;&#32032;&#31867;&#22411;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="com"># &#20301;&#32622;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">    <span class="nam">location</span><span class="op">:</span> <span class="nam">SceneLocation</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">SceneLocation</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20301;&#32622;&#20449;&#24687;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="com"># &#26102;&#38388;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="nam">time_setting</span><span class="op">:</span> <span class="nam">SceneTime</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">SceneTime</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26102;&#38388;&#35774;&#23450;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">    <span class="com"># &#29615;&#22659;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">    <span class="nam">environment</span><span class="op">:</span> <span class="nam">SceneEnvironment</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">SceneEnvironment</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#29615;&#22659;&#20449;&#24687;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="com"># &#27675;&#22260;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">    <span class="nam">atmosphere</span><span class="op">:</span> <span class="nam">SceneAtmosphere</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">SceneAtmosphere</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#27675;&#22260;&#20449;&#24687;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">    <span class="com"># &#21151;&#33021;&#23646;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">    <span class="nam">scene_function</span><span class="op">:</span> <span class="nam">SceneFunction</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="nam">SceneFunction</span><span class="op">.</span><span class="nam">SETTING</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#22330;&#26223;&#21151;&#33021;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">    <span class="nam">recurring</span><span class="op">:</span> <span class="nam">bool</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#26159;&#21542;&#37325;&#22797;&#20351;&#29992;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">    <span class="com"># &#20851;&#32852;&#20803;&#32032;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">    <span class="nam">typical_characters</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">UUID</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#24120;&#35265;&#35282;&#33394;ID&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="nam">typical_events</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">UUID</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20856;&#22411;&#20107;&#20214;ID&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">    <span class="com"># &#20351;&#29992;&#32479;&#35745;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">    <span class="nam">usage_count</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20351;&#29992;&#27425;&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="nam">chapters_used</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">UUID</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20351;&#29992;&#31456;&#33410;ID&#21015;&#34920;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">    <span class="com"># &#22330;&#26223;&#35814;&#32454;&#25551;&#36848;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">    <span class="nam">detailed_description</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">""</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#35814;&#32454;&#25551;&#36848;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="nam">key_features</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#20851;&#38190;&#29305;&#24449;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">    <span class="nam">hidden_elements</span><span class="op">:</span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="nam">Field</span><span class="op">(</span><span class="nam">default_factory</span><span class="op">=</span><span class="nam">list</span><span class="op">,</span> <span class="nam">description</span><span class="op">=</span><span class="str">"&#38544;&#34255;&#20803;&#32032;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="op">**</span><span class="nam">data</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="str">"""&#21021;&#22987;&#21270;&#22330;&#26223;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="com"># &#30830;&#20445;name&#23383;&#27573;&#26377;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="key">if</span> <span class="str">'name'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">data</span> <span class="key">and</span> <span class="str">'location'</span> <span class="key">in</span> <span class="nam">data</span> <span class="key">and</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">data</span><span class="op">[</span><span class="str">'location'</span><span class="op">]</span><span class="op">,</span> <span class="str">'name'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">            <span class="nam">data</span><span class="op">[</span><span class="str">'name'</span><span class="op">]</span> <span class="op">=</span> <span class="nam">data</span><span class="op">[</span><span class="str">'location'</span><span class="op">]</span><span class="op">.</span><span class="nam">name</span> <span class="key">or</span> <span class="str">"&#26410;&#21629;&#21517;&#22330;&#26223;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">        <span class="key">elif</span> <span class="str">'name'</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">data</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="nam">data</span><span class="op">[</span><span class="str">'name'</span><span class="op">]</span> <span class="op">=</span> <span class="str">"&#26410;&#21629;&#21517;&#22330;&#26223;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="nam">super</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">__init__</span><span class="op">(</span><span class="op">**</span><span class="nam">data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">    <span class="key">def</span> <span class="nam">add_character</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">character_id</span><span class="op">:</span> <span class="nam">UUID</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="str">"""&#28155;&#21152;&#24120;&#35265;&#35282;&#33394;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">        <span class="key">if</span> <span class="nam">character_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">character_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">update_timestamp</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">    <span class="key">def</span> <span class="nam">remove_character</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">character_id</span><span class="op">:</span> <span class="nam">UUID</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">        <span class="str">"""&#31227;&#38500;&#24120;&#35265;&#35282;&#33394;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">        <span class="key">if</span> <span class="nam">character_id</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">.</span><span class="nam">remove</span><span class="op">(</span><span class="nam">character_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">update_timestamp</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="key">def</span> <span class="nam">add_event</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event_id</span><span class="op">:</span> <span class="nam">UUID</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="str">"""&#28155;&#21152;&#20856;&#22411;&#20107;&#20214;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="key">if</span> <span class="nam">event_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">event_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">update_timestamp</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">    <span class="key">def</span> <span class="nam">remove_event</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event_id</span><span class="op">:</span> <span class="nam">UUID</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">        <span class="str">"""&#31227;&#38500;&#20856;&#22411;&#20107;&#20214;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="key">if</span> <span class="nam">event_id</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">.</span><span class="nam">remove</span><span class="op">(</span><span class="nam">event_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">update_timestamp</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">    <span class="key">def</span> <span class="nam">use_in_chapter</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">chapter_id</span><span class="op">:</span> <span class="nam">UUID</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="str">"""&#22312;&#31456;&#33410;&#20013;&#20351;&#29992;&#22330;&#26223;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="key">if</span> <span class="nam">chapter_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">chapters_used</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">chapters_used</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">chapter_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">usage_count</span> <span class="op">+=</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">update_timestamp</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_atmosphere_score</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">        <span class="str">"""&#33719;&#21462;&#27675;&#22260;&#35780;&#20998;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">        <span class="nam">score</span> <span class="op">=</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="com"># &#22522;&#20110;&#27675;&#22260;&#25551;&#36848;&#30340;&#35780;&#20998;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">emotional_tone</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">            <span class="nam">score</span> <span class="op">+=</span> <span class="num">0.2</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">mood_descriptors</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">            <span class="nam">score</span> <span class="op">+=</span> <span class="num">0.2</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">symbolic_elements</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="nam">score</span> <span class="op">+=</span> <span class="num">0.2</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">overall_impression</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="nam">score</span> <span class="op">+=</span> <span class="num">0.2</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">        <span class="com"># &#22522;&#20110;&#29615;&#22659;&#20016;&#23500;&#24230;&#30340;&#35780;&#20998;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">        <span class="nam">env_elements</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">sounds</span><span class="op">)</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">            <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">smells</span><span class="op">)</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">            <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">colors</span><span class="op">)</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">            <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">textures</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">        <span class="key">if</span> <span class="nam">env_elements</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">            <span class="nam">score</span> <span class="op">+=</span> <span class="nam">min</span><span class="op">(</span><span class="num">0.2</span><span class="op">,</span> <span class="nam">env_elements</span> <span class="op">*</span> <span class="num">0.05</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">        <span class="key">return</span> <span class="nam">min</span><span class="op">(</span><span class="num">1.0</span><span class="op">,</span> <span class="nam">score</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate_model</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">ValidationResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;&#27169;&#22411;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">ValidationResult</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#22522;&#30784;&#23383;&#27573;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">name</span> <span class="key">or</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">.</span><span class="nam">strip</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#21517;&#31216;&#19981;&#33021;&#20026;&#31354;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">name</span><span class="op">)</span> <span class="op">></span> <span class="num">100</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#21517;&#31216;&#19981;&#33021;&#36229;&#36807;100&#20010;&#23383;&#31526;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">description</span><span class="op">)</span> <span class="op">></span> <span class="num">1000</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#25551;&#36848;&#19981;&#33021;&#36229;&#36807;1000&#20010;&#23383;&#31526;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">detailed_description</span><span class="op">)</span> <span class="op">></span> <span class="num">5000</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#35814;&#32454;&#25551;&#36848;&#19981;&#33021;&#36229;&#36807;5000&#20010;&#23383;&#31526;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20301;&#32622;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">location</span><span class="op">.</span><span class="nam">name</span> <span class="key">and</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">location</span><span class="op">.</span><span class="nam">name</span><span class="op">)</span> <span class="op">></span> <span class="num">100</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#20301;&#32622;&#21517;&#31216;&#19981;&#33021;&#36229;&#36807;100&#20010;&#23383;&#31526;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#27675;&#22260;&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">tension_level</span> <span class="op">&lt;</span> <span class="num">0.0</span> <span class="key">or</span> <span class="nam">self</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">tension_level</span> <span class="op">></span> <span class="num">1.0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#32039;&#24352;&#24230;&#24517;&#39035;&#22312;0.0-1.0&#20043;&#38388;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">        <span class="com"># &#39564;&#35777;&#20351;&#29992;&#32479;&#35745;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">        <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">usage_count</span> <span class="op">&lt;</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#20351;&#29992;&#27425;&#25968;&#19981;&#33021;&#20026;&#36127;&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">        <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">chapters_used</span><span class="op">)</span> <span class="op">></span> <span class="nam">self</span><span class="op">.</span><span class="nam">usage_count</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_error</span><span class="op">(</span><span class="str">"&#20351;&#29992;&#31456;&#33410;&#25968;&#19981;&#33021;&#36229;&#36807;&#20351;&#29992;&#27425;&#25968;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="key">return</span> <span class="nam">result</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t"><span class="key">class</span> <span class="nam">SceneValidator</span><span class="op">(</span><span class="nam">ModelValidator</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">    <span class="str">"""&#22330;&#26223;&#39564;&#35777;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">    <span class="key">def</span> <span class="nam">validate</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">Scene</span><span class="op">)</span> <span class="op">-></span> <span class="nam">ValidationResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">ValidationResult</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">        <span class="com"># &#22522;&#30784;&#39564;&#35777;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">        <span class="nam">base_result</span> <span class="op">=</span> <span class="nam">model</span><span class="op">.</span><span class="nam">validate_model</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">        <span class="nam">result</span><span class="op">.</span><span class="nam">merge</span><span class="op">(</span><span class="nam">base_result</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">        <span class="com"># &#19994;&#21153;&#35268;&#21017;&#39564;&#35777;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_scene_completeness</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">result</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_scene_consistency</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">result</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_validate_scene_relationships</span><span class="op">(</span><span class="nam">model</span><span class="op">,</span> <span class="nam">result</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">        <span class="key">return</span> <span class="nam">result</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_scene_completeness</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">Scene</span><span class="op">,</span> <span class="nam">result</span><span class="op">:</span> <span class="nam">ValidationResult</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;&#23436;&#25972;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#20851;&#38190;&#20449;&#24687;&#26159;&#21542;&#23436;&#25972;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">location</span><span class="op">.</span><span class="nam">name</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">name</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#32570;&#23569;&#20301;&#32622;&#21517;&#31216;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">description</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">detailed_description</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#32570;&#23569;&#25551;&#36848;&#20449;&#24687;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#27675;&#22260;&#35774;&#23450;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">emotional_tone</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">            <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">mood_descriptors</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">            <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">atmosphere</span><span class="op">.</span><span class="nam">overall_impression</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#32570;&#23569;&#27675;&#22260;&#35774;&#23450;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_scene_consistency</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">Scene</span><span class="op">,</span> <span class="nam">result</span><span class="op">:</span> <span class="nam">ValidationResult</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;&#19968;&#33268;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#26102;&#38388;&#21644;&#29615;&#22659;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">model</span><span class="op">.</span><span class="nam">time_setting</span><span class="op">.</span><span class="nam">season</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">            <span class="nam">model</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">weather</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">            <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_is_weather_season_consistent</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">                <span class="nam">model</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">weather</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">                <span class="nam">model</span><span class="op">.</span><span class="nam">time_setting</span><span class="op">.</span><span class="nam">season</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">            <span class="op">)</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#22825;&#27668;&#19982;&#23395;&#33410;&#35774;&#23450;&#21487;&#33021;&#19981;&#19968;&#33268;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#20301;&#32622;&#31867;&#22411;&#21644;&#29615;&#22659;&#30340;&#19968;&#33268;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">        <span class="key">if</span> <span class="op">(</span><span class="nam">model</span><span class="op">.</span><span class="nam">location</span><span class="op">.</span><span class="nam">location_type</span> <span class="op">==</span> <span class="nam">LocationType</span><span class="op">.</span><span class="nam">INDOOR</span> <span class="key">and</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">            <span class="nam">model</span><span class="op">.</span><span class="nam">environment</span><span class="op">.</span><span class="nam">weather</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_warning</span><span class="op">(</span><span class="str">"&#23460;&#20869;&#22330;&#26223;&#36890;&#24120;&#19981;&#38656;&#35201;&#22825;&#27668;&#35774;&#23450;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">    <span class="key">def</span> <span class="nam">_validate_scene_relationships</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">Scene</span><span class="op">,</span> <span class="nam">result</span><span class="op">:</span> <span class="nam">ValidationResult</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">        <span class="str">"""&#39564;&#35777;&#22330;&#26223;&#20851;&#31995;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">        <span class="com"># &#26816;&#26597;&#35282;&#33394;&#21644;&#20107;&#20214;&#30340;&#20851;&#32852;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">        <span class="key">if</span> <span class="nam">model</span><span class="op">.</span><span class="nam">typical_characters</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">typical_events</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_info</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#26377;&#24120;&#35265;&#35282;&#33394;&#20294;&#27809;&#26377;&#20856;&#22411;&#20107;&#20214;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">        <span class="key">if</span> <span class="nam">model</span><span class="op">.</span><span class="nam">typical_events</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">model</span><span class="op">.</span><span class="nam">typical_characters</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">            <span class="nam">result</span><span class="op">.</span><span class="nam">add_info</span><span class="op">(</span><span class="str">"&#22330;&#26223;&#26377;&#20856;&#22411;&#20107;&#20214;&#20294;&#27809;&#26377;&#24120;&#35265;&#35282;&#33394;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">    <span class="key">def</span> <span class="nam">_is_weather_season_consistent</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">weather</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">season</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">        <span class="str">"""&#26816;&#26597;&#22825;&#27668;&#21644;&#23395;&#33410;&#26159;&#21542;&#19968;&#33268;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">        <span class="com"># &#31616;&#21333;&#30340;&#19968;&#33268;&#24615;&#26816;&#26597;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">        <span class="nam">weather_lower</span> <span class="op">=</span> <span class="nam">weather</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">        <span class="nam">season_lower</span> <span class="op">=</span> <span class="nam">season</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">        <span class="com"># &#22799;&#23395;&#19981;&#24212;&#35813;&#26377;&#38634;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">        <span class="key">if</span> <span class="str">"&#22799;"</span> <span class="key">in</span> <span class="nam">season_lower</span> <span class="key">and</span> <span class="op">(</span><span class="str">"&#38634;"</span> <span class="key">in</span> <span class="nam">weather_lower</span> <span class="key">or</span> <span class="str">"&#20912;"</span> <span class="key">in</span> <span class="nam">weather_lower</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">            <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">        <span class="com"># &#20908;&#23395;&#19981;&#24212;&#35813;&#22826;&#28909;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">        <span class="key">if</span> <span class="str">"&#20908;"</span> <span class="key">in</span> <span class="nam">season_lower</span> <span class="key">and</span> <span class="op">(</span><span class="str">"&#28814;&#28909;"</span> <span class="key">in</span> <span class="nam">weather_lower</span> <span class="key">or</span> <span class="str">"&#37239;&#28909;"</span> <span class="key">in</span> <span class="nam">weather_lower</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">            <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">        <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_2a16132bb8ee9259_project_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_2a16132bb8ee9259_story_element_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</footer>
</body>
</html>
