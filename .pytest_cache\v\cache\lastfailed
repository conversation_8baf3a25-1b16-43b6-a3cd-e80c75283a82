{"tests/unit/test_models/test_base.py::TestBaseModel::test_to_json": true, "tests/unit/test_models/test_base.py::TestBaseModel::test_calculate_checksum": true, "tests/unit/test_models/test_project.py::TestWritingProject::test_init_full": true, "tests/unit/test_models/test_project.py::TestProjectValidator::test_validate_invalid_project": true, "tests/unit/test_validation/test_rules.py::TestURLValidationRule::test_validate_invalid_urls": true, "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_completed_project_with_100_percent": true, "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_completed_project_with_low_percentage": true, "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_not_started_project_with_progress": true, "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_ai_configuration": true, "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_critical_character_completeness": true, "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_relationship_consistency": true, "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_relationship_timeline": true, "tests/unit/test_validation/test_business_rules.py::TestSceneBusinessRules::test_validate_critical_scene_completeness": true, "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_critical_event_completeness": true, "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_participants": true, "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_impact_consistency": true, "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_validate_api_key_failure": true, "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_with_retry": true, "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_stream": true, "tests/unit/test_storage/test_version_control.py": true, "tests/unit/test_ui/test_main_window.py::TestWelcomeWidget": true, "tests/unit/test_ui/test_main_window.py::TestMainWindow": true, "tests/unit/test_ui/test_main_window.py::TestMainWindowIntegration": true, "tests/unit/test_validation/test_relationship_validators.py": true, "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_create_project_structure": true, "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_character": true, "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_scene": true, "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_event": true, "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_delete_character": true}