"""
项目对话框模块

实现新建项目和项目设置对话框
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QComboBox, QPushButton,
    QLabel, QGroupBox, QRadioButton, QButtonGroup,
    QMessageBox, QFileDialog, QCheckBox, QSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from ..core.models.enums import ProjectGenre


class ProjectDialog(QDialog):
    """新建项目对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.project_data = {}
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("新建项目")
        self.setModal(True)
        self.setMinimumSize(600, 500)
        self.resize(700, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 项目信息组
        info_group = self.create_project_info_group()
        main_layout.addWidget(info_group)
        
        # 创作模板组
        template_group = self.create_template_group()
        main_layout.addWidget(template_group)
        
        # 高级设置组
        advanced_group = self.create_advanced_group()
        main_layout.addWidget(advanced_group)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        main_layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
        
    def create_project_info_group(self) -> QGroupBox:
        """创建项目信息组"""
        group = QGroupBox("项目信息")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # 项目名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入项目名称")
        layout.addRow("项目名称 *:", self.name_edit)
        
        # 作者姓名
        self.author_edit = QLineEdit()
        self.author_edit.setPlaceholderText("请输入作者姓名")
        layout.addRow("作者姓名 *:", self.author_edit)
        
        # 项目类型
        self.genre_combo = QComboBox()
        self.populate_genre_combo()
        layout.addRow("项目类型:", self.genre_combo)
        
        # 项目简介
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入项目简介（可选）")
        self.description_edit.setMaximumHeight(100)
        layout.addRow("项目简介:", self.description_edit)
        
        return group
        
    def create_template_group(self) -> QGroupBox:
        """创建创作模板组"""
        group = QGroupBox("创作模板")
        layout = QVBoxLayout(group)
        
        # 模板选择按钮组
        self.template_group = QButtonGroup(self)
        
        # 空白项目
        blank_radio = QRadioButton("空白项目")
        blank_radio.setChecked(True)
        self.template_group.addButton(blank_radio, 0)
        layout.addWidget(blank_radio)
        
        # 预设模板
        templates = [
            ("玄幻小说模板", "包含修仙体系、境界设定等元素"),
            ("都市小说模板", "包含现代都市背景、职场商战等元素"),
            ("科幻小说模板", "包含未来科技、星际文明等元素"),
            ("历史小说模板", "包含历史背景、古代文化等元素"),
            ("悬疑小说模板", "包含推理悬疑、犯罪侦探等元素")
        ]
        
        for i, (name, desc) in enumerate(templates, 1):
            radio = QRadioButton(name)
            radio.setToolTip(desc)
            self.template_group.addButton(radio, i)
            layout.addWidget(radio)
            
        # 自定义模板
        custom_layout = QHBoxLayout()
        custom_radio = QRadioButton("自定义模板")
        self.template_group.addButton(custom_radio, len(templates) + 1)
        
        self.custom_template_btn = QPushButton("选择文件...")
        self.custom_template_btn.setEnabled(False)
        
        custom_layout.addWidget(custom_radio)
        custom_layout.addWidget(self.custom_template_btn)
        custom_layout.addStretch()
        
        layout.addLayout(custom_layout)
        
        return group
        
    def create_advanced_group(self) -> QGroupBox:
        """创建高级设置组"""
        group = QGroupBox("高级设置")
        layout = QFormLayout(group)
        
        # 目标字数
        self.target_words = QSpinBox()
        self.target_words.setRange(1000, 10000000)
        self.target_words.setValue(100000)
        self.target_words.setSuffix(" 字")
        layout.addRow("目标字数:", self.target_words)
        
        # 章节数量
        self.chapter_count = QSpinBox()
        self.chapter_count.setRange(1, 1000)
        self.chapter_count.setValue(30)
        self.chapter_count.setSuffix(" 章")
        layout.addRow("预计章节:", self.chapter_count)
        
        # 自动保存
        self.auto_save_check = QCheckBox("启用自动保存")
        self.auto_save_check.setChecked(True)
        layout.addRow("", self.auto_save_check)
        
        # AI辅助
        self.ai_assist_check = QCheckBox("启用AI辅助创作")
        self.ai_assist_check.setChecked(True)
        layout.addRow("", self.ai_assist_check)
        
        return group
        
    def create_button_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumSize(100, 35)
        
        # 创建按钮
        self.create_btn = QPushButton("创建项目")
        self.create_btn.setMinimumSize(100, 35)
        self.create_btn.setDefault(True)
        
        layout.addWidget(self.cancel_btn)
        layout.addWidget(self.create_btn)
        
        return layout
        
    def populate_genre_combo(self):
        """填充项目类型下拉框"""
        for genre in ProjectGenre:
            self.genre_combo.addItem(genre.value, genre)
            
    def setup_connections(self):
        """设置信号连接"""
        self.cancel_btn.clicked.connect(self.reject)
        self.create_btn.clicked.connect(self.validate_and_accept)
        self.name_edit.textChanged.connect(self.validate_form)
        self.author_edit.textChanged.connect(self.validate_form)
        
        # 自定义模板按钮
        self.template_group.buttonClicked.connect(self.on_template_changed)
        self.custom_template_btn.clicked.connect(self.select_custom_template)
        
        # 初始验证
        self.validate_form()
        
    def on_template_changed(self, button):
        """模板选择改变"""
        template_id = self.template_group.id(button)
        is_custom = template_id == len(self.template_group.buttons()) - 1
        self.custom_template_btn.setEnabled(is_custom)
        
    def select_custom_template(self):
        """选择自定义模板文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模板文件",
            "",
            "模板文件 (*.json *.xml);;所有文件 (*)"
        )
        
        if file_path:
            self.custom_template_btn.setText(f"已选择: {file_path.split('/')[-1]}")
            self.custom_template_path = file_path
        
    def validate_form(self):
        """验证表单"""
        name_valid = bool(self.name_edit.text().strip())
        author_valid = bool(self.author_edit.text().strip())
        
        self.create_btn.setEnabled(name_valid and author_valid)
        
    def validate_and_accept(self):
        """验证并接受对话框"""
        # 验证必填字段
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "请输入项目名称")
            self.name_edit.setFocus()
            return
            
        if not self.author_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "请输入作者姓名")
            self.author_edit.setFocus()
            return
            
        # 收集项目数据
        self.collect_project_data()
        
        # 接受对话框
        self.accept()
        
    def collect_project_data(self):
        """收集项目数据"""
        self.project_data = {
            'name': self.name_edit.text().strip(),
            'author': self.author_edit.text().strip(),
            'genre': self.genre_combo.currentData(),
            'description': self.description_edit.toPlainText().strip(),
            'target_words': self.target_words.value(),
            'chapter_count': self.chapter_count.value(),
            'auto_save': self.auto_save_check.isChecked(),
            'ai_assist': self.ai_assist_check.isChecked(),
            'template_id': self.template_group.checkedId(),
            'custom_template_path': getattr(self, 'custom_template_path', None)
        }
        
    def get_project_data(self) -> Dict[str, Any]:
        """获取项目数据"""
        return self.project_data
        
    def apply_styles(self):
        """应用样式表"""
        style = """
        QDialog {
            background: #f8f9fa;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #495057;
        }
        
        QLineEdit, QTextEdit, QComboBox, QSpinBox {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
            background: white;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
            border-color: #007bff;
            outline: none;
        }
        
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #007bff, stop:1 #0056b3);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0056b3, stop:1 #004085);
        }
        
        QPushButton:pressed {
            background: #004085;
        }
        
        QPushButton:disabled {
            background: #6c757d;
            color: #adb5bd;
        }
        
        QRadioButton {
            spacing: 8px;
            margin: 5px;
        }
        
        QRadioButton::indicator {
            width: 16px;
            height: 16px;
        }
        
        QRadioButton::indicator:unchecked {
            border: 2px solid #6c757d;
            border-radius: 8px;
            background: white;
        }
        
        QRadioButton::indicator:checked {
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #007bff;
        }
        
        QCheckBox {
            spacing: 8px;
            margin: 5px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 2px solid #6c757d;
            border-radius: 3px;
            background: white;
        }
        
        QCheckBox::indicator:checked {
            border: 2px solid #007bff;
            border-radius: 3px;
            background: #007bff;
            image: url(:/icons/check.png);
        }
        """
        self.setStyleSheet(style)


class ProjectSettingsDialog(QDialog):
    """项目设置对话框"""
    
    def __init__(self, project, parent=None):
        super().__init__(parent)
        self.project = project
        self.setup_ui()
        self.load_project_settings()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("项目设置")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QFormLayout(info_group)
        
        self.name_edit = QLineEdit()
        self.author_edit = QLineEdit()
        self.genre_combo = QComboBox()
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        
        info_layout.addRow("项目名称:", self.name_edit)
        info_layout.addRow("作者:", self.author_edit)
        info_layout.addRow("类型:", self.genre_combo)
        info_layout.addRow("简介:", self.description_edit)
        
        main_layout.addWidget(info_group)
        
        # 写作设置
        writing_group = QGroupBox("写作设置")
        writing_layout = QFormLayout(writing_group)
        
        self.target_words = QSpinBox()
        self.target_words.setRange(1000, 10000000)
        self.target_words.setSuffix(" 字")
        
        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(30, 3600)
        self.auto_save_interval.setSuffix(" 秒")
        
        writing_layout.addRow("目标字数:", self.target_words)
        writing_layout.addRow("自动保存间隔:", self.auto_save_interval)
        
        main_layout.addWidget(writing_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        save_btn = QPushButton("保存")
        
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        cancel_btn.clicked.connect(self.reject)
        save_btn.clicked.connect(self.save_settings)
        
    def load_project_settings(self):
        """加载项目设置"""
        if self.project:
            self.name_edit.setText(self.project.name)
            self.author_edit.setText(self.project.author)
            self.description_edit.setPlainText(self.project.description or "")
            
    def save_settings(self):
        """保存设置"""
        if self.project:
            self.project.name = self.name_edit.text()
            self.project.author = self.author_edit.text()
            self.project.description = self.description_edit.toPlainText()
            
        self.accept()
