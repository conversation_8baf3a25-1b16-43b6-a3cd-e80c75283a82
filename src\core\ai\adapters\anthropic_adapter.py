"""
Anthropic服务适配器

实现Anthropic Claude API的具体适配逻辑，包括消息生成、流式响应、错误处理等功能。
"""

import asyncio
import time
from typing import AsyncGenerator, Dict, List, Optional
from uuid import uuid4
from datetime import datetime

try:
    from anthropic import AsyncAnthropic
    from anthropic.types import Message, MessageStreamEvent
    from anthropic import APIError, AuthenticationError as AnthropicAuthError, RateLimitError as AnthropicRateLimit
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

from ..base import (
    AIService, AIProvider, GenerationContext, GenerationOptions, 
    GenerationResult, StreamChunk, AIServiceError, AuthenticationError,
    RateLimitError, QuotaExceededError, ModelNotFoundError, ContentFilterError
)


class AnthropicAdapter(AIService):
    """Anthropic服务适配器"""
    
    def __init__(self, api_key: str, model: str = "claude-3-haiku-20240307", base_url: Optional[str] = None):
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic库未安装，请运行: pip install anthropic")
        
        super().__init__(AIProvider.ANTHROPIC, api_key, model, base_url)
        self._default_base_url = "https://api.anthropic.com"
    
    async def initialize(self) -> None:
        """初始化Anthropic客户端"""
        self._client = AsyncAnthropic(
            api_key=self.api_key,
            base_url=self.base_url or self._default_base_url
        )
    
    async def generate_content(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> GenerationResult:
        """生成内容"""
        if not self._client:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            system_prompt = self._build_system_prompt(context)
            
            response = await self._client.messages.create(
                model=self.model,
                max_tokens=options.max_tokens,
                temperature=options.temperature,
                system=system_prompt,
                messages=[{"role": "user", "content": prompt}]
            )
            
            generation_time = time.time() - start_time
            
            # 提取文本内容
            content = ""
            if response.content:
                for block in response.content:
                    if hasattr(block, 'text'):
                        content += block.text
            
            return GenerationResult(
                id=uuid4(),
                content=content,
                provider=self.provider,
                model=self.model,
                usage={
                    "prompt_tokens": response.usage.input_tokens if response.usage else 0,
                    "completion_tokens": response.usage.output_tokens if response.usage else 0,
                    "total_tokens": (response.usage.input_tokens + response.usage.output_tokens) if response.usage else 0
                },
                finish_reason=response.stop_reason or "unknown",
                created_at=datetime.now(),
                generation_time=generation_time,
                context=context,
                options=options,
                metadata={"response_id": response.id}
            )
            
        except Exception as e:
            raise self._handle_error(e)
    
    async def generate_content_stream(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成内容"""
        if not self._client:
            await self.initialize()
        
        try:
            system_prompt = self._build_system_prompt(context)
            
            stream = await self._client.messages.stream(
                model=self.model,
                max_tokens=options.max_tokens,
                temperature=options.temperature,
                system=system_prompt,
                messages=[{"role": "user", "content": prompt}]
            )
            
            chunk_id = uuid4()
            accumulated_content = ""
            
            async with stream as stream_manager:
                async for event in stream_manager:
                    if event.type == "content_block_delta":
                        if hasattr(event.delta, 'text'):
                            delta = event.delta.text
                            accumulated_content += delta
                            
                            yield StreamChunk(
                                id=chunk_id,
                                content=accumulated_content,
                                delta=delta,
                                finish_reason=None
                            )
                    elif event.type == "message_stop":
                        # 流结束
                        yield StreamChunk(
                            id=chunk_id,
                            content=accumulated_content,
                            delta="",
                            finish_reason="stop"
                        )
                        
        except Exception as e:
            raise self._handle_error(e)
    
    async def validate_api_key(self) -> bool:
        """验证API密钥"""
        if not self._client:
            await self.initialize()
        
        try:
            # 发送一个简单的请求来验证API密钥
            await self._client.messages.create(
                model=self.model,
                max_tokens=1,
                messages=[{"role": "user", "content": "test"}]
            )
            return True
        except AnthropicAuthError:
            return False
        except Exception:
            # 其他错误可能是网络问题，不一定是API密钥问题
            return True
    
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        # Anthropic没有提供模型列表API，返回已知的模型
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307",
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022"
        ]
    
    async def estimate_tokens(self, text: str) -> int:
        """估算文本token数量（简单估算）"""
        # Anthropic的token计算与OpenAI类似
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars // 1.5 + other_chars // 4
        return int(estimated_tokens)
    
    def _build_system_prompt(self, context: GenerationContext) -> str:
        """构建系统提示词"""
        base_prompt = "你是一个专业的小说创作助手，擅长创作各种类型的小说内容。请用中文回答。"
        
        if context.content_type.value == "character_description":
            return base_prompt + "请专注于创作生动、立体的角色描述，包含外貌、性格、背景等方面。"
        elif context.content_type.value == "scene_description":
            return base_prompt + "请专注于创作富有画面感的场景描述，注重环境氛围和感官细节。"
        elif context.content_type.value == "plot_development":
            return base_prompt + "请专注于创作引人入胜的情节发展，注重逻辑性和戏剧冲突。"
        elif context.content_type.value == "dialogue":
            return base_prompt + "请专注于创作自然、符合角色性格的对话，注重语言风格和情感表达。"
        elif context.content_type.value == "content_optimization":
            return base_prompt + "请专注于优化和润色文本内容，提升文字质量和表现力。"
        else:
            return base_prompt
    
    def _handle_error(self, error: Exception) -> AIServiceError:
        """处理错误"""
        if isinstance(error, AnthropicAuthError):
            return AuthenticationError(
                "Anthropic API密钥无效或已过期",
                self.provider,
                "authentication_failed"
            )
        elif isinstance(error, AnthropicRateLimit):
            return RateLimitError(
                "Anthropic API请求频率超限",
                self.provider,
                retry_after=getattr(error, 'retry_after', None)
            )
        elif isinstance(error, APIError):
            error_message = str(error).lower()
            if "quota" in error_message or "credit" in error_message:
                return QuotaExceededError(
                    "Anthropic API配额已用完",
                    self.provider,
                    "quota_exceeded"
                )
            elif "model" in error_message and ("not found" in error_message or "invalid" in error_message):
                return ModelNotFoundError(
                    f"Anthropic模型 {self.model} 不存在或无权访问",
                    self.provider,
                    "model_not_found"
                )
            elif "content" in error_message and "policy" in error_message:
                return ContentFilterError(
                    "内容被Anthropic内容策略拦截",
                    self.provider,
                    "content_filtered"
                )
            else:
                return AIServiceError(
                    f"Anthropic API错误: {str(error)}",
                    self.provider,
                    "api_error"
                )
        else:
            return AIServiceError(
                f"Anthropic服务未知错误: {str(error)}",
                self.provider,
                "unknown_error"
            )
