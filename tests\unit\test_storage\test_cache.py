"""缓存管理器测试"""

import tempfile
import time
from pathlib import Path
import pytest

from src.core.storage.cache import (
    CacheEntry, LRUCache, DiskCache, CacheManager, CacheError
)


class TestCacheEntry:
    """缓存条目测试类"""
    
    def test_init(self):
        """测试初始化"""
        entry = CacheEntry("test_value", ttl=60)
        
        assert entry.value == "test_value"
        assert entry.ttl == 60
        assert entry.access_count == 0
        assert entry.created_at > 0
        assert entry.accessed_at == entry.created_at
    
    def test_is_expired(self):
        """测试过期检查"""
        # 不过期的条目
        entry = CacheEntry("test_value")
        assert not entry.is_expired()
        
        # 过期的条目
        entry = CacheEntry("test_value", ttl=0.1)
        time.sleep(0.2)
        assert entry.is_expired()
    
    def test_touch(self):
        """测试更新访问时间"""
        entry = CacheEntry("test_value")
        original_accessed_at = entry.accessed_at
        original_count = entry.access_count
        
        time.sleep(0.01)
        entry.touch()
        
        assert entry.accessed_at > original_accessed_at
        assert entry.access_count == original_count + 1


class TestLRUCache:
    """LRU缓存测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.cache = LRUCache(max_size=3)
    
    def test_init(self):
        """测试初始化"""
        assert self.cache.max_size == 3
        assert self.cache.default_ttl is None
        assert len(self.cache._cache) == 0
    
    def test_set_and_get(self):
        """测试设置和获取"""
        self.cache.set("key1", "value1")
        
        value = self.cache.get("key1")
        assert value == "value1"
        
        # 获取不存在的键
        value = self.cache.get("nonexistent", "default")
        assert value == "default"
    
    def test_lru_eviction(self):
        """测试LRU淘汰策略"""
        # 填满缓存
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        
        # 访问key1，使其成为最近使用
        self.cache.get("key1")
        
        # 添加新键，应该淘汰key2
        self.cache.set("key4", "value4")
        
        assert self.cache.get("key1") == "value1"  # 仍然存在
        assert self.cache.get("key2") is None      # 被淘汰
        assert self.cache.get("key3") == "value3"  # 仍然存在
        assert self.cache.get("key4") == "value4"  # 新添加
    
    def test_ttl_expiration(self):
        """测试TTL过期"""
        self.cache.set("key1", "value1", ttl=0.1)
        
        # 立即获取应该成功
        assert self.cache.get("key1") == "value1"
        
        # 等待过期
        time.sleep(0.2)
        
        # 过期后获取应该返回None
        assert self.cache.get("key1") is None
    
    def test_update_existing_key(self):
        """测试更新现有键"""
        self.cache.set("key1", "value1")
        self.cache.set("key1", "value2")
        
        assert self.cache.get("key1") == "value2"
        assert len(self.cache._cache) == 1
    
    def test_delete(self):
        """测试删除"""
        self.cache.set("key1", "value1")
        
        success = self.cache.delete("key1")
        assert success is True
        assert self.cache.get("key1") is None
        
        # 删除不存在的键
        success = self.cache.delete("nonexistent")
        assert success is False
    
    def test_clear(self):
        """测试清空"""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        self.cache.clear()
        
        assert len(self.cache._cache) == 0
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") is None
    
    def test_cleanup_expired(self):
        """测试清理过期条目"""
        self.cache.set("key1", "value1", ttl=0.1)
        self.cache.set("key2", "value2", ttl=1.0)
        self.cache.set("key3", "value3")  # 无TTL
        
        time.sleep(0.2)
        
        cleaned = self.cache.cleanup_expired()
        
        assert cleaned == 1
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") == "value2"
        assert self.cache.get("key3") == "value3"
    
    def test_get_stats(self):
        """测试获取统计信息"""
        # 初始统计
        stats = self.cache.get_stats()
        assert stats['size'] == 0
        assert stats['hits'] == 0
        assert stats['misses'] == 0
        assert stats['hit_rate'] == 0
        
        # 添加数据并访问
        self.cache.set("key1", "value1")
        self.cache.get("key1")  # hit
        self.cache.get("key2")  # miss
        
        stats = self.cache.get_stats()
        assert stats['size'] == 1
        assert stats['hits'] == 1
        assert stats['misses'] == 1
        assert stats['hit_rate'] == 0.5


class TestDiskCache:
    """磁盘缓存测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache = DiskCache(self.temp_dir, max_size_mb=1)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        assert self.cache.cache_dir == self.temp_dir
        assert self.cache.max_size_bytes == 1024 * 1024
        assert self.temp_dir.exists()
    
    def test_set_and_get(self):
        """测试设置和获取"""
        success = self.cache.set("key1", "value1")
        assert success is True
        
        value = self.cache.get("key1")
        assert value == "value1"
        
        # 获取不存在的键
        value = self.cache.get("nonexistent", "default")
        assert value == "default"
    
    def test_complex_data(self):
        """测试复杂数据类型"""
        test_data = {
            'string': 'test',
            'number': 42,
            'list': [1, 2, 3],
            'dict': {'nested': 'value'}
        }
        
        success = self.cache.set("complex", test_data)
        assert success is True
        
        retrieved = self.cache.get("complex")
        assert retrieved == test_data
    
    def test_ttl_expiration(self):
        """测试TTL过期"""
        success = self.cache.set("key1", "value1", ttl=0.1)
        assert success is True
        
        # 立即获取应该成功
        assert self.cache.get("key1") == "value1"
        
        # 等待过期
        time.sleep(0.2)
        
        # 过期后获取应该返回None
        assert self.cache.get("key1") is None
    
    def test_delete(self):
        """测试删除"""
        self.cache.set("key1", "value1")
        
        success = self.cache.delete("key1")
        assert success is True
        assert self.cache.get("key1") is None
        
        # 删除不存在的键
        success = self.cache.delete("nonexistent")
        assert success is True  # 磁盘缓存删除不存在的键也返回True
    
    def test_clear(self):
        """测试清空"""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        self.cache.clear()
        
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") is None
        assert len(self.cache.metadata) == 0
    
    def test_get_stats(self):
        """测试获取统计信息"""
        # 初始统计
        stats = self.cache.get_stats()
        assert stats['entries'] == 0
        assert stats['total_size_bytes'] == 0
        
        # 添加数据
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        
        stats = self.cache.get_stats()
        assert stats['entries'] == 2
        assert stats['total_size_bytes'] > 0


class TestCacheManager:
    """缓存管理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache_manager = CacheManager(
            cache_dir=self.temp_dir,
            memory_cache_size=100,
            disk_cache_size_mb=1
        )
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        assert self.cache_manager.memory_cache is not None
        assert self.cache_manager.disk_cache is not None
    
    def test_memory_cache_priority(self):
        """测试内存缓存优先级"""
        # 设置缓存
        self.cache_manager.set("key1", "value1")
        
        # 应该从内存缓存获取
        value = self.cache_manager.get("key1")
        assert value == "value1"
        
        # 验证内存缓存中存在
        memory_value = self.cache_manager.memory_cache.get("key1")
        assert memory_value == "value1"
    
    def test_disk_cache_fallback(self):
        """测试磁盘缓存回退"""
        # 直接设置磁盘缓存
        self.cache_manager.disk_cache.set("key1", "value1")
        
        # 从缓存管理器获取（应该从磁盘获取并放入内存）
        value = self.cache_manager.get("key1")
        assert value == "value1"
        
        # 验证已放入内存缓存
        memory_value = self.cache_manager.memory_cache.get("key1")
        assert memory_value == "value1"
    
    def test_disable_disk_cache(self):
        """测试禁用磁盘缓存"""
        # 只使用内存缓存
        self.cache_manager.set("key1", "value1", use_disk=False)
        
        # 内存缓存中应该存在
        memory_value = self.cache_manager.memory_cache.get("key1")
        assert memory_value == "value1"
        
        # 磁盘缓存中不应该存在
        disk_value = self.cache_manager.disk_cache.get("key1")
        assert disk_value is None
    
    def test_delete(self):
        """测试删除"""
        self.cache_manager.set("key1", "value1")
        
        success = self.cache_manager.delete("key1")
        assert success is True
        
        assert self.cache_manager.get("key1") is None
        assert self.cache_manager.memory_cache.get("key1") is None
        assert self.cache_manager.disk_cache.get("key1") is None
    
    def test_clear(self):
        """测试清空"""
        self.cache_manager.set("key1", "value1")
        self.cache_manager.set("key2", "value2")
        
        self.cache_manager.clear()
        
        assert self.cache_manager.get("key1") is None
        assert self.cache_manager.get("key2") is None
    
    def test_cleanup(self):
        """测试清理"""
        # 设置过期缓存
        self.cache_manager.memory_cache.set("key1", "value1", ttl=0.1)
        
        time.sleep(0.2)
        
        cleanup_result = self.cache_manager.cleanup()
        assert 'memory_cleaned' in cleanup_result
        assert cleanup_result['memory_cleaned'] >= 0
    
    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.cache_manager.get_stats()
        
        assert 'memory' in stats
        assert 'disk' in stats
        assert 'size' in stats['memory']
        assert 'entries' in stats['disk']
