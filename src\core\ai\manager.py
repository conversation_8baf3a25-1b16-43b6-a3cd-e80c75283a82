"""
AI服务管理器

负责管理多个AI服务，提供统一的接口，支持负载均衡、故障转移等功能。
"""

import asyncio
import random
from typing import Dict, List, Optional, AsyncGenerator, Union
from uuid import UUID

from ..models.project import AIConfiguration
from .base import (
    AIService, AIProvider, GenerationContext, GenerationOptions,
    GenerationResult, StreamChunk, AIServiceError, PromptManager, GenerationType
)
from .adapters import OpenAIAdapter, AnthropicAdapter, DeepSeekAdapter, ZhipuAdapter


class AIServiceManager:
    """AI服务管理器"""
    
    def __init__(self, config: AIConfiguration):
        self.config = config
        self._services: Dict[AIProvider, AIService] = {}
        self._prompt_manager = PromptManager()
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化所有配置的AI服务"""
        if self._initialized:
            return
        
        # 初始化OpenAI服务
        if self.config.openai_api_key:
            try:
                service = OpenAIAdapter(
                    api_key=self.config.openai_api_key,
                    model=self.config.openai_model,
                    base_url=self.config.openai_base_url
                )
                await service.initialize()
                self._services[AIProvider.OPENAI] = service
            except Exception as e:
                print(f"初始化OpenAI服务失败: {e}")
        
        # 初始化Anthropic服务
        if self.config.anthropic_api_key:
            try:
                service = AnthropicAdapter(
                    api_key=self.config.anthropic_api_key,
                    model=self.config.anthropic_model,
                    base_url=self.config.anthropic_base_url
                )
                await service.initialize()
                self._services[AIProvider.ANTHROPIC] = service
            except Exception as e:
                print(f"初始化Anthropic服务失败: {e}")
        
        # 初始化DeepSeek服务
        if self.config.deepseek_api_key:
            try:
                service = DeepSeekAdapter(
                    api_key=self.config.deepseek_api_key,
                    model=self.config.deepseek_model,
                    base_url=self.config.deepseek_base_url
                )
                await service.initialize()
                self._services[AIProvider.DEEPSEEK] = service
            except Exception as e:
                print(f"初始化DeepSeek服务失败: {e}")
        
        # 初始化智谱AI服务
        if self.config.zhipu_api_key:
            try:
                service = ZhipuAdapter(
                    api_key=self.config.zhipu_api_key,
                    model=self.config.zhipu_model,
                    base_url=self.config.zhipu_base_url
                )
                await service.initialize()
                self._services[AIProvider.ZHIPU] = service
            except Exception as e:
                print(f"初始化智谱AI服务失败: {e}")
        
        self._initialized = True
    
    async def generate_content(
        self,
        context: GenerationContext,
        provider: Optional[AIProvider] = None,
        custom_prompt: Optional[str] = None,
        **prompt_kwargs
    ) -> GenerationResult:
        """生成内容"""
        if not self._initialized:
            await self.initialize()
        
        # 选择服务
        service = self._select_service(provider)
        if not service:
            raise AIServiceError("没有可用的AI服务", AIProvider.OPENAI)
        
        # 构建提示词
        if custom_prompt:
            prompt = custom_prompt
        else:
            prompt = self._prompt_manager.format_prompt(context.content_type, **prompt_kwargs)
        
        # 构建选项
        options = GenerationOptions(
            max_tokens=self.config.max_tokens,
            temperature=self.config.temperature,
            timeout=self.config.timeout,
            max_retries=self.config.max_retries
        )
        
        # 生成内容，支持重试和故障转移
        last_error = None
        for attempt in range(self.config.max_retries):
            try:
                return await service.generate_content(prompt, context, options)
            except AIServiceError as e:
                last_error = e
                if attempt < self.config.max_retries - 1:
                    # 尝试故障转移到其他服务
                    service = self._select_fallback_service(service.provider)
                    if not service:
                        break
                    await asyncio.sleep(2 ** attempt)  # 指数退避
        
        raise last_error or AIServiceError("生成内容失败", service.provider if service else AIProvider.OPENAI)
    
    async def generate_content_stream(
        self,
        context: GenerationContext,
        provider: Optional[AIProvider] = None,
        custom_prompt: Optional[str] = None,
        **prompt_kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成内容"""
        if not self._initialized:
            await self.initialize()
        
        # 选择服务
        service = self._select_service(provider)
        if not service:
            raise AIServiceError("没有可用的AI服务", AIProvider.OPENAI)
        
        # 构建提示词
        if custom_prompt:
            prompt = custom_prompt
        else:
            prompt = self._prompt_manager.format_prompt(context.content_type, **prompt_kwargs)
        
        # 构建选项
        options = GenerationOptions(
            max_tokens=self.config.max_tokens,
            temperature=self.config.temperature,
            timeout=self.config.timeout,
            stream=True
        )
        
        # 流式生成内容
        async for chunk in service.generate_content_stream(prompt, context, options):
            yield chunk
    
    async def validate_service(self, provider: AIProvider) -> bool:
        """验证指定服务"""
        if not self._initialized:
            await self.initialize()
        
        service = self._services.get(provider)
        if not service:
            return False
        
        return await service.validate_api_key()
    
    async def health_check(self) -> Dict[AIProvider, bool]:
        """健康检查所有服务"""
        if not self._initialized:
            await self.initialize()
        
        results = {}
        for provider, service in self._services.items():
            try:
                results[provider] = await service.health_check()
            except Exception:
                results[provider] = False
        
        return results
    
    def get_available_providers(self) -> List[AIProvider]:
        """获取可用的服务商列表"""
        return list(self._services.keys())
    
    def get_prompt_manager(self) -> PromptManager:
        """获取提示词管理器"""
        return self._prompt_manager
    
    def _select_service(self, preferred_provider: Optional[AIProvider] = None) -> Optional[AIService]:
        """选择服务"""
        if not self._services:
            return None
        
        # 如果指定了服务商，优先使用
        if preferred_provider and preferred_provider in self._services:
            return self._services[preferred_provider]
        
        # 使用默认服务商
        default_provider = AIProvider(self.config.default_provider)
        if default_provider in self._services:
            return self._services[default_provider]
        
        # 随机选择一个可用服务
        return random.choice(list(self._services.values()))
    
    def _select_fallback_service(self, failed_provider: AIProvider) -> Optional[AIService]:
        """选择故障转移服务"""
        available_services = [
            service for provider, service in self._services.items() 
            if provider != failed_provider
        ]
        
        if not available_services:
            return None
        
        return random.choice(available_services)


class ContentGenerator:
    """内容生成器"""
    
    def __init__(self, ai_manager: AIServiceManager):
        self.ai_manager = ai_manager
    
    async def generate_character_description(
        self,
        name: str,
        gender: str,
        age: int,
        style: str = "现代都市",
        project_id: Optional[UUID] = None,
        provider: Optional[AIProvider] = None
    ) -> GenerationResult:
        """生成角色描述"""
        context = GenerationContext(
            content_type=GenerationType.CHARACTER_DESCRIPTION,
            project_id=project_id
        )
        
        return await self.ai_manager.generate_content(
            context=context,
            provider=provider,
            name=name,
            gender=gender,
            age=age,
            style=style
        )
    
    async def generate_scene_description(
        self,
        name: str,
        location: str,
        time: str,
        style: str = "现代都市",
        project_id: Optional[UUID] = None,
        provider: Optional[AIProvider] = None
    ) -> GenerationResult:
        """生成场景描述"""
        context = GenerationContext(
            content_type=GenerationType.SCENE_DESCRIPTION,
            project_id=project_id
        )
        
        return await self.ai_manager.generate_content(
            context=context,
            provider=provider,
            name=name,
            location=location,
            time=time,
            style=style
        )
    
    async def generate_plot_development(
        self,
        current_situation: str,
        goal: str,
        style: str = "现代都市",
        project_id: Optional[UUID] = None,
        provider: Optional[AIProvider] = None
    ) -> GenerationResult:
        """生成情节发展"""
        context = GenerationContext(
            content_type=GenerationType.PLOT_DEVELOPMENT,
            project_id=project_id
        )
        
        return await self.ai_manager.generate_content(
            context=context,
            provider=provider,
            current_situation=current_situation,
            goal=goal,
            style=style
        )
    
    async def generate_dialogue(
        self,
        characters: str,
        scene: str,
        situation: str,
        style: str = "现代都市",
        project_id: Optional[UUID] = None,
        provider: Optional[AIProvider] = None
    ) -> GenerationResult:
        """生成对话"""
        context = GenerationContext(
            content_type=GenerationType.DIALOGUE,
            project_id=project_id
        )
        
        return await self.ai_manager.generate_content(
            context=context,
            provider=provider,
            characters=characters,
            scene=scene,
            situation=situation,
            style=style
        )
    
    async def optimize_content(
        self,
        content: str,
        requirements: str = "提升文字质量和表现力",
        project_id: Optional[UUID] = None,
        provider: Optional[AIProvider] = None
    ) -> GenerationResult:
        """优化内容"""
        context = GenerationContext(
            content_type=GenerationType.CONTENT_OPTIMIZATION,
            project_id=project_id,
            existing_content=content
        )
        
        return await self.ai_manager.generate_content(
            context=context,
            provider=provider,
            content=content,
            requirements=requirements
        )
