"""数据验证系统

提供comprehensive的数据验证功能，包括：
- 字段级验证（Field-level validation）
- 业务规则检查（Business rule validation）
- 跨模型关联验证（Cross-model relationship validation）
- 错误处理和报告机制
"""

from .validators import (
    FieldValidator,
    BusinessRuleValidator,
    RelationshipValidator,
    ValidationEngine,
    ValidationRule,
    ValidationContext
)

from .rules import (
    StringValidationRule,
    NumericValidationRule,
    DateTimeValidationRule,
    ListValidationRule,
    EnumValidationRule,
    UUIDValidationRule,
    EmailValidationRule,
    URLValidationRule,
    FilePathValidationRule,
    CustomValidationRule
)

from .business_rules import (
    ProjectBusinessRules,
    CharacterBusinessRules,
    SceneBusinessRules,
    EventBusinessRules,
    StoryElementBusinessRules
)

from .relationship_validators import (
    CharacterRelationshipValidator,
    SceneEventRelationshipValidator,
    ProjectElementRelationshipValidator,
    TimelineConsistencyValidator,
    PlotConsistencyValidator
)

from .exceptions import (
    ValidationError,
    ValidationWarning,
    ValidationInfo,
    ValidationException,
    BusinessRuleViolation,
    RelationshipValidationError
)

__all__ = [
    # 核心验证器
    "FieldValidator",
    "BusinessRuleValidator", 
    "RelationshipValidator",
    "ValidationEngine",
    "ValidationRule",
    "ValidationContext",
    
    # 字段验证规则
    "StringValidationRule",
    "NumericValidationRule",
    "DateTimeValidationRule",
    "ListValidationRule",
    "EnumValidationRule",
    "UUIDValidationRule",
    "EmailValidationRule",
    "URLValidationRule",
    "FilePathValidationRule",
    "CustomValidationRule",
    
    # 业务规则
    "ProjectBusinessRules",
    "CharacterBusinessRules",
    "SceneBusinessRules",
    "EventBusinessRules",
    "StoryElementBusinessRules",
    
    # 关系验证器
    "CharacterRelationshipValidator",
    "SceneEventRelationshipValidator",
    "ProjectElementRelationshipValidator",
    "TimelineConsistencyValidator",
    "PlotConsistencyValidator",
    
    # 异常类
    "ValidationError",
    "ValidationWarning",
    "ValidationInfo",
    "ValidationException",
    "BusinessRuleViolation",
    "RelationshipValidationError"
]
