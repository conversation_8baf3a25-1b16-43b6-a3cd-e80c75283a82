# 笔落App Makefile
# 提供常用的开发和构建命令

.PHONY: help setup install test lint format type-check security clean build run docs

# 默认目标
help:
	@echo "笔落App开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  setup        - 设置开发环境"
	@echo "  install      - 安装依赖包"
	@echo "  test         - 运行测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  lint         - 运行代码质量检查"
	@echo "  format       - 格式化代码"
	@echo "  type-check   - 运行类型检查"
	@echo "  security     - 运行安全检查"
	@echo "  pre-commit   - 运行pre-commit检查"
	@echo "  clean        - 清理临时文件"
	@echo "  build        - 构建应用程序"
	@echo "  run          - 运行应用程序"
	@echo "  docs         - 生成文档"

# 设置开发环境
setup:
	@echo "设置开发环境..."
	python scripts/setup_dev.py

# 安装依赖
install:
	@echo "安装依赖包..."
	pip install --upgrade pip
	pip install -r requirements.txt
	pip install -e .[dev]

# 运行测试
test:
	@echo "运行测试..."
	pytest tests/ -v

# 运行测试并生成覆盖率报告
test-cov:
	@echo "运行测试并生成覆盖率报告..."
	pytest tests/ --cov=src --cov-report=html --cov-report=term-missing

# 代码质量检查
lint:
	@echo "运行代码质量检查..."
	flake8 src/ tests/
	@echo "代码质量检查完成"

# 格式化代码
format:
	@echo "格式化代码..."
	black src/ tests/
	isort src/ tests/
	@echo "代码格式化完成"

# 类型检查
type-check:
	@echo "运行类型检查..."
	mypy src/

# 安全检查
security:
	@echo "运行安全检查..."
	bandit -r src/ -f json -o reports/bandit-report.json
	safety check --json --output reports/safety-report.json
	@echo "安全检查完成，报告保存在 reports/ 目录"

# 运行pre-commit检查
pre-commit:
	@echo "运行pre-commit检查..."
	pre-commit run --all-files

# 清理临时文件
clean:
	@echo "清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	@echo "清理完成"

# 构建应用程序
build:
	@echo "构建应用程序..."
	python setup.py sdist bdist_wheel
	@echo "构建完成，文件保存在 dist/ 目录"

# 使用PyInstaller构建可执行文件
build-exe:
	@echo "构建可执行文件..."
	pyinstaller --onefile --windowed --name=BambooFall src/main.py
	@echo "可执行文件构建完成，保存在 dist/ 目录"

# 运行应用程序
run:
	@echo "启动笔落App..."
	python src/main.py

# 运行应用程序（开发模式）
run-dev:
	@echo "启动笔落App（开发模式）..."
	APP_DEBUG=true python src/main.py

# 生成文档
docs:
	@echo "生成文档..."
	mkdir -p docs/build
	sphinx-build -b html docs/source docs/build
	@echo "文档生成完成，保存在 docs/build/ 目录"

# 安装pre-commit钩子
install-hooks:
	@echo "安装pre-commit钩子..."
	pre-commit install
	@echo "pre-commit钩子安装完成"

# 更新依赖
update-deps:
	@echo "更新依赖包..."
	pip install --upgrade -r requirements.txt
	@echo "依赖更新完成"

# 创建发布版本
release:
	@echo "创建发布版本..."
	python setup.py sdist bdist_wheel
	twine check dist/*
	@echo "发布包已准备就绪，使用 'twine upload dist/*' 上传到PyPI"

# 运行所有检查
check-all: lint type-check security test
	@echo "所有检查完成"

# 初始化项目（首次使用）
init: setup install-hooks
	@echo "项目初始化完成"

# 开发者快速检查
dev-check: format lint type-check test
	@echo "开发者检查完成"
