"""事件系统模块

提供应用程序内部的事件通信机制，包括：
- 事件总线
- 事件定义
- 事件处理器
- 异步事件处理
- 事件日志
"""

from .bus import EventBus
from .base import Event, EventHandler
from .types import (
    ProjectEvent,
    StoryEvent, 
    CharacterEvent,
    SceneEvent,
    AIEvent,
    UIEvent
)

__all__ = [
    "EventBus",
    "Event",
    "EventHandler",
    "ProjectEvent",
    "StoryEvent",
    "CharacterEvent", 
    "SceneEvent",
    "AIEvent",
    "UIEvent"
]
