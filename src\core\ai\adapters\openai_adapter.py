"""
OpenAI服务适配器

实现OpenAI API的具体适配逻辑，包括聊天完成、流式响应、错误处理等功能。
"""

import asyncio
import time
from typing import AsyncGenerator, Dict, List, Optional
from uuid import uuid4

try:
    from openai import AsyncOpenAI
    from openai.types.chat import ChatCompletion, ChatCompletionChunk
    from openai import APIError, AuthenticationError as OpenAIAuthError, RateLimitError as OpenAIRateLimit
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from ..base import (
    AIService, AIProvider, GenerationContext, GenerationOptions, 
    GenerationResult, StreamChunk, AIServiceError, AuthenticationError,
    RateLimitError, QuotaExceededError, ModelNotFoundError, ContentFilterError
)


class OpenAIAdapter(AIService):
    """OpenAI服务适配器"""
    
    def __init__(self, api_key: str, model: str = "gpt-4o-mini", base_url: Optional[str] = None):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI库未安装，请运行: pip install openai")
        
        super().__init__(AIProvider.OPENAI, api_key, model, base_url)
        self._default_base_url = "https://api.openai.com/v1"
    
    async def initialize(self) -> None:
        """初始化OpenAI客户端"""
        self._client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url or self._default_base_url
        )
    
    async def generate_content(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> GenerationResult:
        """生成内容"""
        if not self._client:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            messages = self._build_messages(prompt, context)
            
            response = await self._client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=options.max_tokens,
                temperature=options.temperature,
                top_p=options.top_p,
                frequency_penalty=options.frequency_penalty,
                presence_penalty=options.presence_penalty,
                timeout=options.timeout
            )
            
            generation_time = time.time() - start_time
            
            return GenerationResult(
                id=uuid4(),
                content=response.choices[0].message.content or "",
                provider=self.provider,
                model=self.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0
                },
                finish_reason=response.choices[0].finish_reason or "unknown",
                created_at=response.created,
                generation_time=generation_time,
                context=context,
                options=options,
                metadata={"response_id": response.id}
            )
            
        except Exception as e:
            raise self._handle_error(e)
    
    async def generate_content_stream(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成内容"""
        if not self._client:
            await self.initialize()
        
        try:
            messages = self._build_messages(prompt, context)
            
            stream = await self._client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=options.max_tokens,
                temperature=options.temperature,
                top_p=options.top_p,
                frequency_penalty=options.frequency_penalty,
                presence_penalty=options.presence_penalty,
                timeout=options.timeout,
                stream=True
            )
            
            chunk_id = uuid4()
            accumulated_content = ""
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    delta = chunk.choices[0].delta.content
                    accumulated_content += delta
                    
                    yield StreamChunk(
                        id=chunk_id,
                        content=accumulated_content,
                        delta=delta,
                        finish_reason=chunk.choices[0].finish_reason,
                        usage=None  # OpenAI在流式模式下不提供usage信息
                    )
                    
        except Exception as e:
            raise self._handle_error(e)
    
    async def validate_api_key(self) -> bool:
        """验证API密钥"""
        if not self._client:
            await self.initialize()
        
        try:
            # 发送一个简单的请求来验证API密钥
            await self._client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return True
        except OpenAIAuthError:
            return False
        except Exception:
            # 其他错误可能是网络问题，不一定是API密钥问题
            return True
    
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        if not self._client:
            await self.initialize()
        
        try:
            models = await self._client.models.list()
            return [model.id for model in models.data if "gpt" in model.id.lower()]
        except Exception as e:
            raise self._handle_error(e)
    
    async def estimate_tokens(self, text: str) -> int:
        """估算文本token数量（简单估算）"""
        # 简单的token估算：英文约4字符=1token，中文约1.5字符=1token
        # 这是一个粗略估算，实际应该使用tiktoken库
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars // 1.5 + other_chars // 4
        return int(estimated_tokens)
    
    def _build_messages(self, prompt: str, context: GenerationContext) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = []
        
        # 系统消息
        system_prompt = self._build_system_prompt(context)
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 用户消息
        messages.append({"role": "user", "content": prompt})
        
        return messages
    
    def _build_system_prompt(self, context: GenerationContext) -> str:
        """构建系统提示词"""
        base_prompt = "你是一个专业的小说创作助手，擅长创作各种类型的小说内容。"
        
        if context.content_type.value == "character_description":
            return base_prompt + "请专注于创作生动、立体的角色描述。"
        elif context.content_type.value == "scene_description":
            return base_prompt + "请专注于创作富有画面感的场景描述。"
        elif context.content_type.value == "plot_development":
            return base_prompt + "请专注于创作引人入胜的情节发展。"
        elif context.content_type.value == "dialogue":
            return base_prompt + "请专注于创作自然、符合角色性格的对话。"
        elif context.content_type.value == "content_optimization":
            return base_prompt + "请专注于优化和润色文本内容。"
        else:
            return base_prompt
    
    def _handle_error(self, error: Exception) -> AIServiceError:
        """处理错误"""
        if isinstance(error, OpenAIAuthError):
            return AuthenticationError(
                "OpenAI API密钥无效或已过期",
                self.provider,
                "authentication_failed"
            )
        elif isinstance(error, OpenAIRateLimit):
            return RateLimitError(
                "OpenAI API请求频率超限",
                self.provider,
                retry_after=getattr(error, 'retry_after', None)
            )
        elif isinstance(error, APIError):
            if "quota" in str(error).lower():
                return QuotaExceededError(
                    "OpenAI API配额已用完",
                    self.provider,
                    "quota_exceeded"
                )
            elif "model" in str(error).lower() and "not found" in str(error).lower():
                return ModelNotFoundError(
                    f"OpenAI模型 {self.model} 不存在",
                    self.provider,
                    "model_not_found"
                )
            elif "content_filter" in str(error).lower():
                return ContentFilterError(
                    "内容被OpenAI内容过滤器拦截",
                    self.provider,
                    "content_filtered"
                )
            else:
                return AIServiceError(
                    f"OpenAI API错误: {str(error)}",
                    self.provider,
                    "api_error"
                )
        else:
            return AIServiceError(
                f"OpenAI服务未知错误: {str(error)}",
                self.provider,
                "unknown_error"
            )
