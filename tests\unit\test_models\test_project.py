"""项目模型测试

测试WritingProject、ProjectSettings、AIConfiguration等项目相关模型
"""

import pytest
from datetime import datetime

from src.core.models.project import WritingProject, ProjectSettings, AIConfiguration, ProjectValidator
from src.core.models.enums import ProjectType, ProjectStatus


class TestProjectSettings:
    """ProjectSettings测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        settings = ProjectSettings()
        
        assert settings.auto_save_interval == 300
        assert settings.backup_enabled is True
        assert settings.backup_interval == 3600
        assert settings.max_backup_count == 10
        assert settings.theme == "light"
        assert settings.font_family == "Microsoft YaHei"
        assert settings.font_size == 12
        assert settings.line_spacing == 1.5
        assert settings.default_export_format == "txt"
        assert settings.export_include_metadata is True
        assert settings.word_count_target == 80000
        assert settings.daily_word_target == 2000
    
    def test_validate_auto_save_interval(self):
        """测试自动保存间隔验证"""
        # 有效值
        settings = ProjectSettings(auto_save_interval=120)
        assert settings.auto_save_interval == 120
        
        # 无效值（小于60）
        with pytest.raises(ValueError, match="自动保存间隔不能少于60秒"):
            ProjectSettings(auto_save_interval=30)
    
    def test_validate_font_size(self):
        """测试字体大小验证"""
        # 有效值
        settings = ProjectSettings(font_size=14)
        assert settings.font_size == 14
        
        # 无效值（太小）
        with pytest.raises(ValueError, match="字体大小必须在8-72之间"):
            ProjectSettings(font_size=5)
        
        # 无效值（太大）
        with pytest.raises(ValueError, match="字体大小必须在8-72之间"):
            ProjectSettings(font_size=100)


class TestAIConfiguration:
    """AIConfiguration测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        config = AIConfiguration()
        
        assert config.default_provider == "openai"
        assert config.openai_api_key is None
        assert config.openai_model == "gpt-4o-mini"
        assert config.openai_base_url == "https://api.openai.com/v1"
        assert config.deepseek_api_key is None
        assert config.deepseek_model == "deepseek-chat"
        assert config.zhipu_api_key is None
        assert config.zhipu_model == "glm-4"
        assert config.anthropic_api_key is None
        assert config.anthropic_model == "claude-3-haiku-20240307"
        assert config.max_tokens == 2000
        assert config.temperature == 0.7
        assert config.timeout == 30
        assert config.max_retries == 3
    
    def test_validate_temperature(self):
        """测试温度参数验证"""
        # 有效值
        config = AIConfiguration(temperature=0.5)
        assert config.temperature == 0.5
        
        # 边界值
        config = AIConfiguration(temperature=0.0)
        assert config.temperature == 0.0
        
        config = AIConfiguration(temperature=2.0)
        assert config.temperature == 2.0
        
        # 无效值（小于0）
        with pytest.raises(ValueError, match="温度参数必须在0-2之间"):
            AIConfiguration(temperature=-0.1)
        
        # 无效值（大于2）
        with pytest.raises(ValueError, match="温度参数必须在0-2之间"):
            AIConfiguration(temperature=2.1)
    
    def test_validate_max_tokens(self):
        """测试最大令牌数验证"""
        # 有效值
        config = AIConfiguration(max_tokens=1000)
        assert config.max_tokens == 1000
        
        # 无效值（小于1）
        with pytest.raises(ValueError, match="最大令牌数必须在1-8000之间"):
            AIConfiguration(max_tokens=0)
        
        # 无效值（大于8000）
        with pytest.raises(ValueError, match="最大令牌数必须在1-8000之间"):
            AIConfiguration(max_tokens=8001)


class TestWritingProject:
    """WritingProject测试类"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        project = WritingProject(name="测试项目")
        
        assert project.name == "测试项目"
        assert project.author == ""
        assert project.project_type == ProjectType.FANTASY
        assert project.description == ""
        assert project.cover_image is None
        assert project.status == ProjectStatus.DRAFT
        assert project.completion_percentage == 0.0
        assert project.word_count == 0
        assert project.chapter_count == 0
        assert project.character_count == 0
        assert project.scene_count == 0
        assert project.event_count == 0
        assert project.last_opened_at is None
        assert project.writing_time_minutes == 0
        assert isinstance(project.settings, ProjectSettings)
        assert isinstance(project.ai_config, AIConfiguration)
        assert project.project_path is None
    
    def test_init_full(self):
        """测试完整初始化"""
        project = WritingProject(
            name="完整项目",
            author="测试作者",
            project_type=ProjectType.ROMANCE,
            description="这是一个测试项目",
            status=ProjectStatus.WRITING,
            completion_percentage=25.5,
            word_count=20000,
            chapter_count=5
        )
        
        assert project.name == "完整项目"
        assert project.author == "测试作者"
        assert project.project_type == ProjectType.ROMANCE
        assert project.description == "这是一个测试项目"
        assert project.status == ProjectStatus.WRITING
        assert project.completion_percentage == 25.5
        assert project.word_count == 20000
        assert project.chapter_count == 5
    
    def test_validate_name(self):
        """测试项目名称验证"""
        # 有效名称
        project = WritingProject(name="有效项目名")
        assert project.name == "有效项目名"
        
        # 名称会被trim
        project = WritingProject(name="  带空格的名称  ")
        assert project.name == "带空格的名称"
        
        # 空名称
        with pytest.raises(ValueError, match="项目名称不能为空"):
            WritingProject(name="")
        
        # 只有空格的名称
        with pytest.raises(ValueError, match="项目名称不能为空"):
            WritingProject(name="   ")
        
        # 超长名称
        with pytest.raises(ValueError, match="项目名称不能超过100个字符"):
            WritingProject(name="a" * 101)
        
        # 包含非法字符
        illegal_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        for char in illegal_chars:
            with pytest.raises(ValueError, match="项目名称包含非法字符"):
                WritingProject(name=f"项目{char}名称")
    
    def test_validate_completion_percentage(self):
        """测试完成百分比验证"""
        # 有效值
        project = WritingProject(name="测试", completion_percentage=50.0)
        assert project.completion_percentage == 50.0
        
        # 边界值
        project = WritingProject(name="测试", completion_percentage=0.0)
        assert project.completion_percentage == 0.0
        
        project = WritingProject(name="测试", completion_percentage=100.0)
        assert project.completion_percentage == 100.0
        
        # 无效值（小于0）
        with pytest.raises(ValueError, match="完成百分比必须在0-100之间"):
            WritingProject(name="测试", completion_percentage=-1.0)
        
        # 无效值（大于100）
        with pytest.raises(ValueError, match="完成百分比必须在0-100之间"):
            WritingProject(name="测试", completion_percentage=101.0)
    
    def test_validate_counts(self):
        """测试计数验证"""
        # 有效值
        project = WritingProject(
            name="测试",
            word_count=1000,
            chapter_count=5,
            character_count=10,
            scene_count=8,
            event_count=15
        )
        assert project.word_count == 1000
        assert project.chapter_count == 5
        
        # 无效值（负数）
        with pytest.raises(ValueError, match="计数不能为负数"):
            WritingProject(name="测试", word_count=-1)
        
        with pytest.raises(ValueError, match="计数不能为负数"):
            WritingProject(name="测试", chapter_count=-1)
    
    def test_update_statistics(self):
        """测试更新统计信息"""
        project = WritingProject(name="测试项目")
        original_version = project.version
        
        project.update_statistics(
            word_count=5000,
            chapter_count=3,
            character_count=5
        )
        
        assert project.word_count == 5000
        assert project.chapter_count == 3
        assert project.character_count == 5
        assert project.version == original_version + 1
        
        # 部分更新
        project.update_statistics(word_count=6000)
        assert project.word_count == 6000
        assert project.chapter_count == 3  # 保持不变
    
    def test_calculate_completion_percentage(self):
        """测试计算完成百分比"""
        project = WritingProject(name="测试项目")
        project.settings.word_count_target = 10000
        
        # 0字数
        project.word_count = 0
        assert project.calculate_completion_percentage() == 0.0
        
        # 50%完成
        project.word_count = 5000
        assert project.calculate_completion_percentage() == 50.0
        
        # 100%完成
        project.word_count = 10000
        assert project.calculate_completion_percentage() == 100.0
        
        # 超过目标
        project.word_count = 15000
        assert project.calculate_completion_percentage() == 100.0  # 最大100%
        
        # 目标为0
        project.settings.word_count_target = 0
        assert project.calculate_completion_percentage() == 0.0
    
    def test_update_completion_percentage(self):
        """测试更新完成百分比"""
        project = WritingProject(name="测试项目")
        project.settings.word_count_target = 10000
        project.word_count = 3000
        
        project.update_completion_percentage()
        
        assert project.completion_percentage == 30.0
    
    def test_mark_opened(self):
        """测试标记项目已打开"""
        project = WritingProject(name="测试项目")
        assert project.last_opened_at is None
        
        project.mark_opened()
        
        assert project.last_opened_at is not None
        assert isinstance(project.last_opened_at, datetime)
    
    def test_add_writing_time(self):
        """测试添加写作时间"""
        project = WritingProject(name="测试项目")
        original_time = project.writing_time_minutes
        original_version = project.version
        
        project.add_writing_time(30)
        
        assert project.writing_time_minutes == original_time + 30
        assert project.version == original_version + 1
        
        # 添加0或负数时间不应该更新
        project.add_writing_time(0)
        assert project.writing_time_minutes == 30
        assert project.version == original_version + 1  # 版本不变
        
        project.add_writing_time(-10)
        assert project.writing_time_minutes == 30
    
    def test_get_daily_progress(self):
        """测试获取每日进度"""
        project = WritingProject(name="测试项目")
        project.settings.daily_word_target = 1000
        
        progress = project.get_daily_progress()
        
        assert isinstance(progress, dict)
        assert progress['target_words'] == 1000
        assert 'written_words' in progress
        assert 'percentage' in progress
        assert 'remaining_words' in progress


class TestProjectValidator:
    """ProjectValidator测试类"""
    
    def test_validate_valid_project(self):
        """测试验证有效项目"""
        project = WritingProject(
            name="有效项目",
            author="测试作者",
            description="这是一个有效的项目描述"
        )
        
        validator = ProjectValidator()
        result = validator.validate(project)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_invalid_project(self):
        """测试验证无效项目"""
        project = WritingProject(name="")  # 空名称
        project.author = "a" * 51  # 超长作者名
        project.description = "a" * 1001  # 超长描述
        project.completion_percentage = 150.0  # 无效百分比
        
        validator = ProjectValidator()
        result = validator.validate(project)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_edge_cases(self):
        """测试边界情况验证"""
        # 最大长度的有效值
        project = WritingProject(
            name="a" * 100,  # 最大长度
            author="b" * 50,  # 最大长度
            description="c" * 1000,  # 最大长度
            completion_percentage=100.0  # 最大值
        )
        
        validator = ProjectValidator()
        result = validator.validate(project)
        
        assert result.is_valid is True
