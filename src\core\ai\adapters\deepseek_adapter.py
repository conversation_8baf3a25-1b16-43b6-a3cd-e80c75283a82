"""
DeepSeek服务适配器

实现DeepSeek API的具体适配逻辑。DeepSeek API兼容OpenAI格式，因此继承OpenAI适配器。
"""

from typing import List, Optional
from ..base import AIProvider
from .openai_adapter import OpenAIAdapter


class DeepSeekAdapter(OpenAIAdapter):
    """DeepSeek服务适配器"""
    
    def __init__(self, api_key: str, model: str = "deepseek-chat", base_url: Optional[str] = None):
        # DeepSeek使用自己的provider类型，但继承OpenAI的实现
        super().__init__(api_key, model, base_url)
        self.provider = AIProvider.DEEPSEEK
        self._default_base_url = "https://api.deepseek.com/v1"
    
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        # DeepSeek的已知模型
        return [
            "deepseek-chat",
            "deepseek-coder",
            "deepseek-reasoner"
        ]
    
    def _build_system_prompt(self, context) -> str:
        """构建系统提示词"""
        base_prompt = "你是DeepSeek开发的AI助手，专门协助小说创作。请用中文回答，提供高质量的创作内容。"
        
        if context.content_type.value == "character_description":
            return base_prompt + "请专注于创作生动、立体的角色描述，注重心理刻画和个性特征。"
        elif context.content_type.value == "scene_description":
            return base_prompt + "请专注于创作富有画面感的场景描述，营造恰当的氛围和情境。"
        elif context.content_type.value == "plot_development":
            return base_prompt + "请专注于创作引人入胜的情节发展，注重逻辑性和创新性。"
        elif context.content_type.value == "dialogue":
            return base_prompt + "请专注于创作自然、符合角色性格的对话，体现人物关系和情感变化。"
        elif context.content_type.value == "content_optimization":
            return base_prompt + "请专注于优化和润色文本内容，提升文学性和可读性。"
        else:
            return base_prompt
    
    def _handle_error(self, error: Exception):
        """处理错误，更新错误消息中的服务商名称"""
        ai_error = super()._handle_error(error)
        # 将错误消息中的OpenAI替换为DeepSeek
        ai_error.args = (ai_error.args[0].replace("OpenAI", "DeepSeek"),)
        ai_error.provider = self.provider
        return ai_error
