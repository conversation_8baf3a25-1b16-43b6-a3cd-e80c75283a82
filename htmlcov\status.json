{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.6", "globals": "3426ea0d74f67ae934120dc3e6e45e09", "files": {"z_145eef247bfb46b6___init___py": {"hash": "21be3591ec847bb439d04be4ba8071fa", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997___init___py": {"hash": "6c5d211a7f9d51cda8d780cbb1252022", "index": {"url": "z_b695c9c33e1e1997___init___py.html", "file": "src\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41___init___py": {"hash": "c9c949552a7c66130b053e52d270ae17", "index": {"url": "z_ce21df766c911d41___init___py.html", "file": "src\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dc49558b138ec9b___init___py": {"hash": "c3e8b240d4df898a27d50c4ec3f2c94a", "index": {"url": "z_2dc49558b138ec9b___init___py.html", "file": "src\\core\\ai\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_708c621716977060___init___py": {"hash": "2e03c65c5ce30e2a94518786f0878721", "index": {"url": "z_708c621716977060___init___py.html", "file": "src\\core\\ai\\adapters\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_708c621716977060_anthropic_adapter_py": {"hash": "00bd067696cb7f6cb083675f6b6faf62", "index": {"url": "z_708c621716977060_anthropic_adapter_py.html", "file": "src\\core\\ai\\adapters\\anthropic_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_708c621716977060_deepseek_adapter_py": {"hash": "43bb18778441efaca5a2af4d2f1b276d", "index": {"url": "z_708c621716977060_deepseek_adapter_py.html", "file": "src\\core\\ai\\adapters\\deepseek_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_708c621716977060_openai_adapter_py": {"hash": "911cd65e67ce544389d72ca115b63fc3", "index": {"url": "z_708c621716977060_openai_adapter_py.html", "file": "src\\core\\ai\\adapters\\openai_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_708c621716977060_zhipu_adapter_py": {"hash": "153adb4d703ec431e84b9cb17c131e8b", "index": {"url": "z_708c621716977060_zhipu_adapter_py.html", "file": "src\\core\\ai\\adapters\\zhipu_adapter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dc49558b138ec9b_base_py": {"hash": "2e6a113c330c6d2e3d9a71ff27b0b9d7", "index": {"url": "z_2dc49558b138ec9b_base_py.html", "file": "src\\core\\ai\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 119, "n_excluded": 34, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2dc49558b138ec9b_manager_py": {"hash": "10a82583056be12a44bb3f18d2becfa5", "index": {"url": "z_2dc49558b138ec9b_manager_py.html", "file": "src\\core\\ai\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 132, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_de954926f2f9d31f___init___py": {"hash": "8ceb9dfbf29c2b08ba381e1abf77f6e0", "index": {"url": "z_de954926f2f9d31f___init___py.html", "file": "src\\core\\events\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259___init___py": {"hash": "fc3ba71a2d553bc935099afd96aa3504", "index": {"url": "z_2a16132bb8ee9259___init___py.html", "file": "src\\core\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_base_py": {"hash": "72425da88bbe3c7354c5d8e754a6a54f", "index": {"url": "z_2a16132bb8ee9259_base_py.html", "file": "src\\core\\models\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 4, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_character_py": {"hash": "796677cb15c747d787f5bc6a38af94b2", "index": {"url": "z_2a16132bb8ee9259_character_py.html", "file": "src\\core\\models\\character.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 229, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_enums_py": {"hash": "db46843352afe0f7d4f10413c2359296", "index": {"url": "z_2a16132bb8ee9259_enums_py.html", "file": "src\\core\\models\\enums.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_event_py": {"hash": "35957ddf1cbd252bf9d24f8ea032e4b3", "index": {"url": "z_2a16132bb8ee9259_event_py.html", "file": "src\\core\\models\\event.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_project_py": {"hash": "826ed70e9d5f0bf1b3f06a675988f38f", "index": {"url": "z_2a16132bb8ee9259_project_py.html", "file": "src\\core\\models\\project.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 156, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_scene_py": {"hash": "57c3a4d5296232d0c4483b5c2b34f55e", "index": {"url": "z_2a16132bb8ee9259_scene_py.html", "file": "src\\core\\models\\scene.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2a16132bb8ee9259_story_element_py": {"hash": "1ded29a4a66537d5bec1b8db58f4ba84", "index": {"url": "z_2a16132bb8ee9259_story_element_py.html", "file": "src\\core\\models\\story_element.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18___init___py": {"hash": "ea5351eda9a39ed1b022747f9123fbea", "index": {"url": "z_60458b4be718ff18___init___py.html", "file": "src\\core\\storage\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_backup_py": {"hash": "64df74163b1f567488d2d7f92beb45cd", "index": {"url": "z_60458b4be718ff18_backup_py.html", "file": "src\\core\\storage\\backup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 176, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_cache_py": {"hash": "3c46cd33601225ff2045e0db713910e0", "index": {"url": "z_60458b4be718ff18_cache_py.html", "file": "src\\core\\storage\\cache.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_file_storage_py": {"hash": "91ba546aea50aff0985091e9324fd2a3", "index": {"url": "z_60458b4be718ff18_file_storage_py.html", "file": "src\\core\\storage\\file_storage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_manager_py": {"hash": "01c79e34b7584b86e91aaa72faec341c", "index": {"url": "z_60458b4be718ff18_manager_py.html", "file": "src\\core\\storage\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 227, "n_excluded": 0, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_serializer_py": {"hash": "0e1dab62ca76832c0e6ab266ac555bf6", "index": {"url": "z_60458b4be718ff18_serializer_py.html", "file": "src\\core\\storage\\serializer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 158, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_60458b4be718ff18_version_control_py": {"hash": "ef7c95e57811223f37b06393673ff68f", "index": {"url": "z_60458b4be718ff18_version_control_py.html", "file": "src\\core\\storage\\version_control.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 149, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15___init___py": {"hash": "c22d108f4ec8fed645348507b7e4adb9", "index": {"url": "z_fb48bbbbe924ff15___init___py.html", "file": "src\\core\\validation\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15_business_rules_py": {"hash": "a02fd3cc3242956b151a3170a8b37f6f", "index": {"url": "z_fb48bbbbe924ff15_business_rules_py.html", "file": "src\\core\\validation\\business_rules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 201, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15_exceptions_py": {"hash": "b55f532b1b63e9d292b2f4d41d49b274", "index": {"url": "z_fb48bbbbe924ff15_exceptions_py.html", "file": "src\\core\\validation\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15_relationship_validators_py": {"hash": "5b6a10289<PERSON>de5a385d5cfdac4d40822", "index": {"url": "z_fb48bbbbe924ff15_relationship_validators_py.html", "file": "src\\core\\validation\\relationship_validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 0, "n_missing": 149, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15_rules_py": {"hash": "3e8008cf066aa7087bea4759d9b3f600", "index": {"url": "z_fb48bbbbe924ff15_rules_py.html", "file": "src\\core\\validation\\rules.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 272, "n_excluded": 0, "n_missing": 224, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb48bbbbe924ff15_validators_py": {"hash": "337840701f9b0e790a9c82e3f7a0f6bc", "index": {"url": "z_fb48bbbbe924ff15_validators_py.html", "file": "src\\core\\validation\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 28, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "ee70e60b7f2ff0f1bbf03789a66dc0c4", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 2, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9___init___py": {"hash": "42ef76a1debe0469efb3c051d9ed64ce", "index": {"url": "z_d8dae034b85f0cf9___init___py.html", "file": "src\\ui\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_ai_assistant_py": {"hash": "aee478a431e3bac2b515cb1d006466e5", "index": {"url": "z_d8dae034b85f0cf9_ai_assistant_py.html", "file": "src\\ui\\ai_assistant.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 216, "n_excluded": 0, "n_missing": 216, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_character_manager_py": {"hash": "cd0c881f05caa41a3c1a1f67b30649e1", "index": {"url": "z_d8dae034b85f0cf9_character_manager_py.html", "file": "src\\ui\\character_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 325, "n_excluded": 0, "n_missing": 325, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_event_manager_py": {"hash": "1c1679557ba1f14d9513e55278c4892d", "index": {"url": "z_d8dae034b85f0cf9_event_manager_py.html", "file": "src\\ui\\event_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 294, "n_excluded": 0, "n_missing": 294, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_main_window_py": {"hash": "1515384a740ee288cb1df9a86f4af8e6", "index": {"url": "z_d8dae034b85f0cf9_main_window_py.html", "file": "src\\ui\\main_window.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 205, "n_excluded": 7, "n_missing": 205, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_project_dialog_py": {"hash": "e175f057f960f118a7c9c2c28ef39821", "index": {"url": "z_d8dae034b85f0cf9_project_dialog_py.html", "file": "src\\ui\\project_dialog.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 197, "n_excluded": 0, "n_missing": 197, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_scene_manager_py": {"hash": "40dce8bdd531d093bb5f9696119f2e98", "index": {"url": "z_d8dae034b85f0cf9_scene_manager_py.html", "file": "src\\ui\\scene_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 306, "n_excluded": 0, "n_missing": 306, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_settings_dialog_py": {"hash": "d578d289b91e5e37551ccc1f41f09fbd", "index": {"url": "z_d8dae034b85f0cf9_settings_dialog_py.html", "file": "src\\ui\\settings_dialog.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 283, "n_excluded": 0, "n_missing": 283, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_writing_interface_py": {"hash": "111c267ed5bf425a26dcee70ecdfb10d", "index": {"url": "z_d8dae034b85f0cf9_writing_interface_py.html", "file": "src\\ui\\writing_interface.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 241, "n_excluded": 0, "n_missing": 241, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6156a86a215061be___init___py": {"hash": "8a8e77bfd4a72d89c9bb30a85355cb2f", "index": {"url": "z_6156a86a215061be___init___py.html", "file": "src\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}