"""
关系验证器测试模块
"""

import pytest
from uuid import uuid4

from src.core.validation.relationship_validators import (
    RelationshipValidator, CharacterRelationshipValidator,
    SceneCharacterValidator, EventParticipantValidator
)
from src.core.validation.exceptions import ValidationError
from src.core.models.character import Character, CharacterRelationship
from src.core.models.scene import Scene
from src.core.models.event import Event, EventParticipation
from src.core.models.enums import (
    RelationType, ParticipationRole, SceneType, EventImportance
)


@pytest.fixture
def sample_characters():
    """创建示例角色列表"""
    char1 = Character(name="角色1", full_name="角色1全名")
    char2 = Character(name="角色2", full_name="角色2全名")
    char3 = Character(name="角色3", full_name="角色3全名")
    return [char1, char2, char3]


@pytest.fixture
def sample_scene():
    """创建示例场景"""
    return Scene(
        name="测试场景",
        scene_type=SceneType.INDOOR,
        location="测试地点"
    )


@pytest.fixture
def sample_event():
    """创建示例事件"""
    return Event(
        name="测试事件",
        importance=EventImportance.HIGH,
        timeline_position=1
    )


class TestRelationshipValidator:
    """关系验证器基类测试"""
    
    def test_init(self):
        """测试初始化"""
        validator = RelationshipValidator()
        assert validator is not None
        
    def test_validate_entity_existence(self, sample_characters):
        """测试实体存在性验证"""
        validator = RelationshipValidator()
        
        # 测试存在的实体
        char1 = sample_characters[0]
        assert validator.validate_entity_existence(char1.id, sample_characters) is True
        
        # 测试不存在的实体
        nonexistent_id = uuid4()
        assert validator.validate_entity_existence(nonexistent_id, sample_characters) is False
        
    def test_validate_circular_reference(self, sample_characters):
        """测试循环引用验证"""
        validator = RelationshipValidator()
        
        char1, char2, char3 = sample_characters
        
        # 创建循环引用：char1 -> char2 -> char3 -> char1
        relationships = [
            (char1.id, char2.id),
            (char2.id, char3.id),
            (char3.id, char1.id)
        ]
        
        # 应该检测到循环引用
        has_cycle = validator.has_circular_reference(relationships)
        assert has_cycle is True
        
        # 创建无循环引用的关系
        non_circular_relationships = [
            (char1.id, char2.id),
            (char2.id, char3.id)
        ]
        
        has_cycle = validator.has_circular_reference(non_circular_relationships)
        assert has_cycle is False


class TestCharacterRelationshipValidator:
    """角色关系验证器测试"""
    
    def test_init(self):
        """测试初始化"""
        validator = CharacterRelationshipValidator()
        assert validator is not None
        
    def test_validate_relationship_valid(self, sample_characters):
        """测试验证有效关系"""
        validator = CharacterRelationshipValidator()
        char1, char2 = sample_characters[:2]
        
        # 创建有效关系
        relationship = CharacterRelationship(
            character_id=char2.id,
            relation_type=RelationType.FRIEND,
            intimacy_level=0.7,
            trust_level=0.8,
            conflict_level=0.2
        )
        
        # 应该验证成功
        result = validator.validate_relationship(char1, relationship, sample_characters)
        assert result is True
        
    def test_validate_relationship_nonexistent_character(self, sample_characters):
        """测试验证不存在角色的关系"""
        validator = CharacterRelationshipValidator()
        char1 = sample_characters[0]
        
        # 创建指向不存在角色的关系
        relationship = CharacterRelationship(
            character_id=uuid4(),  # 不存在的角色ID
            relation_type=RelationType.FRIEND,
            intimacy_level=0.7,
            trust_level=0.8,
            conflict_level=0.2
        )
        
        # 应该验证失败
        with pytest.raises(ValidationError, match="关系中引用的角色不存在"):
            validator.validate_relationship(char1, relationship, sample_characters)
            
    def test_validate_self_relationship(self, sample_characters):
        """测试验证自我关系"""
        validator = CharacterRelationshipValidator()
        char1 = sample_characters[0]
        
        # 创建自我关系
        relationship = CharacterRelationship(
            character_id=char1.id,  # 指向自己
            relation_type=RelationType.FRIEND,
            intimacy_level=0.7,
            trust_level=0.8,
            conflict_level=0.2
        )
        
        # 应该验证失败
        with pytest.raises(ValidationError, match="角色不能与自己建立关系"):
            validator.validate_relationship(char1, relationship, sample_characters)
            
    def test_validate_relationship_consistency(self, sample_characters):
        """测试关系一致性验证"""
        validator = CharacterRelationshipValidator()
        char1, char2 = sample_characters[:2]
        
        # 创建不一致的关系（高亲密度但高冲突）
        relationship = CharacterRelationship(
            character_id=char2.id,
            relation_type=RelationType.FRIEND,
            intimacy_level=0.9,  # 高亲密度
            trust_level=0.8,
            conflict_level=0.9   # 高冲突
        )
        
        # 应该有警告但不失败
        result = validator.validate_relationship(char1, relationship, sample_characters)
        assert result is True  # 不阻止，但可能有警告
        
    def test_validate_duplicate_relationships(self, sample_characters):
        """测试重复关系验证"""
        validator = CharacterRelationshipValidator()
        char1, char2 = sample_characters[:2]
        
        # 创建重复关系
        relationship1 = CharacterRelationship(
            character_id=char2.id,
            relation_type=RelationType.FRIEND,
            intimacy_level=0.7,
            trust_level=0.8,
            conflict_level=0.2
        )
        
        relationship2 = CharacterRelationship(
            character_id=char2.id,  # 同一个角色
            relation_type=RelationType.ENEMY,  # 不同类型
            intimacy_level=0.2,
            trust_level=0.1,
            conflict_level=0.9
        )
        
        char1.relationships = [relationship1, relationship2]
        
        # 应该检测到重复关系
        with pytest.raises(ValidationError, match="与同一角色存在多个关系"):
            validator.validate_all_relationships(char1, sample_characters)


class TestSceneCharacterValidator:
    """场景角色验证器测试"""
    
    def test_init(self):
        """测试初始化"""
        validator = SceneCharacterValidator()
        assert validator is not None
        
    def test_validate_character_presence_valid(self, sample_scene, sample_characters):
        """测试验证有效的角色出现"""
        validator = SceneCharacterValidator()
        
        # 添加存在的角色到场景
        char1, char2 = sample_characters[:2]
        sample_scene.characters = [str(char1.id), str(char2.id)]
        
        # 应该验证成功
        result = validator.validate_character_presence(sample_scene, sample_characters)
        assert result is True
        
    def test_validate_character_presence_invalid(self, sample_scene, sample_characters):
        """测试验证无效的角色出现"""
        validator = SceneCharacterValidator()
        
        # 添加不存在的角色到场景
        sample_scene.characters = [str(uuid4())]
        
        # 应该验证失败
        with pytest.raises(ValidationError, match="场景中包含不存在的角色"):
            validator.validate_character_presence(sample_scene, sample_characters)
            
    def test_validate_empty_scene(self, sample_scene, sample_characters):
        """测试验证空场景"""
        validator = SceneCharacterValidator()
        
        # 空场景（没有角色）
        sample_scene.characters = []
        
        # 应该有警告但不失败
        result = validator.validate_character_presence(sample_scene, sample_characters)
        assert result is True  # 允许空场景，但可能有警告
        
    def test_validate_scene_character_consistency(self, sample_scene, sample_characters):
        """测试场景角色一致性验证"""
        validator = SceneCharacterValidator()
        
        # 添加角色到场景
        char1, char2, char3 = sample_characters
        sample_scene.characters = [str(char1.id), str(char2.id), str(char3.id)]
        
        # 验证角色数量是否合理
        result = validator.validate_scene_character_count(sample_scene)
        assert result is True
        
        # 添加过多角色
        many_characters = [str(uuid4()) for _ in range(20)]
        sample_scene.characters = many_characters
        
        # 应该有警告
        result = validator.validate_scene_character_count(sample_scene)
        # 这里可能返回True但有警告，具体取决于实现


class TestEventParticipantValidator:
    """事件参与者验证器测试"""
    
    def test_init(self):
        """测试初始化"""
        validator = EventParticipantValidator()
        assert validator is not None
        
    def test_validate_participant_existence_valid(self, sample_event, sample_characters):
        """测试验证有效的参与者存在性"""
        validator = EventParticipantValidator()
        
        # 创建有效的参与者
        char1, char2 = sample_characters[:2]
        participation1 = EventParticipation(
            character_id=char1.id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8
        )
        participation2 = EventParticipation(
            character_id=char2.id,
            role=ParticipationRole.SUPPORTING,
            involvement_level=0.6
        )
        
        sample_event.participants = [participation1, participation2]
        
        # 应该验证成功
        result = validator.validate_participant_existence(sample_event, sample_characters)
        assert result is True
        
    def test_validate_participant_existence_invalid(self, sample_event, sample_characters):
        """测试验证无效的参与者存在性"""
        validator = EventParticipantValidator()
        
        # 创建指向不存在角色的参与者
        participation = EventParticipation(
            character_id=uuid4(),  # 不存在的角色ID
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8
        )
        
        sample_event.participants = [participation]
        
        # 应该验证失败
        with pytest.raises(ValidationError, match="事件参与者中包含不存在的角色"):
            validator.validate_participant_existence(sample_event, sample_characters)
            
    def test_validate_participation_roles(self, sample_event, sample_characters):
        """测试验证参与角色"""
        validator = EventParticipantValidator()
        
        # 创建多个主角参与者
        char1, char2 = sample_characters[:2]
        participation1 = EventParticipation(
            character_id=char1.id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8
        )
        participation2 = EventParticipation(
            character_id=char2.id,
            role=ParticipationRole.PROTAGONIST,  # 另一个主角
            involvement_level=0.8
        )
        
        sample_event.participants = [participation1, participation2]
        
        # 应该有警告（一个事件中有多个主角可能不合理）
        result = validator.validate_participation_roles(sample_event)
        assert result is True  # 允许但可能有警告
        
    def test_validate_involvement_levels(self, sample_event, sample_characters):
        """测试验证参与程度"""
        validator = EventParticipantValidator()
        
        # 创建参与程度不合理的参与者
        char1 = sample_characters[0]
        participation = EventParticipation(
            character_id=char1.id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.1  # 主角但参与程度很低
        )
        
        sample_event.participants = [participation]
        
        # 应该有警告
        result = validator.validate_involvement_consistency(sample_event)
        assert result is True  # 允许但可能有警告
