"""场景模型测试

测试场景相关的数据模型，包括：
- SceneLocation
- SceneTime
- SceneEnvironment
- SceneAtmosphere
- Scene
- SceneValidator
"""

import pytest
from uuid import uuid4

from src.core.models.scene import (
    SceneLocation, SceneTime, SceneEnvironment, SceneAtmosphere,
    Scene, SceneValidator
)
from src.core.models.enums import (
    LocationType, SceneFunction, ElementType, ImportanceLevel
)


class TestSceneLocation:
    """测试场景位置"""
    
    def test_init_default(self):
        """测试默认初始化"""
        location = SceneLocation()
        
        assert location.name == ""
        assert location.location_type == LocationType.INDOOR
        assert location.address is None
        assert location.coordinates is None
        assert location.parent_location is None
        assert location.sub_locations == []
        assert location.size_description == ""
        assert location.layout_description == ""
        assert location.accessibility == []
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        location = SceneLocation(
            name="咖啡厅",
            location_type=LocationType.INDOOR,
            address="市中心商业街123号",
            coordinates=(116.4074, 39.9042),
            size_description="中等大小",
            layout_description="开放式布局",
            accessibility=["轮椅可达", "公交便利"]
        )
        
        assert location.name == "咖啡厅"
        assert location.location_type == LocationType.INDOOR.value
        assert location.address == "市中心商业街123号"
        assert location.coordinates == (116.4074, 39.9042)
        assert location.size_description == "中等大小"
        assert location.layout_description == "开放式布局"
        assert "轮椅可达" in location.accessibility
    
    def test_validate_name_length(self):
        """测试名称长度验证"""
        with pytest.raises(ValueError, match="位置名称不能超过100个字符"):
            SceneLocation(name="a" * 101)


class TestSceneTime:
    """测试场景时间"""
    
    def test_init_default(self):
        """测试默认初始化"""
        time_setting = SceneTime()
        
        assert time_setting.time_period == ""
        assert time_setting.season is None
        assert time_setting.time_of_day is None
        assert time_setting.specific_time is None
        assert time_setting.duration_description == ""
        assert time_setting.time_flow == "normal"
        assert time_setting.temporal_mood == ""
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        time_setting = SceneTime(
            time_period="现代",
            season="春天",
            time_of_day="黄昏",
            duration_description="约一小时",
            time_flow="slow",
            temporal_mood="宁静"
        )
        
        assert time_setting.time_period == "现代"
        assert time_setting.season == "春天"
        assert time_setting.time_of_day == "黄昏"
        assert time_setting.duration_description == "约一小时"
        assert time_setting.time_flow == "slow"
        assert time_setting.temporal_mood == "宁静"


class TestSceneEnvironment:
    """测试场景环境"""
    
    def test_init_default(self):
        """测试默认初始化"""
        environment = SceneEnvironment()
        
        assert environment.weather is None
        assert environment.temperature is None
        assert environment.humidity is None
        assert environment.lighting is None
        assert environment.light_source == []
        assert environment.shadows == ""
        assert environment.sounds == []
        assert environment.smells == []
        assert environment.textures == []
        assert environment.colors == []
        assert environment.air_quality is None
        assert environment.visibility is None
        assert environment.environmental_hazards == []
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        environment = SceneEnvironment(
            weather="晴朗",
            temperature="温暖",
            lighting="柔和",
            light_source=["阳光", "台灯"],
            sounds=["鸟鸣", "风声"],
            smells=["花香", "咖啡香"],
            colors=["金黄", "翠绿"]
        )
        
        assert environment.weather == "晴朗"
        assert environment.temperature == "温暖"
        assert environment.lighting == "柔和"
        assert "阳光" in environment.light_source
        assert "鸟鸣" in environment.sounds
        assert "花香" in environment.smells
        assert "金黄" in environment.colors


class TestSceneAtmosphere:
    """测试场景氛围"""
    
    def test_init_default(self):
        """测试默认初始化"""
        atmosphere = SceneAtmosphere()
        
        assert atmosphere.emotional_tone == ""
        assert atmosphere.mood_descriptors == []
        assert atmosphere.tension_level == 0.5
        assert atmosphere.symbolic_elements == []
        assert atmosphere.metaphorical_descriptions == []
        assert atmosphere.overall_impression == ""
        assert atmosphere.dominant_sensory_element == ""
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        atmosphere = SceneAtmosphere(
            emotional_tone="温馨",
            mood_descriptors=["舒适", "放松"],
            tension_level=0.2,
            symbolic_elements=["暖光", "绿植"],
            overall_impression="家的感觉"
        )
        
        assert atmosphere.emotional_tone == "温馨"
        assert "舒适" in atmosphere.mood_descriptors
        assert atmosphere.tension_level == 0.2
        assert "暖光" in atmosphere.symbolic_elements
        assert atmosphere.overall_impression == "家的感觉"
    
    def test_validate_tension_level(self):
        """测试紧张度验证"""
        with pytest.raises(ValueError, match="紧张度必须在0.0-1.0之间"):
            SceneAtmosphere(tension_level=1.5)
        
        with pytest.raises(ValueError, match="紧张度必须在0.0-1.0之间"):
            SceneAtmosphere(tension_level=-0.1)


class TestScene:
    """测试场景模型"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        scene = Scene()

        assert scene.element_type.value == ElementType.SCENE.value
        assert scene.name == "未命名场景"
        assert scene.description == ""
        assert scene.scene_function.value == SceneFunction.SETTING.value
        assert scene.recurring is False
        assert scene.typical_characters == []
        assert scene.typical_events == []
        assert scene.usage_count == 0
        assert scene.chapters_used == []
        assert isinstance(scene.location, SceneLocation)
        assert isinstance(scene.time_setting, SceneTime)
        assert isinstance(scene.environment, SceneEnvironment)
        assert isinstance(scene.atmosphere, SceneAtmosphere)

    def test_init_with_name(self):
        """测试带名称初始化"""
        scene = Scene(name="图书馆")

        assert scene.name == "图书馆"
        assert scene.element_type.value == ElementType.SCENE.value
    
    def test_init_full(self):
        """测试完整初始化"""
        location = SceneLocation(name="古老图书馆", location_type=LocationType.INDOOR)
        
        scene = Scene(
            name="神秘图书馆",
            description="充满魔法气息的古老图书馆",
            location=location,
            scene_function=SceneFunction.ACTION,
            recurring=True,
            detailed_description="这是一个详细的场景描述"
        )
        
        assert scene.name == "神秘图书馆"
        assert scene.description == "充满魔法气息的古老图书馆"
        assert scene.location.name == "古老图书馆"
        assert scene.scene_function == SceneFunction.ACTION.value
        assert scene.recurring is True
        assert scene.detailed_description == "这是一个详细的场景描述"
    
    def test_add_character(self):
        """测试添加角色"""
        scene = Scene(name="测试场景")
        character_id = uuid4()
        
        scene.add_character(character_id)
        
        assert character_id in scene.typical_characters
        assert len(scene.typical_characters) == 1
        
        # 重复添加不应该增加数量
        scene.add_character(character_id)
        assert len(scene.typical_characters) == 1
    
    def test_remove_character(self):
        """测试移除角色"""
        scene = Scene(name="测试场景")
        character_id = uuid4()
        
        # 先添加角色
        scene.add_character(character_id)
        assert character_id in scene.typical_characters
        
        # 移除角色
        scene.remove_character(character_id)
        assert character_id not in scene.typical_characters
        assert len(scene.typical_characters) == 0
    
    def test_add_event(self):
        """测试添加事件"""
        scene = Scene(name="测试场景")
        event_id = uuid4()
        
        scene.add_event(event_id)
        
        assert event_id in scene.typical_events
        assert len(scene.typical_events) == 1
    
    def test_use_in_chapter(self):
        """测试在章节中使用"""
        scene = Scene(name="测试场景")
        chapter_id = uuid4()
        
        initial_count = scene.usage_count
        scene.use_in_chapter(chapter_id)
        
        assert chapter_id in scene.chapters_used
        assert scene.usage_count == initial_count + 1
        
        # 重复使用同一章节
        scene.use_in_chapter(chapter_id)
        assert scene.usage_count == initial_count + 2  # 使用次数增加
        assert len(scene.chapters_used) == 1  # 但章节列表不重复
    
    def test_get_atmosphere_score(self):
        """测试获取氛围评分"""
        scene = Scene(name="测试场景")
        
        # 空氛围评分应该很低
        score = scene.get_atmosphere_score()
        assert score == 0.0
        
        # 添加氛围元素
        scene.atmosphere.emotional_tone = "神秘"
        scene.atmosphere.mood_descriptors = ["阴暗", "古老"]
        scene.atmosphere.overall_impression = "令人敬畏"
        scene.environment.sounds = ["风声", "脚步声"]
        scene.environment.colors = ["深蓝", "金黄"]
        
        score = scene.get_atmosphere_score()
        assert score > 0.5
    
    def test_validate_model(self):
        """测试模型验证"""
        scene = Scene(name="测试场景")
        result = scene.validate_model()
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_invalid_scene(self):
        """测试验证无效场景"""
        scene = Scene(name="测试场景")
        
        # 直接修改字段为无效值
        scene.__dict__['name'] = ""  # 空名称
        scene.__dict__['description'] = "a" * 1001  # 超长描述
        scene.__dict__['detailed_description'] = "b" * 5001  # 超长详细描述
        scene.__dict__['usage_count'] = -1  # 负数使用次数
        scene.atmosphere.__dict__['tension_level'] = 1.5  # 无效紧张度
        
        result = scene.validate_model()
        
        assert result.is_valid is False
        assert len(result.errors) > 0


class TestSceneValidator:
    """测试场景验证器"""
    
    def test_validate_valid_scene(self):
        """测试验证有效场景"""
        scene = Scene(
            name="完整场景",
            description="这是一个完整的场景描述",
            detailed_description="详细的场景描述"
        )
        scene.atmosphere.emotional_tone = "温馨"
        scene.location.name = "咖啡厅"
        
        validator = SceneValidator()
        result = validator.validate(scene)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_incomplete_scene(self):
        """测试验证不完整场景"""
        scene = Scene(name="不完整场景")
        # 缺少描述和氛围设定
        
        validator = SceneValidator()
        result = validator.validate(scene)
        
        # 应该有警告但不是错误
        assert len(result.warnings) > 0
