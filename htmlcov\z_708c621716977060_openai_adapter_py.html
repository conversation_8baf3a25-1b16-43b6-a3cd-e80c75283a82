<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\core\ai\adapters\openai_adapter.py: 70%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\core\ai\adapters\openai_adapter.py</b>:
            <span class="pc_cov">70%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">103 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">72<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">31<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_708c621716977060_deepseek_adapter_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_708c621716977060_zhipu_adapter_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">OpenAI&#26381;&#21153;&#36866;&#37197;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">&#23454;&#29616;OpenAI API&#30340;&#20855;&#20307;&#36866;&#37197;&#36923;&#36753;&#65292;&#21253;&#25324;&#32842;&#22825;&#23436;&#25104;&#12289;&#27969;&#24335;&#21709;&#24212;&#12289;&#38169;&#35823;&#22788;&#29702;&#31561;&#21151;&#33021;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">asyncio</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">time</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">AsyncGenerator</span><span class="op">,</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">uuid</span> <span class="key">import</span> <span class="nam">uuid4</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="key">from</span> <span class="nam">openai</span> <span class="key">import</span> <span class="nam">AsyncOpenAI</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">    <span class="key">from</span> <span class="nam">openai</span><span class="op">.</span><span class="nam">types</span><span class="op">.</span><span class="nam">chat</span> <span class="key">import</span> <span class="nam">ChatCompletion</span><span class="op">,</span> <span class="nam">ChatCompletionChunk</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="key">from</span> <span class="nam">openai</span> <span class="key">import</span> <span class="nam">APIError</span><span class="op">,</span> <span class="nam">AuthenticationError</span> <span class="key">as</span> <span class="nam">OpenAIAuthError</span><span class="op">,</span> <span class="nam">RateLimitError</span> <span class="key">as</span> <span class="nam">OpenAIRateLimit</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="nam">OPENAI_AVAILABLE</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">except</span> <span class="nam">ImportError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="nam">OPENAI_AVAILABLE</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">AIService</span><span class="op">,</span> <span class="nam">AIProvider</span><span class="op">,</span> <span class="nam">GenerationContext</span><span class="op">,</span> <span class="nam">GenerationOptions</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">GenerationResult</span><span class="op">,</span> <span class="nam">StreamChunk</span><span class="op">,</span> <span class="nam">AIServiceError</span><span class="op">,</span> <span class="nam">AuthenticationError</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">RateLimitError</span><span class="op">,</span> <span class="nam">QuotaExceededError</span><span class="op">,</span> <span class="nam">ModelNotFoundError</span><span class="op">,</span> <span class="nam">ContentFilterError</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t"><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t"><span class="key">class</span> <span class="nam">OpenAIAdapter</span><span class="op">(</span><span class="nam">AIService</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="str">"""OpenAI&#26381;&#21153;&#36866;&#37197;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">api_key</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="str">"gpt-4o-mini"</span><span class="op">,</span> <span class="nam">base_url</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">OPENAI_AVAILABLE</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ImportError</span><span class="op">(</span><span class="str">"OpenAI&#24211;&#26410;&#23433;&#35013;&#65292;&#35831;&#36816;&#34892;: pip install openai"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">super</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">__init__</span><span class="op">(</span><span class="nam">AIProvider</span><span class="op">.</span><span class="nam">OPENAI</span><span class="op">,</span> <span class="nam">api_key</span><span class="op">,</span> <span class="nam">model</span><span class="op">,</span> <span class="nam">base_url</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_default_base_url</span> <span class="op">=</span> <span class="str">"https://api.openai.com/v1"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">initialize</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="str">"""&#21021;&#22987;&#21270;OpenAI&#23458;&#25143;&#31471;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span> <span class="op">=</span> <span class="nam">AsyncOpenAI</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">            <span class="nam">api_key</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">api_key</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">            <span class="nam">base_url</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">base_url</span> <span class="key">or</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_default_base_url</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">generate_content</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="nam">self</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="nam">prompt</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">context</span><span class="op">:</span> <span class="nam">GenerationContext</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="nam">options</span><span class="op">:</span> <span class="nam">GenerationOptions</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="op">)</span> <span class="op">-></span> <span class="nam">GenerationResult</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="str">"""&#29983;&#25104;&#20869;&#23481;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">            <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">initialize</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="nam">start_time</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">time</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">            <span class="nam">messages</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_build_messages</span><span class="op">(</span><span class="nam">prompt</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">            <span class="nam">response</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">.</span><span class="nam">chat</span><span class="op">.</span><span class="nam">completions</span><span class="op">.</span><span class="nam">create</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">                <span class="nam">model</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">                <span class="nam">messages</span><span class="op">=</span><span class="nam">messages</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                <span class="nam">max_tokens</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">max_tokens</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="nam">temperature</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">temperature</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="nam">top_p</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">top_p</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                <span class="nam">frequency_penalty</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">frequency_penalty</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                <span class="nam">presence_penalty</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">presence_penalty</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">                <span class="nam">timeout</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">timeout</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">            <span class="nam">generation_time</span> <span class="op">=</span> <span class="nam">time</span><span class="op">.</span><span class="nam">time</span><span class="op">(</span><span class="op">)</span> <span class="op">-</span> <span class="nam">start_time</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="key">return</span> <span class="nam">GenerationResult</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">                <span class="nam">id</span><span class="op">=</span><span class="nam">uuid4</span><span class="op">(</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">                <span class="nam">content</span><span class="op">=</span><span class="nam">response</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">message</span><span class="op">.</span><span class="nam">content</span> <span class="key">or</span> <span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">                <span class="nam">provider</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">                <span class="nam">model</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">                <span class="nam">usage</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">                    <span class="str">"prompt_tokens"</span><span class="op">:</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span><span class="op">.</span><span class="nam">prompt_tokens</span> <span class="key">if</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span> <span class="key">else</span> <span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">                    <span class="str">"completion_tokens"</span><span class="op">:</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span><span class="op">.</span><span class="nam">completion_tokens</span> <span class="key">if</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span> <span class="key">else</span> <span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                    <span class="str">"total_tokens"</span><span class="op">:</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span><span class="op">.</span><span class="nam">total_tokens</span> <span class="key">if</span> <span class="nam">response</span><span class="op">.</span><span class="nam">usage</span> <span class="key">else</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">                <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">                <span class="nam">finish_reason</span><span class="op">=</span><span class="nam">response</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">finish_reason</span> <span class="key">or</span> <span class="str">"unknown"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">                <span class="nam">created_at</span><span class="op">=</span><span class="nam">response</span><span class="op">.</span><span class="nam">created</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">                <span class="nam">generation_time</span><span class="op">=</span><span class="nam">generation_time</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">                <span class="nam">context</span><span class="op">=</span><span class="nam">context</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                <span class="nam">options</span><span class="op">=</span><span class="nam">options</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                <span class="nam">metadata</span><span class="op">=</span><span class="op">{</span><span class="str">"response_id"</span><span class="op">:</span> <span class="nam">response</span><span class="op">.</span><span class="nam">id</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="key">raise</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_handle_error</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">generate_content_stream</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="nam">self</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">        <span class="nam">prompt</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">        <span class="nam">context</span><span class="op">:</span> <span class="nam">GenerationContext</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="nam">options</span><span class="op">:</span> <span class="nam">GenerationOptions</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="op">)</span> <span class="op">-></span> <span class="nam">AsyncGenerator</span><span class="op">[</span><span class="nam">StreamChunk</span><span class="op">,</span> <span class="key">None</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">        <span class="str">"""&#27969;&#24335;&#29983;&#25104;&#20869;&#23481;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">            <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">initialize</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="nam">messages</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_build_messages</span><span class="op">(</span><span class="nam">prompt</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">            <span class="nam">stream</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">.</span><span class="nam">chat</span><span class="op">.</span><span class="nam">completions</span><span class="op">.</span><span class="nam">create</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">                <span class="nam">model</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">                <span class="nam">messages</span><span class="op">=</span><span class="nam">messages</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">                <span class="nam">max_tokens</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">max_tokens</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">                <span class="nam">temperature</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">temperature</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">                <span class="nam">top_p</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">top_p</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">                <span class="nam">frequency_penalty</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">frequency_penalty</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">                <span class="nam">presence_penalty</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">presence_penalty</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">                <span class="nam">timeout</span><span class="op">=</span><span class="nam">options</span><span class="op">.</span><span class="nam">timeout</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">                <span class="nam">stream</span><span class="op">=</span><span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">            <span class="nam">chunk_id</span> <span class="op">=</span> <span class="nam">uuid4</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">            <span class="nam">accumulated_content</span> <span class="op">=</span> <span class="str">""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">            <span class="key">async</span> <span class="key">for</span> <span class="nam">chunk</span> <span class="key">in</span> <span class="nam">stream</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">                <span class="key">if</span> <span class="nam">chunk</span><span class="op">.</span><span class="nam">choices</span> <span class="key">and</span> <span class="nam">chunk</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">delta</span><span class="op">.</span><span class="nam">content</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">                    <span class="nam">delta</span> <span class="op">=</span> <span class="nam">chunk</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">delta</span><span class="op">.</span><span class="nam">content</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">                    <span class="nam">accumulated_content</span> <span class="op">+=</span> <span class="nam">delta</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">                    <span class="key">yield</span> <span class="nam">StreamChunk</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">                        <span class="nam">id</span><span class="op">=</span><span class="nam">chunk_id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">                        <span class="nam">content</span><span class="op">=</span><span class="nam">accumulated_content</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">                        <span class="nam">delta</span><span class="op">=</span><span class="nam">delta</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">                        <span class="nam">finish_reason</span><span class="op">=</span><span class="nam">chunk</span><span class="op">.</span><span class="nam">choices</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">finish_reason</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">                        <span class="nam">usage</span><span class="op">=</span><span class="key">None</span>  <span class="com"># OpenAI&#22312;&#27969;&#24335;&#27169;&#24335;&#19979;&#19981;&#25552;&#20379;usage&#20449;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">                    <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">            <span class="key">raise</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_handle_error</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">validate_api_key</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">        <span class="str">"""&#39564;&#35777;API&#23494;&#38053;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">            <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">initialize</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">            <span class="com"># &#21457;&#36865;&#19968;&#20010;&#31616;&#21333;&#30340;&#35831;&#27714;&#26469;&#39564;&#35777;API&#23494;&#38053;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">            <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">.</span><span class="nam">chat</span><span class="op">.</span><span class="nam">completions</span><span class="op">.</span><span class="nam">create</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">                <span class="nam">model</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">                <span class="nam">messages</span><span class="op">=</span><span class="op">[</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"user"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="str">"test"</span><span class="op">}</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">                <span class="nam">max_tokens</span><span class="op">=</span><span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="key">except</span> <span class="nam">OpenAIAuthError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">            <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="com"># &#20854;&#20182;&#38169;&#35823;&#21487;&#33021;&#26159;&#32593;&#32476;&#38382;&#39064;&#65292;&#19981;&#19968;&#23450;&#26159;API&#23494;&#38053;&#38382;&#39064;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_available_models</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="str">"""&#33719;&#21462;&#21487;&#29992;&#27169;&#22411;&#21015;&#34920;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">            <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">initialize</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">            <span class="nam">models</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_client</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">list</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">            <span class="key">return</span> <span class="op">[</span><span class="nam">model</span><span class="op">.</span><span class="nam">id</span> <span class="key">for</span> <span class="nam">model</span> <span class="key">in</span> <span class="nam">models</span><span class="op">.</span><span class="nam">data</span> <span class="key">if</span> <span class="str">"gpt"</span> <span class="key">in</span> <span class="nam">model</span><span class="op">.</span><span class="nam">id</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="key">raise</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_handle_error</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">estimate_tokens</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">text</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">int</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">        <span class="str">"""&#20272;&#31639;&#25991;&#26412;token&#25968;&#37327;&#65288;&#31616;&#21333;&#20272;&#31639;&#65289;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">        <span class="com"># &#31616;&#21333;&#30340;token&#20272;&#31639;&#65306;&#33521;&#25991;&#32422;4&#23383;&#31526;=1token&#65292;&#20013;&#25991;&#32422;1.5&#23383;&#31526;=1token</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="com"># &#36825;&#26159;&#19968;&#20010;&#31895;&#30053;&#20272;&#31639;&#65292;&#23454;&#38469;&#24212;&#35813;&#20351;&#29992;tiktoken&#24211;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="nam">chinese_chars</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="num">1</span> <span class="key">for</span> <span class="nam">char</span> <span class="key">in</span> <span class="nam">text</span> <span class="key">if</span> <span class="str">'\u4e00'</span> <span class="op">&lt;=</span> <span class="nam">char</span> <span class="op">&lt;=</span> <span class="str">'\u9fff'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="nam">other_chars</span> <span class="op">=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">text</span><span class="op">)</span> <span class="op">-</span> <span class="nam">chinese_chars</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">        <span class="nam">estimated_tokens</span> <span class="op">=</span> <span class="nam">chinese_chars</span> <span class="op">//</span> <span class="num">1.5</span> <span class="op">+</span> <span class="nam">other_chars</span> <span class="op">//</span> <span class="num">4</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="key">return</span> <span class="nam">int</span><span class="op">(</span><span class="nam">estimated_tokens</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">    <span class="key">def</span> <span class="nam">_build_messages</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">prompt</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">GenerationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">str</span><span class="op">]</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="str">"""&#26500;&#24314;&#28040;&#24687;&#21015;&#34920;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">        <span class="nam">messages</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="com"># &#31995;&#32479;&#28040;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">        <span class="nam">system_prompt</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_build_system_prompt</span><span class="op">(</span><span class="nam">context</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="key">if</span> <span class="nam">system_prompt</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">            <span class="nam">messages</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"system"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="nam">system_prompt</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="com"># &#29992;&#25143;&#28040;&#24687;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">        <span class="nam">messages</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span><span class="str">"role"</span><span class="op">:</span> <span class="str">"user"</span><span class="op">,</span> <span class="str">"content"</span><span class="op">:</span> <span class="nam">prompt</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">        <span class="key">return</span> <span class="nam">messages</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="key">def</span> <span class="nam">_build_system_prompt</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">context</span><span class="op">:</span> <span class="nam">GenerationContext</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">        <span class="str">"""&#26500;&#24314;&#31995;&#32479;&#25552;&#31034;&#35789;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">        <span class="nam">base_prompt</span> <span class="op">=</span> <span class="str">"&#20320;&#26159;&#19968;&#20010;&#19987;&#19994;&#30340;&#23567;&#35828;&#21019;&#20316;&#21161;&#25163;&#65292;&#25797;&#38271;&#21019;&#20316;&#21508;&#31181;&#31867;&#22411;&#30340;&#23567;&#35828;&#20869;&#23481;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="key">if</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"character_description"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#29983;&#21160;&#12289;&#31435;&#20307;&#30340;&#35282;&#33394;&#25551;&#36848;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"scene_description"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#23500;&#26377;&#30011;&#38754;&#24863;&#30340;&#22330;&#26223;&#25551;&#36848;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"plot_development"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#24341;&#20154;&#20837;&#32988;&#30340;&#24773;&#33410;&#21457;&#23637;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"dialogue"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#33258;&#28982;&#12289;&#31526;&#21512;&#35282;&#33394;&#24615;&#26684;&#30340;&#23545;&#35805;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"content_optimization"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#20248;&#21270;&#21644;&#28070;&#33394;&#25991;&#26412;&#20869;&#23481;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">    <span class="key">def</span> <span class="nam">_handle_error</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">error</span><span class="op">:</span> <span class="nam">Exception</span><span class="op">)</span> <span class="op">-></span> <span class="nam">AIServiceError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">        <span class="str">"""&#22788;&#29702;&#38169;&#35823;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">        <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">error</span><span class="op">,</span> <span class="nam">OpenAIAuthError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">            <span class="key">return</span> <span class="nam">AuthenticationError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">                <span class="str">"OpenAI API&#23494;&#38053;&#26080;&#25928;&#25110;&#24050;&#36807;&#26399;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">                <span class="str">"authentication_failed"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">        <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">error</span><span class="op">,</span> <span class="nam">OpenAIRateLimit</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">            <span class="key">return</span> <span class="nam">RateLimitError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">                <span class="str">"OpenAI API&#35831;&#27714;&#39057;&#29575;&#36229;&#38480;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">                <span class="nam">retry_after</span><span class="op">=</span><span class="nam">getattr</span><span class="op">(</span><span class="nam">error</span><span class="op">,</span> <span class="str">'retry_after'</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">        <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">error</span><span class="op">,</span> <span class="nam">APIError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">            <span class="key">if</span> <span class="str">"quota"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">                <span class="key">return</span> <span class="nam">QuotaExceededError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">                    <span class="str">"OpenAI API&#37197;&#39069;&#24050;&#29992;&#23436;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">                    <span class="str">"quota_exceeded"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">            <span class="key">elif</span> <span class="str">"model"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span> <span class="key">and</span> <span class="str">"not found"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">                <span class="key">return</span> <span class="nam">ModelNotFoundError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">OpenAI&#27169;&#22411; </span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">model</span><span class="op">}</span><span class="fst"> &#19981;&#23384;&#22312;</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">                    <span class="str">"model_not_found"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">            <span class="key">elif</span> <span class="str">"content_filter"</span> <span class="key">in</span> <span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">.</span><span class="nam">lower</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">                <span class="key">return</span> <span class="nam">ContentFilterError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">                    <span class="str">"&#20869;&#23481;&#34987;OpenAI&#20869;&#23481;&#36807;&#28388;&#22120;&#25318;&#25130;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">                    <span class="str">"content_filtered"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">                <span class="key">return</span> <span class="nam">AIServiceError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">                    <span class="fst">f"</span><span class="fst">OpenAI API&#38169;&#35823;: </span><span class="op">{</span><span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">                    <span class="str">"api_error"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">                <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="key">return</span> <span class="nam">AIServiceError</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">                <span class="fst">f"</span><span class="fst">OpenAI&#26381;&#21153;&#26410;&#30693;&#38169;&#35823;: </span><span class="op">{</span><span class="nam">str</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">                <span class="str">"unknown_error"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_708c621716977060_deepseek_adapter_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_708c621716977060_zhipu_adapter_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</footer>
</body>
</html>
