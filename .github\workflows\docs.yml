# 笔落App文档构建和部署工作流
name: Documentation

on:
  push:
    branches: [ main ]
    paths:
      - 'docs/**'
      - 'src/**'
      - '.github/workflows/docs.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'docs/**'

jobs:
  # 构建文档
  build-docs:
    name: 构建文档
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install sphinx sphinx-rtd-theme sphinx-autodoc-typehints
        
    - name: 构建文档
      run: |
        cd docs
        make html
        
    - name: 上传文档
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/_build/html/

  # 部署文档到GitHub Pages
  deploy-docs:
    name: 部署文档
    needs: build-docs
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: 下载文档
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs/
        
    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: docs/
