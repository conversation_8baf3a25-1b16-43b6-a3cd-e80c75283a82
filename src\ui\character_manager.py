"""
角色管理界面模块

实现角色的创建、编辑、删除和管理功能
"""

from typing import List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QPushButton, QLabel, QGroupBox,
    QFormLayout, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QTabWidget, QFrame, QSplitter,
    QMessageBox, QDialog, QDialogButtonBox,
    QScrollArea, QGridLayout
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..core.models.project import WritingProject
from ..core.models.character import Character
from ..core.models.enums import CharacterRole, PersonalityTrait


class CharacterEditDialog(QDialog):
    """角色编辑对话框"""
    
    def __init__(self, character: Optional[Character] = None, parent=None):
        super().__init__(parent)
        self.character = character
        self.is_editing = character is not None
        
        self.setup_ui()
        if self.is_editing:
            self.load_character_data()
            
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("编辑角色" if self.is_editing else "新建角色")
        self.setModal(True)
        self.setMinimumSize(600, 700)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")
        
        # 外貌描述标签页
        appearance_tab = self.create_appearance_tab()
        tab_widget.addTab(appearance_tab, "外貌描述")
        
        # 性格特征标签页
        personality_tab = self.create_personality_tab()
        tab_widget.addTab(personality_tab, "性格特征")
        
        # 背景故事标签页
        background_tab = self.create_background_tab()
        tab_widget.addTab(background_tab, "背景故事")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 角色姓名
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入角色姓名")
        layout.addRow("姓名 *:", self.name_edit)
        
        # 角色别名
        self.alias_edit = QLineEdit()
        self.alias_edit.setPlaceholderText("别名、外号等")
        layout.addRow("别名:", self.alias_edit)
        
        # 角色类型
        self.role_combo = QComboBox()
        for role in CharacterRole:
            self.role_combo.addItem(role.value, role)
        layout.addRow("角色类型:", self.role_combo)
        
        # 年龄
        self.age_spin = QSpinBox()
        self.age_spin.setRange(0, 200)
        self.age_spin.setSuffix(" 岁")
        layout.addRow("年龄:", self.age_spin)
        
        # 性别
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["男", "女", "其他", "未知"])
        layout.addRow("性别:", self.gender_combo)
        
        # 职业
        self.occupation_edit = QLineEdit()
        self.occupation_edit.setPlaceholderText("角色的职业或身份")
        layout.addRow("职业:", self.occupation_edit)
        
        # 简介
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("角色的简要描述")
        layout.addRow("简介:", self.description_edit)
        
        return widget
        
    def create_appearance_tab(self) -> QWidget:
        """创建外貌描述标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 身高
        self.height_edit = QLineEdit()
        self.height_edit.setPlaceholderText("如：175cm")
        layout.addRow("身高:", self.height_edit)
        
        # 体重
        self.weight_edit = QLineEdit()
        self.weight_edit.setPlaceholderText("如：65kg")
        layout.addRow("体重:", self.weight_edit)
        
        # 发色
        self.hair_color_edit = QLineEdit()
        self.hair_color_edit.setPlaceholderText("如：黑色、棕色等")
        layout.addRow("发色:", self.hair_color_edit)
        
        # 眼色
        self.eye_color_edit = QLineEdit()
        self.eye_color_edit.setPlaceholderText("如：黑色、蓝色等")
        layout.addRow("眼色:", self.eye_color_edit)
        
        # 特征
        self.features_edit = QTextEdit()
        self.features_edit.setMaximumHeight(80)
        self.features_edit.setPlaceholderText("特殊的外貌特征，如疤痕、胎记等")
        layout.addRow("特征:", self.features_edit)
        
        # 服装风格
        self.clothing_edit = QTextEdit()
        self.clothing_edit.setMaximumHeight(80)
        self.clothing_edit.setPlaceholderText("角色的服装风格和偏好")
        layout.addRow("服装风格:", self.clothing_edit)
        
        # 详细描述
        self.appearance_detail_edit = QTextEdit()
        self.appearance_detail_edit.setMinimumHeight(120)
        self.appearance_detail_edit.setPlaceholderText("详细的外貌描述")
        layout.addRow("详细描述:", self.appearance_detail_edit)
        
        return widget
        
    def create_personality_tab(self) -> QWidget:
        """创建性格特征标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性格特质选择
        traits_group = QGroupBox("性格特质")
        traits_layout = QGridLayout(traits_group)
        
        self.trait_checkboxes = {}
        row, col = 0, 0
        for trait in PersonalityTrait:
            checkbox = QPushButton(trait.value)
            checkbox.setCheckable(True)
            checkbox.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 5px;
                    border: 1px solid #ced4da;
                    border-radius: 3px;
                }
                QPushButton:checked {
                    background: #007bff;
                    color: white;
                }
            """)
            
            self.trait_checkboxes[trait] = checkbox
            traits_layout.addWidget(checkbox, row, col)
            
            col += 1
            if col >= 3:
                col = 0
                row += 1
                
        layout.addWidget(traits_group)
        
        # 性格描述
        desc_group = QGroupBox("性格描述")
        desc_layout = QVBoxLayout(desc_group)
        
        self.personality_edit = QTextEdit()
        self.personality_edit.setPlaceholderText("详细描述角色的性格特点、行为习惯等")
        desc_layout.addWidget(self.personality_edit)
        
        layout.addWidget(desc_group)
        
        # 目标和动机
        motivation_group = QGroupBox("目标和动机")
        motivation_layout = QVBoxLayout(motivation_group)
        
        self.motivation_edit = QTextEdit()
        self.motivation_edit.setPlaceholderText("角色的目标、动机和内心驱动力")
        motivation_layout.addWidget(self.motivation_edit)
        
        layout.addWidget(motivation_group)
        
        return widget
        
    def create_background_tab(self) -> QWidget:
        """创建背景故事标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 出生地
        self.birthplace_edit = QLineEdit()
        self.birthplace_edit.setPlaceholderText("角色的出生地")
        layout.addRow("出生地:", self.birthplace_edit)
        
        # 家庭背景
        self.family_edit = QTextEdit()
        self.family_edit.setMaximumHeight(80)
        self.family_edit.setPlaceholderText("家庭成员、家庭状况等")
        layout.addRow("家庭背景:", self.family_edit)
        
        # 教育经历
        self.education_edit = QTextEdit()
        self.education_edit.setMaximumHeight(80)
        self.education_edit.setPlaceholderText("教育背景和学习经历")
        layout.addRow("教育经历:", self.education_edit)
        
        # 重要经历
        self.experience_edit = QTextEdit()
        self.experience_edit.setMinimumHeight(100)
        self.experience_edit.setPlaceholderText("塑造角色的重要经历和事件")
        layout.addRow("重要经历:", self.experience_edit)
        
        # 技能和能力
        self.skills_edit = QTextEdit()
        self.skills_edit.setMaximumHeight(80)
        self.skills_edit.setPlaceholderText("角色掌握的技能和特殊能力")
        layout.addRow("技能能力:", self.skills_edit)
        
        # 弱点和恐惧
        self.weaknesses_edit = QTextEdit()
        self.weaknesses_edit.setMaximumHeight(80)
        self.weaknesses_edit.setPlaceholderText("角色的弱点、恐惧和局限性")
        layout.addRow("弱点恐惧:", self.weaknesses_edit)
        
        return widget
        
    def load_character_data(self):
        """加载角色数据"""
        if not self.character:
            return
            
        # 基本信息
        self.name_edit.setText(self.character.name)
        self.alias_edit.setText(self.character.alias or "")
        
        # 设置角色类型
        for i in range(self.role_combo.count()):
            if self.role_combo.itemData(i) == self.character.role:
                self.role_combo.setCurrentIndex(i)
                break
                
        self.age_spin.setValue(self.character.age or 0)
        self.gender_combo.setCurrentText(self.character.gender or "未知")
        self.occupation_edit.setText(self.character.occupation or "")
        self.description_edit.setPlainText(self.character.description or "")
        
        # 外貌信息
        if self.character.appearance:
            appearance = self.character.appearance
            self.height_edit.setText(appearance.height or "")
            self.weight_edit.setText(appearance.weight or "")
            self.hair_color_edit.setText(appearance.hair_color or "")
            self.eye_color_edit.setText(appearance.eye_color or "")
            self.features_edit.setPlainText(appearance.distinctive_features or "")
            self.clothing_edit.setPlainText(appearance.clothing_style or "")
            self.appearance_detail_edit.setPlainText(appearance.detailed_description or "")
            
        # 性格特质
        if self.character.personality:
            for trait in self.character.personality.traits:
                if trait in self.trait_checkboxes:
                    self.trait_checkboxes[trait].setChecked(True)
                    
            self.personality_edit.setPlainText(self.character.personality.description or "")
            self.motivation_edit.setPlainText(self.character.personality.motivations or "")
            
        # 背景信息
        if self.character.background:
            background = self.character.background
            self.birthplace_edit.setText(background.birthplace or "")
            self.family_edit.setPlainText(background.family_background or "")
            self.education_edit.setPlainText(background.education or "")
            self.experience_edit.setPlainText(background.key_experiences or "")
            self.skills_edit.setPlainText(background.skills or "")
            self.weaknesses_edit.setPlainText(background.weaknesses or "")
            
    def get_character_data(self) -> dict:
        """获取角色数据"""
        # 收集性格特质
        selected_traits = []
        for trait, checkbox in self.trait_checkboxes.items():
            if checkbox.isChecked():
                selected_traits.append(trait)
                
        return {
            'name': self.name_edit.text().strip(),
            'alias': self.alias_edit.text().strip() or None,
            'role': self.role_combo.currentData(),
            'age': self.age_spin.value() if self.age_spin.value() > 0 else None,
            'gender': self.gender_combo.currentText() if self.gender_combo.currentText() != "未知" else None,
            'occupation': self.occupation_edit.text().strip() or None,
            'description': self.description_edit.toPlainText().strip() or None,
            'appearance': {
                'height': self.height_edit.text().strip() or None,
                'weight': self.weight_edit.text().strip() or None,
                'hair_color': self.hair_color_edit.text().strip() or None,
                'eye_color': self.eye_color_edit.text().strip() or None,
                'distinctive_features': self.features_edit.toPlainText().strip() or None,
                'clothing_style': self.clothing_edit.toPlainText().strip() or None,
                'detailed_description': self.appearance_detail_edit.toPlainText().strip() or None,
            },
            'personality': {
                'traits': selected_traits,
                'description': self.personality_edit.toPlainText().strip() or None,
                'motivations': self.motivation_edit.toPlainText().strip() or None,
            },
            'background': {
                'birthplace': self.birthplace_edit.text().strip() or None,
                'family_background': self.family_edit.toPlainText().strip() or None,
                'education': self.education_edit.toPlainText().strip() or None,
                'key_experiences': self.experience_edit.toPlainText().strip() or None,
                'skills': self.skills_edit.toPlainText().strip() or None,
                'weaknesses': self.weaknesses_edit.toPlainText().strip() or None,
            }
        }


class CharacterListWidget(QListWidget):
    """角色列表组件"""
    
    # 信号定义
    character_selected = pyqtSignal(Character)
    character_double_clicked = pyqtSignal(Character)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.characters: List[Character] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setAlternatingRowColors(True)
        self.setStyleSheet("""
            QListWidget {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                alternate-background-color: #f8f9fa;
            }
            
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QListWidget::item:selected {
                background: #007bff;
                color: white;
            }
            
            QListWidget::item:hover {
                background: #e3f2fd;
            }
        """)
        
        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
        
    def add_character(self, character: Character):
        """添加角色"""
        self.characters.append(character)
        
        item = QListWidgetItem()
        item.setText(f"{character.name} ({character.role.value})")
        item.setData(Qt.ItemDataRole.UserRole, character)
        
        self.addItem(item)
        
    def remove_character(self, character: Character):
        """移除角色"""
        if character in self.characters:
            self.characters.remove(character)
            
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == character:
                self.takeItem(i)
                break
                
    def update_character(self, character: Character):
        """更新角色"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.ItemDataRole.UserRole).id == character.id:
                item.setText(f"{character.name} ({character.role.value})")
                item.setData(Qt.ItemDataRole.UserRole, character)
                break
                
    def on_item_clicked(self, item: QListWidgetItem):
        """处理项目点击"""
        character = item.data(Qt.ItemDataRole.UserRole)
        if character:
            self.character_selected.emit(character)
            
    def on_item_double_clicked(self, item: QListWidgetItem):
        """处理项目双击"""
        character = item.data(Qt.ItemDataRole.UserRole)
        if character:
            self.character_double_clicked.emit(character)


class CharacterManagerWidget(QWidget):
    """角色管理主组件"""
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.current_character: Optional[Character] = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_characters()
        
    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout(self)
        
        # 左侧角色列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 角色列表标题和按钮
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("角色列表"))
        header_layout.addStretch()
        
        self.add_btn = QPushButton("新建")
        self.add_btn.setMaximumWidth(60)
        header_layout.addWidget(self.add_btn)
        
        left_layout.addLayout(header_layout)
        
        # 角色列表
        self.character_list = CharacterListWidget()
        left_layout.addWidget(self.character_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.edit_btn = QPushButton("编辑")
        self.delete_btn = QPushButton("删除")
        self.duplicate_btn = QPushButton("复制")
        
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.duplicate_btn)
        
        left_layout.addLayout(button_layout)
        
        # 右侧角色详情
        self.character_detail = QTextEdit()
        self.character_detail.setReadOnly(True)
        self.character_detail.setPlaceholderText("选择角色查看详细信息")
        
        # 添加到主布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(self.character_detail)
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
        
    def setup_connections(self):
        """设置信号连接"""
        self.add_btn.clicked.connect(self.add_character)
        self.edit_btn.clicked.connect(self.edit_character)
        self.delete_btn.clicked.connect(self.delete_character)
        self.duplicate_btn.clicked.connect(self.duplicate_character)
        
        self.character_list.character_selected.connect(self.show_character_detail)
        self.character_list.character_double_clicked.connect(self.edit_character_from_list)
        
    def load_characters(self):
        """加载角色列表"""
        # TODO: 从项目中加载角色
        pass
        
    def add_character(self):
        """添加角色"""
        dialog = CharacterEditDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            character_data = dialog.get_character_data()
            
            # 验证必填字段
            if not character_data['name']:
                QMessageBox.warning(self, "验证错误", "角色姓名不能为空")
                return
                
            # 创建角色对象
            try:
                character = Character(
                    name=character_data['name'],
                    role=character_data['role'],
                    **{k: v for k, v in character_data.items() if k not in ['name', 'role'] and v is not None}
                )
                
                # 添加到列表
                self.character_list.add_character(character)
                
                # TODO: 保存到项目
                
            except Exception as e:
                QMessageBox.critical(self, "创建失败", f"创建角色失败: {e}")
                
    def edit_character(self):
        """编辑当前选中的角色"""
        if self.current_character:
            self.edit_character_from_list(self.current_character)
            
    def edit_character_from_list(self, character: Character):
        """编辑指定角色"""
        dialog = CharacterEditDialog(character, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            character_data = dialog.get_character_data()
            
            # 更新角色数据
            try:
                # TODO: 更新角色对象
                self.character_list.update_character(character)
                self.show_character_detail(character)
                
            except Exception as e:
                QMessageBox.critical(self, "更新失败", f"更新角色失败: {e}")
                
    def delete_character(self):
        """删除角色"""
        if not self.current_character:
            return
            
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除角色 '{self.current_character.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.character_list.remove_character(self.current_character)
            self.current_character = None
            self.character_detail.clear()
            
    def duplicate_character(self):
        """复制角色"""
        if not self.current_character:
            return
            
        # TODO: 实现角色复制功能
        QMessageBox.information(self, "功能开发中", "角色复制功能正在开发中")
        
    def show_character_detail(self, character: Character):
        """显示角色详情"""
        self.current_character = character
        
        # 生成详情文本
        detail_text = f"""
<h2>{character.name}</h2>
<p><strong>角色类型:</strong> {character.role.value}</p>
"""
        
        if character.age:
            detail_text += f"<p><strong>年龄:</strong> {character.age}岁</p>"
            
        if character.gender:
            detail_text += f"<p><strong>性别:</strong> {character.gender}</p>"
            
        if character.occupation:
            detail_text += f"<p><strong>职业:</strong> {character.occupation}</p>"
            
        if character.description:
            detail_text += f"<p><strong>简介:</strong> {character.description}</p>"
            
        # TODO: 添加更多详情信息
        
        self.character_detail.setHtml(detail_text)
