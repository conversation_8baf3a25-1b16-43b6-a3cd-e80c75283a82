#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("警告:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        print(f"标准输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        return False


def run_unit_tests(coverage=True, verbose=False):
    """运行单元测试"""
    cmd = ["python", "-m", "pytest", "tests/unit/"]
    
    if coverage:
        cmd.extend(["--cov=src", "--cov-report=term-missing", "--cov-report=html"])
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
        
    return run_command(cmd, "单元测试")


def run_integration_tests(verbose=False):
    """运行集成测试"""
    cmd = ["python", "-m", "pytest", "tests/integration/", "-m", "integration"]
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
        
    return run_command(cmd, "集成测试")


def run_performance_tests(verbose=False):
    """运行性能测试"""
    cmd = ["python", "-m", "pytest", "tests/performance/", "-m", "performance"]
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
        
    return run_command(cmd, "性能测试")


def run_gui_tests(verbose=False):
    """运行GUI测试"""
    cmd = ["python", "-m", "pytest", "tests/unit/test_ui/", "-m", "gui"]
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
        
    return run_command(cmd, "GUI测试")


def run_smoke_tests():
    """运行冒烟测试"""
    cmd = ["python", "-m", "pytest", "-m", "smoke", "--tb=line"]
    return run_command(cmd, "冒烟测试")


def run_all_tests(coverage=True, verbose=False):
    """运行所有测试"""
    cmd = ["python", "-m", "pytest"]
    
    if coverage:
        cmd.extend(["--cov=src", "--cov-report=term-missing", "--cov-report=html"])
    
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
        
    return run_command(cmd, "所有测试")


def run_specific_test(test_path, verbose=False):
    """运行特定测试"""
    cmd = ["python", "-m", "pytest", test_path]
    
    if verbose:
        cmd.append("-v")
        
    return run_command(cmd, f"特定测试: {test_path}")


def generate_coverage_report():
    """生成覆盖率报告"""
    cmd = ["python", "-m", "pytest", "--cov=src", "--cov-report=html", "--cov-report=term"]
    return run_command(cmd, "生成覆盖率报告")


def check_test_quality():
    """检查测试质量"""
    print("\n检查测试质量...")
    
    # 检查测试文件数量
    test_files = list(Path("tests").rglob("test_*.py"))
    print(f"测试文件数量: {len(test_files)}")
    
    # 检查测试覆盖的模块
    src_files = list(Path("src").rglob("*.py"))
    src_modules = [f for f in src_files if not f.name.startswith("__")]
    print(f"源代码模块数量: {len(src_modules)}")
    
    # 简单的测试质量检查
    total_tests = 0
    for test_file in test_files:
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            test_count = content.count("def test_")
            total_tests += test_count
            
    print(f"总测试用例数量: {total_tests}")
    
    if total_tests > 0:
        avg_tests_per_file = total_tests / len(test_files)
        print(f"平均每个测试文件的测试用例数: {avg_tests_per_file:.1f}")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试运行脚本")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "gui", "smoke", "all"], 
                       default="all", help="测试类型")
    parser.add_argument("--no-coverage", action="store_true", help="禁用覆盖率报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--test", help="运行特定测试文件或测试用例")
    parser.add_argument("--quality-check", action="store_true", help="检查测试质量")
    parser.add_argument("--coverage-only", action="store_true", help="只生成覆盖率报告")
    
    args = parser.parse_args()
    
    # 确保在项目根目录运行
    if not Path("src").exists():
        print("错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    success = True
    
    if args.coverage_only:
        success = generate_coverage_report()
    elif args.quality_check:
        success = check_test_quality()
    elif args.test:
        success = run_specific_test(args.test, args.verbose)
    else:
        coverage = not args.no_coverage
        
        if args.type == "unit":
            success = run_unit_tests(coverage, args.verbose)
        elif args.type == "integration":
            success = run_integration_tests(args.verbose)
        elif args.type == "performance":
            success = run_performance_tests(args.verbose)
        elif args.type == "gui":
            success = run_gui_tests(args.verbose)
        elif args.type == "smoke":
            success = run_smoke_tests()
        elif args.type == "all":
            success = run_all_tests(coverage, args.verbose)
    
    if success:
        print("\n✅ 测试运行成功!")
        if not args.no_coverage and args.type in ["unit", "all"]:
            print("📊 覆盖率报告已生成: htmlcov/index.html")
    else:
        print("\n❌ 测试运行失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
