"""
主窗口模块

实现应用程序的主窗口，包括启动界面、项目管理等功能
"""

import sys
from typing import Optional
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QListWidget, QListWidgetItem,
    QStackedWidget, QFrame, QSizePolicy, QApplication,
    QMessageBox, QFileDialog, QSplashScreen
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt6.QtGui import QPixmap, QFont, QIcon, QPalette, QColor

from ..core.models.project import WritingProject
from ..core.storage.manager import StorageManager
from .project_dialog import ProjectDialog
from .writing_interface import WritingInterface
from .settings_dialog import SettingsDialog


class WelcomeWidget(QWidget):
    """欢迎界面组件"""
    
    # 信号定义
    new_project_requested = pyqtSignal()
    open_project_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    project_selected = pyqtSignal(str)  # 项目路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # 使用临时路径进行测试
        import tempfile
        self.storage_manager = StorageManager(tempfile.mkdtemp())
        self.setup_ui()
        self.load_recent_projects()
        
    def setup_ui(self):
        """设置界面"""
        self.setObjectName("WelcomeWidget")
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(50, 50, 50, 50)
        
        # 标题区域
        title_layout = self.create_title_section()
        main_layout.addLayout(title_layout)
        
        # 操作面板
        action_panel = self.create_action_panel()
        main_layout.addWidget(action_panel)
        
        # 最近项目列表
        recent_panel = self.create_recent_projects_panel()
        main_layout.addWidget(recent_panel)
        
        # 底部按钮
        bottom_layout = self.create_bottom_section()
        main_layout.addLayout(bottom_layout)
        
        # 设置样式
        self.apply_styles()
        
    def create_title_section(self) -> QVBoxLayout:
        """创建标题区域"""
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 应用标题
        title_label = QLabel("笔落 BambooFall")
        title_label.setObjectName("AppTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 副标题
        subtitle_label = QLabel("AI辅助小说创作工具")
        subtitle_label.setObjectName("AppSubtitle")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        
        return layout
        
    def create_action_panel(self) -> QFrame:
        """创建操作面板"""
        panel = QFrame()
        panel.setObjectName("ActionPanel")
        panel.setFrameStyle(QFrame.Shape.Box)
        
        layout = QHBoxLayout(panel)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 新建项目按钮
        new_btn = QPushButton("新建项目")
        new_btn.setObjectName("PrimaryButton")
        new_btn.setMinimumSize(150, 60)
        new_btn.clicked.connect(self.new_project_requested.emit)
        
        # 打开项目按钮
        open_btn = QPushButton("打开项目")
        open_btn.setObjectName("SecondaryButton")
        open_btn.setMinimumSize(150, 60)
        open_btn.clicked.connect(self.open_project_requested.emit)
        
        layout.addWidget(new_btn)
        layout.addWidget(open_btn)
        
        return panel
        
    def create_recent_projects_panel(self) -> QFrame:
        """创建最近项目面板"""
        panel = QFrame()
        panel.setObjectName("RecentPanel")
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("最近项目")
        title_label.setObjectName("SectionTitle")
        layout.addWidget(title_label)
        
        # 项目列表
        self.recent_list = QListWidget()
        self.recent_list.setObjectName("RecentList")
        self.recent_list.setMinimumHeight(200)
        self.recent_list.itemDoubleClicked.connect(self.on_project_double_clicked)
        
        layout.addWidget(self.recent_list)
        
        return panel
        
    def create_bottom_section(self) -> QHBoxLayout:
        """创建底部按钮区域"""
        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置按钮
        settings_btn = QPushButton("设置")
        settings_btn.setObjectName("BottomButton")
        settings_btn.clicked.connect(self.settings_requested.emit)
        
        # 帮助按钮
        help_btn = QPushButton("帮助")
        help_btn.setObjectName("BottomButton")
        
        # 关于按钮
        about_btn = QPushButton("关于")
        about_btn.setObjectName("BottomButton")
        about_btn.clicked.connect(self.show_about)
        
        layout.addWidget(settings_btn)
        layout.addWidget(help_btn)
        layout.addWidget(about_btn)
        
        return layout
        
    def apply_styles(self):
        """应用样式表"""
        style = """
        #WelcomeWidget {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
        }
        
        #AppTitle {
            font-size: 36px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        #AppSubtitle {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        #ActionPanel {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #dee2e6;
            border-radius: 10px;
        }
        
        #PrimaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #007bff, stop:1 #0056b3);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        
        #PrimaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0056b3, stop:1 #004085);
        }
        
        #SecondaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #6c757d, stop:1 #545b62);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
        }
        
        #SecondaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #545b62, stop:1 #3d4142);
        }
        
        #RecentPanel {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #dee2e6;
            border-radius: 10px;
        }
        
        #SectionTitle {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        
        #RecentList {
            border: 1px solid #ced4da;
            border-radius: 5px;
            background: white;
        }
        
        #BottomButton {
            background: transparent;
            border: 1px solid #6c757d;
            border-radius: 5px;
            color: #6c757d;
            padding: 8px 16px;
            margin: 0 5px;
        }
        
        #BottomButton:hover {
            background: #6c757d;
            color: white;
        }
        """
        self.setStyleSheet(style)
        
    def load_recent_projects(self):
        """加载最近项目列表"""
        try:
            # TODO: 从配置文件加载最近项目
            # 这里先添加一些示例数据
            sample_projects = [
                {"name": "玄幻小说：修仙传奇", "path": "/path/to/project1", "modified": "2024-01-15"},
                {"name": "都市小说：商业风云", "path": "/path/to/project2", "modified": "2024-01-10"},
                {"name": "科幻小说：星际征途", "path": "/path/to/project3", "modified": "2024-01-05"},
            ]
            
            for project in sample_projects:
                item = QListWidgetItem(f"{project['name']} - {project['modified']}")
                item.setData(Qt.ItemDataRole.UserRole, project['path'])
                self.recent_list.addItem(item)
                
        except Exception as e:
            print(f"加载最近项目失败: {e}")
            
    def on_project_double_clicked(self, item: QListWidgetItem):
        """处理项目双击事件"""
        project_path = item.data(Qt.ItemDataRole.UserRole)
        if project_path:
            self.project_selected.emit(project_path)
            
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于笔落App",
            "笔落 BambooFall v1.0\n\n"
            "AI辅助小说创作工具\n"
            "帮助作者更高效地进行创作\n\n"
            "© 2024 BambooFall Team"
        )


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        # 使用临时路径进行测试
        import tempfile
        self.storage_manager = StorageManager(tempfile.mkdtemp())
        self.current_project: Optional[WritingProject] = None
        self.writing_interface: Optional[WritingInterface] = None
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("笔落 BambooFall")
        self.setMinimumSize(1024, 768)
        self.resize(1440, 900)
        
        # 设置窗口图标
        # self.setWindowIcon(QIcon(":/icons/app_icon.png"))
        
        # 创建中央堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # 创建欢迎界面
        self.welcome_widget = WelcomeWidget()
        self.stacked_widget.addWidget(self.welcome_widget)
        
        # 设置状态栏
        self.statusBar().showMessage("就绪")
        
    def setup_connections(self):
        """设置信号连接"""
        self.welcome_widget.new_project_requested.connect(self.show_new_project_dialog)
        self.welcome_widget.open_project_requested.connect(self.show_open_project_dialog)
        self.welcome_widget.settings_requested.connect(self.show_settings_dialog)
        self.welcome_widget.project_selected.connect(self.open_project)
        
    def show_new_project_dialog(self):
        """显示新建项目对话框"""
        try:
            dialog = ProjectDialog(self)
            if dialog.exec() == dialog.DialogCode.Accepted:
                project_data = dialog.get_project_data()
                self.create_new_project(project_data)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建项目对话框失败: {e}")
            
    def show_open_project_dialog(self):
        """显示打开项目对话框"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "打开项目",
                "",
                "笔落项目文件 (*.bamboo);;所有文件 (*)"
            )
            
            if file_path:
                self.open_project(file_path)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败: {e}")
            
    def show_settings_dialog(self):
        """显示设置对话框"""
        try:
            dialog = SettingsDialog(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开设置失败: {e}")
            
    def create_new_project(self, project_data: dict):
        """创建新项目"""
        try:
            # 创建项目对象
            project = WritingProject(
                name=project_data['name'],
                author=project_data['author'],
                genre=project_data['genre'],
                description=project_data['description']
            )
            
            # 保存项目
            project_path = self.storage_manager.save_project(project)
            
            # 打开创作界面
            self.open_writing_interface(project)
            
            self.statusBar().showMessage(f"项目 '{project.name}' 创建成功")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建项目失败: {e}")
            
    def open_project(self, project_path: str):
        """打开项目"""
        try:
            # 加载项目
            project = self.storage_manager.load_project(project_path)
            
            # 打开创作界面
            self.open_writing_interface(project)
            
            self.statusBar().showMessage(f"项目 '{project.name}' 打开成功")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败: {e}")
            
    def open_writing_interface(self, project: WritingProject):
        """打开创作界面"""
        try:
            self.current_project = project
            
            # 创建创作界面
            if self.writing_interface:
                self.stacked_widget.removeWidget(self.writing_interface)
                
            self.writing_interface = WritingInterface(project, self)
            self.stacked_widget.addWidget(self.writing_interface)
            self.stacked_widget.setCurrentWidget(self.writing_interface)
            
            # 更新窗口标题
            self.setWindowTitle(f"笔落 BambooFall - {project.name}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开创作界面失败: {e}")
            
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        if self.current_project and self.writing_interface:
            # 询问是否保存项目
            reply = QMessageBox.question(
                self,
                "确认退出",
                "是否保存当前项目？",
                QMessageBox.StandardButton.Save | 
                QMessageBox.StandardButton.Discard | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Save:
                try:
                    self.storage_manager.save_project(self.current_project)
                    event.accept()
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"保存项目失败: {e}")
                    event.ignore()
            elif reply == QMessageBox.StandardButton.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def show_splash_screen(app: QApplication) -> QSplashScreen:
    """显示启动画面"""
    # 创建启动画面
    pixmap = QPixmap(400, 300)
    pixmap.fill(QColor(248, 249, 250))
    
    splash = QSplashScreen(pixmap)
    splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
    
    # 添加文字
    splash.showMessage(
        "笔落 BambooFall\n正在启动...",
        Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
        QColor(44, 62, 80)
    )
    
    splash.show()
    app.processEvents()
    
    return splash


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 显示启动画面
    splash = show_splash_screen(app)
    
    # 模拟启动延迟
    QTimer.singleShot(2000, splash.close)
    
    # 创建主窗口
    window = MainWindow()
    QTimer.singleShot(2000, window.show)
    
    sys.exit(app.exec())
