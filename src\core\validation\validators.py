"""核心验证器类

提供字段级验证、业务规则验证和关系验证的核心实现
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union, Callable
from uuid import UUID
from datetime import datetime

from ..models.base import BaseModel, ValidationResult
from .exceptions import (
    ValidationIssue, ValidationError, ValidationWarning, ValidationInfo,
    ValidationException, ValidationSeverity
)


class ValidationContext:
    """验证上下文
    
    提供验证过程中需要的上下文信息，如相关模型、配置等
    """
    
    def __init__(
        self,
        model: BaseModel,
        related_models: Optional[Dict[str, List[BaseModel]]] = None,
        config: Optional[Dict[str, Any]] = None,
        strict_mode: bool = False
    ):
        self.model = model
        self.related_models = related_models or {}
        self.config = config or {}
        self.strict_mode = strict_mode
        self.issues: List[ValidationIssue] = []
    
    def add_error(self, message: str, field_name: Optional[str] = None, rule_name: Optional[str] = None):
        """添加错误"""
        self.issues.append(ValidationError(message, field_name, rule_name))
    
    def add_warning(self, message: str, field_name: Optional[str] = None, rule_name: Optional[str] = None):
        """添加警告"""
        self.issues.append(ValidationWarning(message, field_name, rule_name))
    
    def add_info(self, message: str, field_name: Optional[str] = None, rule_name: Optional[str] = None):
        """添加信息"""
        self.issues.append(ValidationInfo(message, field_name, rule_name))
    
    def get_related_models(self, model_type: str) -> List[BaseModel]:
        """获取相关模型"""
        return self.related_models.get(model_type, [])
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return any(issue.severity == ValidationSeverity.ERROR for issue in self.issues)
    
    def get_validation_result(self) -> ValidationResult:
        """获取验证结果"""
        result = ValidationResult()
        for issue in self.issues:
            if issue.severity == ValidationSeverity.ERROR:
                result.add_error(str(issue))
            elif issue.severity == ValidationSeverity.WARNING:
                result.add_warning(str(issue))
        return result


class ValidationRule(ABC):
    """验证规则基类"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
    
    @abstractmethod
    def validate(self, value: Any, context: ValidationContext) -> bool:
        """验证值
        
        Args:
            value: 要验证的值
            context: 验证上下文
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    def get_error_message(self, value: Any, context: ValidationContext) -> str:
        """获取错误消息"""
        return f"验证规则 '{self.name}' 失败"


class FieldValidator:
    """字段验证器
    
    负责对单个字段进行验证
    """
    
    def __init__(self):
        self.rules: Dict[str, List[ValidationRule]] = {}
    
    def add_rule(self, field_name: str, rule: ValidationRule):
        """添加验证规则"""
        if field_name not in self.rules:
            self.rules[field_name] = []
        self.rules[field_name].append(rule)
    
    def validate_field(self, field_name: str, value: Any, context: ValidationContext) -> bool:
        """验证字段"""
        if field_name not in self.rules:
            return True
        
        all_passed = True
        for rule in self.rules[field_name]:
            try:
                if not rule.validate(value, context):
                    context.add_error(
                        rule.get_error_message(value, context),
                        field_name=field_name,
                        rule_name=rule.name
                    )
                    all_passed = False
            except Exception as e:
                context.add_error(
                    f"验证规则 '{rule.name}' 执行失败: {str(e)}",
                    field_name=field_name,
                    rule_name=rule.name
                )
                all_passed = False
        
        return all_passed
    
    def validate_model(self, model: BaseModel, context: ValidationContext) -> bool:
        """验证整个模型的字段"""
        all_passed = True
        
        # 获取模型的所有字段
        model_dict = model.dict() if hasattr(model, 'dict') else model.model_dump()
        
        for field_name, value in model_dict.items():
            if not self.validate_field(field_name, value, context):
                all_passed = False
        
        return all_passed


class BusinessRuleValidator(ABC):
    """业务规则验证器基类"""
    
    @abstractmethod
    def validate(self, model: BaseModel, context: ValidationContext) -> bool:
        """验证业务规则"""
        pass
    
    @abstractmethod
    def get_rule_name(self) -> str:
        """获取规则名称"""
        pass


class RelationshipValidator(ABC):
    """关系验证器基类"""
    
    @abstractmethod
    def validate_relationships(self, model: BaseModel, context: ValidationContext) -> bool:
        """验证模型关系"""
        pass
    
    @abstractmethod
    def get_validator_name(self) -> str:
        """获取验证器名称"""
        pass


class ValidationEngine:
    """验证引擎
    
    协调各种验证器，提供统一的验证接口
    """
    
    def __init__(self):
        self.field_validators: Dict[Type[BaseModel], FieldValidator] = {}
        self.business_rule_validators: Dict[Type[BaseModel], List[BusinessRuleValidator]] = {}
        self.relationship_validators: List[RelationshipValidator] = []
    
    def register_field_validator(self, model_type: Type[BaseModel], validator: FieldValidator):
        """注册字段验证器"""
        self.field_validators[model_type] = validator
    
    def register_business_rule_validator(self, model_type: Type[BaseModel], validator: BusinessRuleValidator):
        """注册业务规则验证器"""
        if model_type not in self.business_rule_validators:
            self.business_rule_validators[model_type] = []
        self.business_rule_validators[model_type].append(validator)
    
    def register_relationship_validator(self, validator: RelationshipValidator):
        """注册关系验证器"""
        self.relationship_validators.append(validator)
    
    def validate(
        self,
        model: BaseModel,
        related_models: Optional[Dict[str, List[BaseModel]]] = None,
        strict_mode: bool = False,
        config: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """执行完整验证"""
        context = ValidationContext(model, related_models, config, strict_mode)
        
        # 1. 字段级验证
        model_type = type(model)
        if model_type in self.field_validators:
            self.field_validators[model_type].validate_model(model, context)
        
        # 2. 业务规则验证
        if model_type in self.business_rule_validators:
            for validator in self.business_rule_validators[model_type]:
                try:
                    validator.validate(model, context)
                except Exception as e:
                    context.add_error(
                        f"业务规则验证失败: {str(e)}",
                        rule_name=validator.get_rule_name()
                    )
        
        # 3. 关系验证
        for validator in self.relationship_validators:
            try:
                validator.validate_relationships(model, context)
            except Exception as e:
                context.add_error(
                    f"关系验证失败: {str(e)}",
                    rule_name=validator.get_validator_name()
                )
        
        return context.get_validation_result()

    def validate_batch(
        self,
        models: List[BaseModel],
        related_models: Optional[Dict[str, List[BaseModel]]] = None,
        strict_mode: bool = False,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, ValidationResult]:
        """批量验证"""
        results = {}
        for i, model in enumerate(models):
            model_key = f"{type(model).__name__}_{i}"
            results[model_key] = self.validate(model, related_models, strict_mode, config)
        return results

    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, Any]:
        """获取验证结果摘要"""
        total_models = len(results)
        models_with_errors = sum(1 for result in results.values() if result.has_errors)
        models_with_warnings = sum(1 for result in results.values() if result.has_warnings)
        total_errors = sum(len(result.errors) for result in results.values())
        total_warnings = sum(len(result.warnings) for result in results.values())

        return {
            "total_models": total_models,
            "models_with_errors": models_with_errors,
            "models_with_warnings": models_with_warnings,
            "total_errors": total_errors,
            "total_warnings": total_warnings,
            "success_rate": (total_models - models_with_errors) / total_models if total_models > 0 else 1.0
        }
