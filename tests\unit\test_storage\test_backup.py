"""
备份管理器测试模块
"""

import pytest
import tempfile
import shutil
import zipfile
import json
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from src.core.storage.backup import BackupManager, BackupInfo, BackupError


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def backup_manager(temp_dir):
    """创建备份管理器实例"""
    return BackupManager(temp_dir)


@pytest.fixture
def sample_project_dir(temp_dir):
    """创建示例项目目录"""
    project_dir = Path(temp_dir) / "test_project"
    project_dir.mkdir(parents=True)
    
    # 创建一些示例文件
    (project_dir / "project.json").write_text('{"name": "测试项目"}')
    (project_dir / "characters").mkdir()
    (project_dir / "characters" / "char1.json").write_text('{"name": "角色1"}')
    (project_dir / "scenes").mkdir()
    (project_dir / "scenes" / "scene1.json").write_text('{"name": "场景1"}')
    
    return str(project_dir)


class TestBackupInfo:
    """备份信息测试类"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        backup_info = BackupInfo(
            backup_id="test-backup",
            project_id="test-project",
            backup_path="/path/to/backup.zip"
        )
        
        assert backup_info.backup_id == "test-backup"
        assert backup_info.project_id == "test-project"
        assert backup_info.backup_path == "/path/to/backup.zip"
        assert backup_info.created_at is not None
        assert backup_info.description == ""
        assert backup_info.file_count == 0
        assert backup_info.compressed_size == 0
        assert backup_info.original_size == 0
        
    def test_init_full(self):
        """测试完整初始化"""
        created_at = datetime.now()
        backup_info = BackupInfo(
            backup_id="test-backup",
            project_id="test-project",
            backup_path="/path/to/backup.zip",
            created_at=created_at,
            description="测试备份",
            file_count=10,
            compressed_size=1024,
            original_size=2048,
            checksum="abc123"
        )
        
        assert backup_info.created_at == created_at
        assert backup_info.description == "测试备份"
        assert backup_info.file_count == 10
        assert backup_info.compressed_size == 1024
        assert backup_info.original_size == 2048
        assert backup_info.checksum == "abc123"
        
    def test_to_dict(self):
        """测试转换为字典"""
        backup_info = BackupInfo(
            backup_id="test-backup",
            project_id="test-project",
            backup_path="/path/to/backup.zip"
        )
        
        data = backup_info.to_dict()
        
        assert data["backup_id"] == "test-backup"
        assert data["project_id"] == "test-project"
        assert data["backup_path"] == "/path/to/backup.zip"
        assert "created_at" in data
        
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "backup_id": "test-backup",
            "project_id": "test-project",
            "backup_path": "/path/to/backup.zip",
            "created_at": "2024-01-01T12:00:00",
            "description": "测试备份"
        }
        
        backup_info = BackupInfo.from_dict(data)
        
        assert backup_info.backup_id == "test-backup"
        assert backup_info.project_id == "test-project"
        assert backup_info.description == "测试备份"


class TestBackupManager:
    """备份管理器测试类"""
    
    def test_init(self, temp_dir):
        """测试初始化"""
        manager = BackupManager(temp_dir)
        
        assert manager.backup_dir == Path(temp_dir)
        assert manager.backup_dir.exists()
        
    def test_create_backup(self, backup_manager, sample_project_dir):
        """测试创建备份"""
        backup_info = backup_manager.create_backup(
            project_id="test-project",
            source_path=sample_project_dir,
            description="测试备份"
        )
        
        assert backup_info is not None
        assert backup_info.project_id == "test-project"
        assert backup_info.description == "测试备份"
        assert Path(backup_info.backup_path).exists()
        assert backup_info.file_count > 0
        
    def test_create_backup_with_compression(self, backup_manager, sample_project_dir):
        """测试创建压缩备份"""
        backup_info = backup_manager.create_backup(
            project_id="test-project",
            source_path=sample_project_dir
        )
        
        # 验证备份文件是有效的ZIP文件
        assert zipfile.is_zipfile(backup_info.backup_path)
        
        # 验证压缩率
        assert backup_info.compressed_size < backup_info.original_size
        
    def test_list_backups(self, backup_manager, sample_project_dir):
        """测试列出备份"""
        # 创建几个备份
        backup1 = backup_manager.create_backup("project1", sample_project_dir, "备份1")
        backup2 = backup_manager.create_backup("project2", sample_project_dir, "备份2")
        
        # 列出所有备份
        all_backups = backup_manager.list_backups()
        assert len(all_backups) >= 2
        
        # 列出特定项目的备份
        project1_backups = backup_manager.list_backups("project1")
        assert len(project1_backups) >= 1
        assert all(b.project_id == "project1" for b in project1_backups)
        
    def test_get_backup_info(self, backup_manager, sample_project_dir):
        """测试获取备份信息"""
        backup_info = backup_manager.create_backup("test-project", sample_project_dir)
        
        retrieved_info = backup_manager.get_backup_info(backup_info.backup_id)
        
        assert retrieved_info is not None
        assert retrieved_info.backup_id == backup_info.backup_id
        assert retrieved_info.project_id == backup_info.project_id
        
    def test_restore_backup(self, backup_manager, sample_project_dir, temp_dir):
        """测试恢复备份"""
        # 创建备份
        backup_info = backup_manager.create_backup("test-project", sample_project_dir)
        
        # 恢复到新位置
        restore_path = Path(temp_dir) / "restored_project"
        restored_path = backup_manager.restore_backup(backup_info.backup_id, str(restore_path))
        
        assert Path(restored_path).exists()
        assert (Path(restored_path) / "project.json").exists()
        assert (Path(restored_path) / "characters" / "char1.json").exists()
        
    def test_delete_backup(self, backup_manager, sample_project_dir):
        """测试删除备份"""
        backup_info = backup_manager.create_backup("test-project", sample_project_dir)
        backup_path = backup_info.backup_path
        
        # 验证备份文件存在
        assert Path(backup_path).exists()
        
        # 删除备份
        backup_manager.delete_backup(backup_info.backup_id)
        
        # 验证备份文件已删除
        assert not Path(backup_path).exists()
        
        # 验证备份信息已删除
        assert backup_manager.get_backup_info(backup_info.backup_id) is None
        
    def test_cleanup_old_backups(self, backup_manager, sample_project_dir):
        """测试清理旧备份"""
        # 创建一些备份
        backup1 = backup_manager.create_backup("test-project", sample_project_dir)
        backup2 = backup_manager.create_backup("test-project", sample_project_dir)
        
        # 模拟旧备份（修改创建时间）
        old_time = datetime.now() - timedelta(days=31)
        backup1.created_at = old_time
        backup_manager._save_backup_info(backup1)
        
        # 清理30天前的备份
        deleted_count = backup_manager.cleanup_old_backups(days=30)
        
        assert deleted_count >= 1
        assert backup_manager.get_backup_info(backup1.backup_id) is None
        assert backup_manager.get_backup_info(backup2.backup_id) is not None
        
    def test_verify_backup_integrity(self, backup_manager, sample_project_dir):
        """测试验证备份完整性"""
        backup_info = backup_manager.create_backup("test-project", sample_project_dir)
        
        # 验证完整性
        is_valid = backup_manager.verify_backup_integrity(backup_info.backup_id)
        assert is_valid is True
        
    def test_verify_corrupted_backup(self, backup_manager, sample_project_dir):
        """测试验证损坏的备份"""
        backup_info = backup_manager.create_backup("test-project", sample_project_dir)
        
        # 损坏备份文件
        with open(backup_info.backup_path, 'w') as f:
            f.write("corrupted data")
            
        # 验证完整性应该失败
        is_valid = backup_manager.verify_backup_integrity(backup_info.backup_id)
        assert is_valid is False
        
    def test_get_backup_statistics(self, backup_manager, sample_project_dir):
        """测试获取备份统计"""
        # 创建一些备份
        backup_manager.create_backup("project1", sample_project_dir)
        backup_manager.create_backup("project2", sample_project_dir)
        
        stats = backup_manager.get_backup_statistics()
        
        assert stats["total_backups"] >= 2
        assert stats["total_size"] > 0
        assert "projects" in stats
        
    def test_error_handling_invalid_source(self, backup_manager):
        """测试无效源路径错误处理"""
        with pytest.raises(BackupError):
            backup_manager.create_backup("test-project", "/nonexistent/path")
            
    def test_error_handling_invalid_backup_id(self, backup_manager):
        """测试无效备份ID错误处理"""
        with pytest.raises(BackupError):
            backup_manager.restore_backup("nonexistent-backup", "/tmp/restore")
            
    def test_error_handling_permission_denied(self, backup_manager, sample_project_dir):
        """测试权限拒绝错误处理"""
        # 这个测试在Windows上可能不适用
        if Path("/").exists():  # Unix-like系统
            with pytest.raises(BackupError):
                backup_manager.restore_backup("test-backup", "/root/no_permission")
                
    @patch('zipfile.ZipFile')
    def test_compression_error_handling(self, mock_zipfile, backup_manager, sample_project_dir):
        """测试压缩错误处理"""
        mock_zipfile.side_effect = Exception("压缩失败")
        
        with pytest.raises(BackupError):
            backup_manager.create_backup("test-project", sample_project_dir)
            
    def test_concurrent_backup_creation(self, backup_manager, sample_project_dir):
        """测试并发备份创建"""
        import threading
        
        results = []
        errors = []
        
        def create_backup(project_id):
            try:
                backup_info = backup_manager.create_backup(project_id, sample_project_dir)
                results.append(backup_info)
            except Exception as e:
                errors.append(e)
                
        # 创建多个线程同时创建备份
        threads = []
        for i in range(3):
            thread = threading.Thread(target=create_backup, args=(f"project-{i}",))
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证结果
        assert len(errors) == 0, f"并发备份创建出现错误: {errors}"
        assert len(results) == 3, "应该创建3个备份"
        
        # 验证每个备份都是唯一的
        backup_ids = [r.backup_id for r in results]
        assert len(set(backup_ids)) == 3, "备份ID应该是唯一的"
