"""事件模型测试

测试事件相关的数据模型，包括：
- EventParticipation
- EventImpactData
- Event
- EventValidator
"""

import pytest
from datetime import timedelta
from uuid import uuid4

from src.core.models.event import (
    EventParticipation, EventImpactData, Event, EventValidator
)
from src.core.models.enums import (
    ElementType, EventType, EventCategory, ParticipationRole,
    PlotFunction, ImportanceLevel
)


class TestEventParticipation:
    """测试事件参与"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        character_id = uuid4()
        participation = EventParticipation(character_id=character_id)
        
        assert participation.character_id == character_id
        assert participation.role.value == ParticipationRole.OBSERVER.value
        assert participation.involvement_level == 1.0
        assert participation.character_state_before == {}
        assert participation.character_state_after == {}
        assert participation.participation_description == ""
        assert participation.character_motivation == ""
        assert participation.character_goal == ""
    
    def test_init_full(self):
        """测试完整初始化"""
        character_id = uuid4()
        participation = EventParticipation(
            character_id=character_id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8,
            character_state_before={"mood": "calm"},
            character_state_after={"mood": "excited"},
            participation_description="主动参与",
            character_motivation="寻找真相",
            character_goal="解决谜题"
        )
        
        assert participation.character_id == character_id
        assert participation.role == ParticipationRole.PROTAGONIST.value
        assert participation.involvement_level == 0.8
        assert participation.character_state_before["mood"] == "calm"
        assert participation.character_state_after["mood"] == "excited"
        assert participation.participation_description == "主动参与"
        assert participation.character_motivation == "寻找真相"
        assert participation.character_goal == "解决谜题"
    
    def test_validate_involvement_level(self):
        """测试参与度验证"""
        character_id = uuid4()
        
        with pytest.raises(ValueError, match="参与度必须在0.0-1.0之间"):
            EventParticipation(character_id=character_id, involvement_level=1.5)
        
        with pytest.raises(ValueError, match="参与度必须在0.0-1.0之间"):
            EventParticipation(character_id=character_id, involvement_level=-0.1)


class TestEventImpactData:
    """测试事件影响数据"""
    
    def test_init_default(self):
        """测试默认初始化"""
        impact = EventImpactData()
        
        assert impact.plot_impact == 0.0
        assert impact.plot_advancement == ""
        assert impact.character_impact == {}
        assert impact.character_development == {}
        assert impact.world_impact == 0.0
        assert impact.world_changes == []
        assert impact.theme_impact == 0.0
        assert impact.theme_exploration == ""
        assert impact.emotional_impact == 0.5
        assert impact.suspense_level == 0.5
    
    def test_init_with_data(self):
        """测试带数据初始化"""
        character_id = uuid4()
        impact = EventImpactData(
            plot_impact=0.8,
            plot_advancement="情节重大推进",
            character_impact={character_id: 0.7},
            character_development={character_id: "性格成长"},
            world_impact=0.3,
            world_changes=["新规则建立"],
            theme_impact=0.5,
            theme_exploration="探索友谊主题",
            emotional_impact=0.9,
            suspense_level=0.8
        )
        
        assert impact.plot_impact == 0.8
        assert impact.plot_advancement == "情节重大推进"
        assert impact.character_impact[character_id] == 0.7
        assert impact.character_development[character_id] == "性格成长"
        assert impact.world_impact == 0.3
        assert "新规则建立" in impact.world_changes
        assert impact.theme_impact == 0.5
        assert impact.theme_exploration == "探索友谊主题"
        assert impact.emotional_impact == 0.9
        assert impact.suspense_level == 0.8
    
    def test_validate_impact_range(self):
        """测试影响值范围验证"""
        with pytest.raises(ValueError, match="影响值必须在-1.0到1.0之间"):
            EventImpactData(plot_impact=1.5)
        
        with pytest.raises(ValueError, match="影响值必须在-1.0到1.0之间"):
            EventImpactData(world_impact=-1.5)
    
    def test_validate_level_range(self):
        """测试级别值范围验证"""
        with pytest.raises(ValueError, match="级别值必须在0.0到1.0之间"):
            EventImpactData(emotional_impact=1.5)
        
        with pytest.raises(ValueError, match="级别值必须在0.0到1.0之间"):
            EventImpactData(suspense_level=-0.1)


class TestEvent:
    """测试事件模型"""
    
    def test_init_minimal(self):
        """测试最小初始化"""
        event = Event()
        
        assert event.element_type.value == ElementType.EVENT.value
        assert event.name == "未命名事件"
        assert event.event_type.value == EventType.SCENE.value
        assert event.event_category.value == EventCategory.NORMAL.value
        assert event.timeline_position == 0.0
        assert event.duration is None
        assert event.sequence_order == 0
        assert event.summary == ""
        assert event.detailed_description == ""
        assert event.outcome == ""
        assert event.participants == []
        assert event.triggers == []
        assert event.consequences == []
        assert isinstance(event.impact, EventImpactData)
        assert event.location is None
        assert event.plot_function.value == PlotFunction.DEVELOPMENT.value
        assert event.tension_level == 0.5
        assert event.is_turning_point is False
        assert event.is_climax is False
        assert event.is_resolution is False
    
    def test_init_with_summary(self):
        """测试带摘要初始化"""
        event = Event(summary="主角发现重要线索")
        
        assert event.name == "主角发现重要线索"
        assert event.summary == "主角发现重要线索"
    
    def test_init_full(self):
        """测试完整初始化"""
        event = Event(
            name="关键对话",
            summary="主角与反派的关键对话",
            detailed_description="这是一个详细的事件描述",
            event_type=EventType.DIALOGUE,
            event_category=EventCategory.TURNING_POINT,
            timeline_position=0.6,
            duration=timedelta(minutes=30),
            sequence_order=10,
            outcome="真相大白",
            plot_function=PlotFunction.CLIMAX,
            tension_level=0.9,
            is_turning_point=True
        )
        
        assert event.name == "关键对话"
        assert event.summary == "主角与反派的关键对话"
        assert event.detailed_description == "这是一个详细的事件描述"
        assert event.event_type == EventType.DIALOGUE.value
        assert event.event_category == EventCategory.TURNING_POINT.value
        assert event.timeline_position == 0.6
        assert event.duration == timedelta(minutes=30)
        assert event.sequence_order == 10
        assert event.outcome == "真相大白"
        assert event.plot_function == PlotFunction.CLIMAX.value
        assert event.tension_level == 0.9
        assert event.is_turning_point is True
    
    def test_validate_level_fields(self):
        """测试级别字段验证"""
        with pytest.raises(ValueError, match="级别字段必须在0.0-1.0之间"):
            Event(timeline_position=1.5)
        
        with pytest.raises(ValueError, match="级别字段必须在0.0-1.0之间"):
            Event(tension_level=-0.1)
    
    def test_add_participant(self):
        """测试添加参与者"""
        event = Event(name="测试事件")
        character_id = uuid4()
        
        event.add_participant(
            character_id=character_id,
            role=ParticipationRole.PROTAGONIST,
            involvement_level=0.8,
            description="主要参与者"
        )
        
        assert len(event.participants) == 1
        participant = event.participants[0]
        assert participant.character_id == character_id
        assert participant.role == ParticipationRole.PROTAGONIST.value
        assert participant.involvement_level == 0.8
        assert participant.participation_description == "主要参与者"
    
    def test_update_participant(self):
        """测试更新参与者"""
        event = Event(name="测试事件")
        character_id = uuid4()
        
        # 先添加参与者
        event.add_participant(character_id, ParticipationRole.OBSERVER)
        assert len(event.participants) == 1
        
        # 更新参与者
        event.add_participant(
            character_id, 
            ParticipationRole.PROTAGONIST, 
            involvement_level=0.9
        )
        
        # 应该还是只有一个参与者，但信息已更新
        assert len(event.participants) == 1
        participant = event.participants[0]
        assert participant.role == ParticipationRole.PROTAGONIST.value
        assert participant.involvement_level == 0.9
    
    def test_remove_participant(self):
        """测试移除参与者"""
        event = Event(name="测试事件")
        character_id = uuid4()
        
        # 先添加参与者
        event.add_participant(character_id, ParticipationRole.OBSERVER)
        assert len(event.participants) == 1
        
        # 移除参与者
        event.remove_participant(character_id)
        assert len(event.participants) == 0
    
    def test_get_participant(self):
        """测试获取参与者"""
        event = Event(name="测试事件")
        character_id = uuid4()
        
        # 获取不存在的参与者
        participant = event.get_participant(character_id)
        assert participant is None
        
        # 添加参与者后获取
        event.add_participant(character_id, ParticipationRole.PROTAGONIST)
        participant = event.get_participant(character_id)
        assert participant is not None
        assert participant.character_id == character_id
        assert participant.role == ParticipationRole.PROTAGONIST.value
    
    def test_add_trigger_and_consequence(self):
        """测试添加触发和后果"""
        event = Event(name="测试事件")
        trigger_id = uuid4()
        consequence_id = uuid4()
        
        event.add_trigger(trigger_id)
        event.add_consequence(consequence_id)
        
        assert trigger_id in event.triggers
        assert consequence_id in event.consequences
        assert len(event.triggers) == 1
        assert len(event.consequences) == 1
        
        # 重复添加不应该增加数量
        event.add_trigger(trigger_id)
        assert len(event.triggers) == 1
    
    def test_get_main_participants(self):
        """测试获取主要参与者"""
        event = Event(name="测试事件")
        protagonist_id = uuid4()
        antagonist_id = uuid4()
        observer_id = uuid4()
        
        event.add_participant(protagonist_id, ParticipationRole.PROTAGONIST)
        event.add_participant(antagonist_id, ParticipationRole.ANTAGONIST)
        event.add_participant(observer_id, ParticipationRole.OBSERVER)
        
        main_participants = event.get_main_participants()
        assert len(main_participants) == 2
        
        main_character_ids = [p.character_id for p in main_participants]
        assert protagonist_id in main_character_ids
        assert antagonist_id in main_character_ids
        assert observer_id not in main_character_ids
    
    def test_get_impact_score(self):
        """测试获取影响评分"""
        event = Event(name="测试事件")
        
        # 默认影响评分
        score = event.get_impact_score()
        assert score >= 0.0
        
        # 设置高影响
        event.impact.plot_impact = 0.8
        event.impact.emotional_impact = 0.9
        event.tension_level = 0.8
        event.event_category = EventCategory.CLIMAX
        
        score = event.get_impact_score()
        assert score > 0.5
    
    def test_validate_model(self):
        """测试模型验证"""
        event = Event(name="测试事件", summary="测试摘要")
        result = event.validate_model()
        
        assert result.is_valid is True
        assert len(result.errors) == 0


class TestEventValidator:
    """测试事件验证器"""
    
    def test_validate_valid_event(self):
        """测试验证有效事件"""
        event = Event(
            name="完整事件",
            summary="这是一个完整的事件",
            detailed_description="详细的事件描述",
            outcome="明确的结果"
        )
        
        # 添加参与者
        character_id = uuid4()
        event.add_participant(character_id, ParticipationRole.PROTAGONIST)
        
        validator = EventValidator()
        result = validator.validate(event)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_incomplete_event(self):
        """测试验证不完整事件"""
        event = Event(name="不完整事件")
        # 缺少摘要、描述和参与者
        
        validator = EventValidator()
        result = validator.validate(event)
        
        # 应该有错误或警告
        assert len(result.errors) > 0 or len(result.warnings) > 0
