"""事件模型

定义故事事件相关的数据模型，包括：
- EventParticipation: 事件参与信息
- EventImpactData: 事件影响数据
- Event: 主要事件模型
- EventValidator: 事件验证器
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import Field, validator

from .base import BaseModel, ValidationResult, ModelValidator
from .story_element import StoryElement, StoryElementValidator
from .enums import (
    ElementType, EventType, EventCategory, ParticipationRole, 
    PlotFunction, ImportanceLevel
)


class EventParticipation(BaseModel):
    """事件参与信息"""
    
    # 参与者信息
    character_id: UUID = Field(..., description="角色ID")
    role: ParticipationRole = Field(default=ParticipationRole.OBSERVER, description="参与角色")
    involvement_level: float = Field(default=1.0, description="参与度 (0.0-1.0)")
    
    # 状态变化
    character_state_before: Dict[str, Any] = Field(default_factory=dict, description="事件前状态")
    character_state_after: Dict[str, Any] = Field(default_factory=dict, description="事件后状态")
    
    # 参与描述
    participation_description: str = Field(default="", description="参与描述")
    character_motivation: str = Field(default="", description="角色动机")
    character_goal: str = Field(default="", description="角色目标")
    
    @validator('involvement_level')
    def validate_involvement_level(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("参与度必须在0.0-1.0之间")
        return v


class EventImpactData(BaseModel):
    """事件影响数据"""
    
    # 情节影响
    plot_impact: float = Field(default=0.0, description="对情节的影响 (-1.0到1.0)")
    plot_advancement: str = Field(default="", description="情节推进描述")
    
    # 角色影响
    character_impact: Dict[UUID, float] = Field(default_factory=dict, description="对角色的影响")
    character_development: Dict[UUID, str] = Field(default_factory=dict, description="角色发展描述")
    
    # 世界观影响
    world_impact: float = Field(default=0.0, description="对世界观的影响 (-1.0到1.0)")
    world_changes: List[str] = Field(default_factory=list, description="世界变化列表")
    
    # 主题影响
    theme_impact: float = Field(default=0.0, description="对主题的影响 (-1.0到1.0)")
    theme_exploration: str = Field(default="", description="主题探索描述")
    
    # 读者影响
    emotional_impact: float = Field(default=0.5, description="情感影响强度 (0.0-1.0)")
    suspense_level: float = Field(default=0.5, description="悬念程度 (0.0-1.0)")
    
    @validator('plot_impact', 'world_impact', 'theme_impact')
    def validate_impact_range(cls, v):
        if v < -1.0 or v > 1.0:
            raise ValueError("影响值必须在-1.0到1.0之间")
        return v
    
    @validator('emotional_impact', 'suspense_level')
    def validate_level_range(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("级别值必须在0.0到1.0之间")
        return v


class Event(StoryElement):
    """故事事件模型"""
    
    # 元素类型
    element_type: ElementType = Field(default=ElementType.EVENT, description="元素类型")
    
    # 事件分类
    event_type: EventType = Field(default=EventType.SCENE, description="事件类型")
    event_category: EventCategory = Field(default=EventCategory.NORMAL, description="事件类别")
    
    # 时间信息
    timeline_position: float = Field(default=0.0, description="时间线位置 (0.0-1.0)")
    duration: Optional[timedelta] = Field(default=None, description="持续时间")
    sequence_order: int = Field(default=0, description="序列顺序")
    
    # 事件内容
    summary: str = Field(default="", description="事件摘要")
    detailed_description: str = Field(default="", description="详细描述")
    outcome: str = Field(default="", description="事件结果")
    
    # 参与者
    participants: List[EventParticipation] = Field(default_factory=list, description="参与者列表")
    
    # 因果关系
    triggers: List[UUID] = Field(default_factory=list, description="触发此事件的事件ID")
    consequences: List[UUID] = Field(default_factory=list, description="此事件导致的事件ID")
    
    # 影响分析
    impact: EventImpactData = Field(default_factory=EventImpactData, description="事件影响")
    
    # 场景关联
    location: Optional[UUID] = Field(default=None, description="发生场景ID")
    
    # 情节功能
    plot_function: PlotFunction = Field(default=PlotFunction.DEVELOPMENT, description="情节功能")
    tension_level: float = Field(default=0.5, description="紧张度 (0.0-1.0)")
    
    # 事件标记
    is_turning_point: bool = Field(default=False, description="是否为转折点")
    is_climax: bool = Field(default=False, description="是否为高潮")
    is_resolution: bool = Field(default=False, description="是否为解决点")
    
    # 创作信息
    inspiration_source: str = Field(default="", description="灵感来源")
    writing_notes: str = Field(default="", description="创作笔记")
    
    def __init__(self, **data):
        """初始化事件"""
        # 确保name字段有值
        if 'name' not in data:
            if 'summary' in data and data['summary']:
                data['name'] = data['summary'][:50] + ("..." if len(data['summary']) > 50 else "")
            else:
                data['name'] = "未命名事件"
        
        super().__init__(**data)
    
    @validator('timeline_position', 'tension_level')
    def validate_level_fields(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("级别字段必须在0.0-1.0之间")
        return v
    
    def add_participant(self, character_id: UUID, role: ParticipationRole = ParticipationRole.OBSERVER, 
                       involvement_level: float = 1.0, description: str = ""):
        """添加参与者"""
        # 检查是否已存在
        for participant in self.participants:
            if participant.character_id == character_id:
                # 更新现有参与者
                participant.role = role
                participant.involvement_level = involvement_level
                if description:
                    participant.participation_description = description
                self.update_timestamp()
                return
        
        # 添加新参与者
        participation = EventParticipation(
            character_id=character_id,
            role=role,
            involvement_level=involvement_level,
            participation_description=description
        )
        self.participants.append(participation)
        self.update_timestamp()
    
    def remove_participant(self, character_id: UUID):
        """移除参与者"""
        self.participants = [p for p in self.participants if p.character_id != character_id]
        self.update_timestamp()
    
    def get_participant(self, character_id: UUID) -> Optional[EventParticipation]:
        """获取参与者信息"""
        for participant in self.participants:
            if participant.character_id == character_id:
                return participant
        return None
    
    def add_trigger(self, event_id: UUID):
        """添加触发事件"""
        if event_id not in self.triggers:
            self.triggers.append(event_id)
            self.update_timestamp()
    
    def add_consequence(self, event_id: UUID):
        """添加后果事件"""
        if event_id not in self.consequences:
            self.consequences.append(event_id)
            self.update_timestamp()
    
    def get_main_participants(self) -> List[EventParticipation]:
        """获取主要参与者"""
        return [p for p in self.participants
                if p.role in [ParticipationRole.PROTAGONIST.value, ParticipationRole.ANTAGONIST.value]]
    
    def get_impact_score(self) -> float:
        """获取事件影响评分"""
        score = 0.0
        
        # 基于影响数据的评分
        score += abs(self.impact.plot_impact) * 0.3
        score += abs(self.impact.world_impact) * 0.2
        score += abs(self.impact.theme_impact) * 0.2
        score += self.impact.emotional_impact * 0.2
        score += self.tension_level * 0.1
        
        # 基于事件类别的调整
        if self.event_category == EventCategory.CLIMAX.value:
            score += 0.2
        elif self.event_category == EventCategory.TURNING_POINT.value:
            score += 0.15
        elif self.event_category == EventCategory.RESOLUTION.value:
            score += 0.1
        
        return min(1.0, score)
    
    def validate_model(self) -> ValidationResult:
        """验证事件模型"""
        result = ValidationResult()
        
        # 验证基础字段
        if not self.name or not self.name.strip():
            result.add_error("事件名称不能为空")
        
        if len(self.name) > 100:
            result.add_error("事件名称不能超过100个字符")
        
        if len(self.summary) > 500:
            result.add_error("事件摘要不能超过500个字符")
        
        if len(self.detailed_description) > 5000:
            result.add_error("详细描述不能超过5000个字符")
        
        # 验证时间线位置
        if self.timeline_position < 0.0 or self.timeline_position > 1.0:
            result.add_error("时间线位置必须在0.0-1.0之间")
        
        # 验证紧张度
        if self.tension_level < 0.0 or self.tension_level > 1.0:
            result.add_error("紧张度必须在0.0-1.0之间")
        
        # 验证序列顺序
        if self.sequence_order < 0:
            result.add_error("序列顺序不能为负数")
        
        # 验证参与者
        for i, participant in enumerate(self.participants):
            if participant.involvement_level < 0.0 or participant.involvement_level > 1.0:
                result.add_error(f"参与者{i+1}的参与度必须在0.0-1.0之间")
        
        # 验证影响数据
        impact_result = self._validate_impact_data()
        result.merge(impact_result)
        
        return result
    
    def _validate_impact_data(self) -> ValidationResult:
        """验证影响数据"""
        result = ValidationResult()
        
        # 验证影响值范围
        if (self.impact.plot_impact < -1.0 or self.impact.plot_impact > 1.0):
            result.add_error("情节影响值必须在-1.0到1.0之间")
        
        if (self.impact.world_impact < -1.0 or self.impact.world_impact > 1.0):
            result.add_error("世界观影响值必须在-1.0到1.0之间")
        
        if (self.impact.theme_impact < -1.0 or self.impact.theme_impact > 1.0):
            result.add_error("主题影响值必须在-1.0到1.0之间")
        
        if (self.impact.emotional_impact < 0.0 or self.impact.emotional_impact > 1.0):
            result.add_error("情感影响值必须在0.0到1.0之间")
        
        if (self.impact.suspense_level < 0.0 or self.impact.suspense_level > 1.0):
            result.add_error("悬念程度必须在0.0到1.0之间")
        
        return result


class EventValidator(ModelValidator):
    """事件验证器"""
    
    def validate(self, model: Event) -> ValidationResult:
        """验证事件"""
        result = ValidationResult()
        
        # 基础验证
        base_result = model.validate_model()
        result.merge(base_result)
        
        # 业务规则验证
        self._validate_event_completeness(model, result)
        self._validate_event_consistency(model, result)
        self._validate_event_relationships(model, result)
        
        return result
    
    def _validate_event_completeness(self, model: Event, result: ValidationResult):
        """验证事件完整性"""
        # 检查关键信息是否完整
        if not model.summary and not model.detailed_description:
            result.add_error("事件必须有摘要或详细描述")
        
        if not model.participants:
            result.add_warning("事件没有参与者")
        
        # 检查重要事件的完整性
        if model.importance == ImportanceLevel.CRITICAL:
            if not model.outcome:
                result.add_warning("关键事件应该有明确的结果")
            
            if not model.impact.plot_advancement:
                result.add_warning("关键事件应该描述情节推进")
    
    def _validate_event_consistency(self, model: Event, result: ValidationResult):
        """验证事件一致性"""
        # 检查事件类别和标记的一致性
        if model.is_climax and model.event_category != EventCategory.CLIMAX:
            result.add_warning("高潮事件的类别应该设置为CLIMAX")
        
        if model.is_turning_point and model.event_category != EventCategory.TURNING_POINT:
            result.add_warning("转折点事件的类别应该设置为TURNING_POINT")
        
        if model.is_resolution and model.event_category != EventCategory.RESOLUTION:
            result.add_warning("解决事件的类别应该设置为RESOLUTION")
        
        # 检查紧张度和事件类别的一致性
        if model.event_category == EventCategory.CLIMAX and model.tension_level < 0.7:
            result.add_warning("高潮事件的紧张度通常应该较高")
    
    def _validate_event_relationships(self, model: Event, result: ValidationResult):
        """验证事件关系"""
        # 检查因果关系
        if model.triggers and not model.consequences:
            result.add_info("事件有触发条件但没有后果")
        
        # 检查循环引用
        if model.id in model.triggers:
            result.add_error("事件不能触发自己")
        
        if model.id in model.consequences:
            result.add_error("事件不能是自己的后果")
        
        # 检查参与者角色分配
        protagonist_count = sum(1 for p in model.participants 
                              if p.role == ParticipationRole.PROTAGONIST)
        if protagonist_count > 1:
            result.add_warning("事件有多个主角参与者")
        
        if protagonist_count == 0 and model.importance == ImportanceLevel.CRITICAL:
            result.add_warning("关键事件通常应该有主角参与")
