# 笔落 - 开发者指南

## 📋 目录

1. [项目概述](#项目概述)
2. [开发环境搭建](#开发环境搭建)
3. [项目架构](#项目架构)
4. [核心模块](#核心模块)
5. [开发规范](#开发规范)
6. [测试指南](#测试指南)
7. [部署发布](#部署发布)
8. [贡献指南](#贡献指南)

## 🎯 项目概述

### 技术栈

- **编程语言**: Python 3.11+
- **GUI框架**: PyQt6
- **数据验证**: Pydantic v2
- **AI集成**: OpenAI, Anthropic, DeepSeek, 智谱AI
- **测试框架**: pytest, pytest-qt
- **代码质量**: flake8, black, mypy
- **打包工具**: PyInstaller
- **版本控制**: Git

### 项目特点

- **模块化架构**: 清晰的模块分离和依赖管理
- **类型安全**: 全面的类型注解和静态检查
- **测试驱动**: 90%+ 测试覆盖率
- **异步支持**: 多线程AI调用和文件操作
- **跨平台**: 支持Windows、macOS、Linux

## 🛠️ 开发环境搭建

### 环境要求

```bash
# Python版本
Python 3.11 或更高版本

# 系统要求
Windows 10/11, macOS 10.15+, Ubuntu 20.04+
内存: 8GB+ 推荐
存储: 2GB+ 可用空间
```

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-org/bamboofall.git
cd bamboofall
```

2. **创建虚拟环境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

4. **配置开发工具**
```bash
# 安装pre-commit钩子
pre-commit install

# 配置IDE（推荐VSCode）
code .
```

### 开发工具配置

**VSCode设置** (`.vscode/settings.json`):
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"]
}
```

## 🏗️ 项目架构

### 目录结构

```
bamboofall/
├── src/                    # 源代码
│   ├── core/              # 核心业务逻辑
│   │   ├── models/        # 数据模型
│   │   ├── storage/       # 存储系统
│   │   ├── ai/           # AI服务集成
│   │   ├── validation/    # 数据验证
│   │   └── events/       # 事件系统
│   ├── ui/               # 用户界面
│   │   ├── main_window.py
│   │   ├── writing_interface.py
│   │   └── ...
│   ├── utils/            # 工具函数
│   ├── config/           # 配置管理
│   └── main.py          # 应用入口
├── tests/                # 测试代码
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   └── performance/     # 性能测试
├── scripts/             # 构建脚本
├── Docs/               # 文档
├── resources/          # 资源文件
└── requirements.txt    # 依赖列表
```

### 架构模式

```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│  ┌─────────────┬─────────────────────┐  │
│  │ Main Window │   Writing Interface │  │
│  │             │                     │  │
│  └─────────────┴─────────────────────┘  │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│              Service Layer              │
│  ┌─────────────┬─────────────────────┐  │
│  │ AI Services │  Storage Manager    │  │
│  │             │                     │  │
│  └─────────────┴─────────────────────┘  │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────┴───────────────────────┐
│               Core Layer                │
│  ┌─────────────┬─────────────────────┐  │
│  │ Data Models │    Validation       │  │
│  │             │                     │  │
│  └─────────────┴─────────────────────┘  │
└─────────────────────────────────────────┘
```

## 🔧 核心模块

### 数据模型 (src/core/models/)

**基础模型继承关系**:
```python
BaseModel (Pydantic)
└── VersionedModel
    └── StoryElement
        ├── Character
        ├── Scene
        └── Event
```

**关键特性**:
- UUID主键
- 时间戳管理
- 版本控制
- 数据验证
- JSON序列化

### 存储系统 (src/core/storage/)

**组件架构**:
```python
StorageManager
├── FileStorage      # 文件存储
├── CacheManager     # 缓存管理
├── BackupManager    # 备份管理
└── VersionControl   # 版本控制
```

**存储格式**:
```
project_dir/
├── project.json     # 项目元数据
├── characters/      # 角色数据
├── scenes/         # 场景数据
├── events/         # 事件数据
├── chapters/       # 章节内容
└── .metadata/      # 元数据和索引
```

### AI服务 (src/core/ai/)

**服务抽象**:
```python
AIService (抽象基类)
├── OpenAIAdapter
├── AnthropicAdapter
├── DeepSeekAdapter
└── ZhipuAdapter
```

**功能模块**:
- 内容生成
- 文本优化
- 对话生成
- 创意辅助

### 用户界面 (src/ui/)

**主要组件**:
```python
MainWindow
├── WritingInterface    # 写作界面
├── AIAssistant        # AI助手
├── CharacterManager   # 角色管理
├── SceneManager       # 场景管理
├── EventManager       # 事件管理
└── SettingsDialog     # 设置对话框
```

## 📝 开发规范

### 代码风格

**Python代码规范**:
```python
# 使用类型注解
def save_project(self, project: WritingProject) -> Path:
    """保存项目到文件系统"""
    pass

# 使用docstring
class Character(StoryElement):
    """角色数据模型
    
    Attributes:
        full_name: 角色全名
        age: 年龄
        description: 角色描述
    """
    pass

# 错误处理
try:
    result = risky_operation()
except SpecificError as e:
    logger.error(f"操作失败: {e}")
    raise ProcessingError(f"处理失败: {e}") from e
```

**命名规范**:
- 类名: PascalCase (`WritingProject`)
- 函数名: snake_case (`save_project`)
- 常量: UPPER_CASE (`MAX_RETRIES`)
- 私有成员: 前缀下划线 (`_internal_method`)

### Git工作流

**分支策略**:
```
main                 # 主分支，稳定版本
├── develop         # 开发分支
├── feature/xxx     # 功能分支
├── bugfix/xxx      # 修复分支
└── release/vx.x.x  # 发布分支
```

**提交规范**:
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 重构代码
test: 添加测试
chore: 构建工具或辅助工具的变动
```

### 错误处理

**异常层次**:
```python
BambooFallError (基础异常)
├── ValidationError      # 数据验证错误
├── StorageError        # 存储操作错误
├── AIServiceError      # AI服务错误
└── UIError            # 界面操作错误
```

**错误处理模式**:
```python
# 1. 具体异常处理
try:
    data = load_data()
except FileNotFoundError:
    logger.warning("文件不存在，使用默认配置")
    data = get_default_config()

# 2. 异常转换
try:
    result = external_api_call()
except ExternalAPIError as e:
    raise AIServiceError(f"AI服务调用失败: {e}") from e

# 3. 资源清理
try:
    with open(file_path) as f:
        process_file(f)
finally:
    cleanup_resources()
```

## 🧪 测试指南

### 测试结构

```
tests/
├── unit/              # 单元测试
│   ├── test_models/   # 模型测试
│   ├── test_storage/  # 存储测试
│   ├── test_ai/      # AI服务测试
│   └── test_ui/      # UI测试
├── integration/       # 集成测试
├── performance/       # 性能测试
└── fixtures/         # 测试数据
```

### 测试编写

**单元测试示例**:
```python
import pytest
from src.core.models.character import Character

class TestCharacter:
    def test_character_creation(self):
        """测试角色创建"""
        character = Character(
            name="测试角色",
            full_name="测试角色全名",
            age=25
        )
        assert character.name == "测试角色"
        assert character.age == 25

    def test_character_validation(self):
        """测试角色数据验证"""
        with pytest.raises(ValidationError):
            Character(name="", age=-1)

    @pytest.fixture
    def sample_character(self):
        """测试用角色数据"""
        return Character(
            name="示例角色",
            full_name="示例角色全名",
            age=30
        )
```

**GUI测试示例**:
```python
import pytest
from PyQt6.QtCore import Qt
from src.ui.main_window import MainWindow

class TestMainWindow:
    def test_window_creation(self, qtbot):
        """测试主窗口创建"""
        window = MainWindow()
        qtbot.addWidget(window)
        assert window.isVisible()

    def test_menu_actions(self, qtbot):
        """测试菜单操作"""
        window = MainWindow()
        qtbot.addWidget(window)
        
        # 模拟菜单点击
        qtbot.mouseClick(window.new_project_action, Qt.LeftButton)
        # 验证对话框打开
        assert window.project_dialog.isVisible()
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_models/

# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行性能测试
pytest tests/performance/ -v

# 并行测试
pytest -n auto
```

## 🚀 部署发布

### 构建流程

1. **代码质量检查**
```bash
# 代码格式化
black src/ tests/

# 类型检查
mypy src/

# 代码检查
flake8 src/

# 运行测试
pytest --cov=src --cov-report=term-missing
```

2. **构建应用程序**
```bash
# 运行构建脚本
python scripts/build_app.py

# 手动构建
pyinstaller --clean --noconfirm bamboofall.spec
```

3. **创建安装包**
```bash
# 创建ZIP包
python scripts/create_installer.py

# 创建MSI安装包（Windows）
python scripts/create_msi.py
```

### 版本管理

**版本号规范**: `MAJOR.MINOR.PATCH`
- MAJOR: 不兼容的API修改
- MINOR: 向后兼容的功能性新增
- PATCH: 向后兼容的问题修正

**发布流程**:
1. 更新版本号
2. 更新CHANGELOG
3. 创建发布标签
4. 构建发布包
5. 发布到分发平台

### CI/CD配置

**GitHub Actions** (`.github/workflows/ci.yml`):
```yaml
name: CI/CD Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    - name: Run tests
      run: pytest --cov=src --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: windows-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    - name: Build application
      run: python scripts/build_app.py
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: bamboofall-windows
        path: dist/
```

## 🤝 贡献指南

### 贡献流程

1. **Fork项目**
2. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

3. **开发和测试**
```bash
# 编写代码
# 添加测试
# 运行测试
pytest
```

4. **提交代码**
```bash
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
```

5. **创建Pull Request**

### 代码审查

**审查要点**:
- 代码质量和风格
- 测试覆盖率
- 文档完整性
- 性能影响
- 安全考虑

### 问题报告

**Bug报告模板**:
```markdown
## Bug描述
简要描述遇到的问题

## 复现步骤
1. 打开应用程序
2. 点击...
3. 输入...
4. 观察到错误

## 期望行为
描述期望的正确行为

## 环境信息
- 操作系统: Windows 11
- Python版本: 3.11.0
- 应用版本: 1.0.0

## 附加信息
错误日志、截图等
```

### 开发资源

- **API文档**: 自动生成的API文档
- **架构图**: 系统架构和模块关系图
- **开发工具**: 推荐的开发工具和插件
- **学习资源**: 相关技术的学习资料

---

## 📞 开发支持

如果您在开发过程中遇到问题，可以通过以下方式获取帮助：

- **技术文档**: 查阅详细的API文档
- **开发者社区**: 加入开发者交流群
- **Issue跟踪**: 在GitHub上提交问题
- **邮件联系**: <EMAIL>

感谢您对笔落项目的贡献！ 🚀
