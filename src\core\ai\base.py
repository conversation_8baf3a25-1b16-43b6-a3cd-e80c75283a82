"""
AI服务抽象基类和相关数据结构

本模块定义了AI服务的抽象接口和通用数据结构，为不同AI服务商提供统一的接口。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from uuid import UUID, uuid4
from datetime import datetime

# 移除不存在的导入


class AIProvider(Enum):
    """AI服务提供商"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    ZHIPU = "zhipu"
    ANTHROPIC = "anthropic"


class GenerationType(Enum):
    """生成内容类型"""
    CHARACTER_DESCRIPTION = "character_description"
    SCENE_DESCRIPTION = "scene_description"
    PLOT_DEVELOPMENT = "plot_development"
    DIALOGUE = "dialogue"
    CONTENT_OPTIMIZATION = "content_optimization"
    STORY_OUTLINE = "story_outline"
    CHAPTER_SUMMARY = "chapter_summary"


@dataclass
class GenerationContext:
    """生成上下文"""
    content_type: GenerationType
    project_id: Optional[UUID] = None
    character_ids: Optional[List[UUID]] = None
    scene_ids: Optional[List[UUID]] = None
    event_ids: Optional[List[UUID]] = None
    existing_content: Optional[str] = None
    style_preferences: Optional[Dict[str, Any]] = None
    constraints: Optional[Dict[str, Any]] = None


@dataclass
class GenerationOptions:
    """生成选项"""
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stream: bool = False
    timeout: int = 30
    max_retries: int = 3


@dataclass
class GenerationResult:
    """生成结果"""
    id: UUID
    content: str
    provider: AIProvider
    model: str
    usage: Dict[str, int]
    finish_reason: str
    created_at: datetime
    generation_time: float
    context: GenerationContext
    options: GenerationOptions
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class StreamChunk:
    """流式响应块"""
    id: UUID
    content: str
    delta: str
    finish_reason: Optional[str] = None
    usage: Optional[Dict[str, int]] = None


class AIServiceError(Exception):
    """AI服务错误基类"""
    def __init__(self, message: str, provider: AIProvider, error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class AuthenticationError(AIServiceError):
    """认证错误"""
    pass


class RateLimitError(AIServiceError):
    """速率限制错误"""
    def __init__(self, message: str, provider: AIProvider, retry_after: Optional[int] = None):
        super().__init__(message, provider)
        self.retry_after = retry_after


class QuotaExceededError(AIServiceError):
    """配额超限错误"""
    pass


class ModelNotFoundError(AIServiceError):
    """模型不存在错误"""
    pass


class ContentFilterError(AIServiceError):
    """内容过滤错误"""
    pass


class AIService(ABC):
    """AI服务抽象基类"""
    
    def __init__(self, provider: AIProvider, api_key: str, model: str, base_url: Optional[str] = None):
        self.provider = provider
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self._client = None
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def generate_content(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> GenerationResult:
        """生成内容"""
        pass
    
    @abstractmethod
    async def generate_content_stream(
        self, 
        prompt: str, 
        context: GenerationContext,
        options: GenerationOptions
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成内容"""
        pass
    
    @abstractmethod
    async def validate_api_key(self) -> bool:
        """验证API密钥"""
        pass
    
    @abstractmethod
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        pass
    
    @abstractmethod
    async def estimate_tokens(self, text: str) -> int:
        """估算文本token数量"""
        pass
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            return await self.validate_api_key()
        except Exception:
            return False


class PromptTemplate:
    """提示词模板"""
    
    def __init__(self, template: str, variables: Optional[List[str]] = None):
        self.template = template
        self.variables = variables or []
    
    def format(self, **kwargs) -> str:
        """格式化模板"""
        return self.template.format(**kwargs)
    
    def validate_variables(self, **kwargs) -> bool:
        """验证变量"""
        provided_vars = set(kwargs.keys())
        required_vars = set(self.variables)
        return required_vars.issubset(provided_vars)


class PromptManager:
    """提示词管理器"""
    
    def __init__(self):
        self._templates: Dict[GenerationType, PromptTemplate] = {}
        self._load_default_templates()
    
    def _load_default_templates(self):
        """加载默认模板"""
        # 角色描述模板
        self._templates[GenerationType.CHARACTER_DESCRIPTION] = PromptTemplate(
            "请为小说创作一个角色描述。角色名称：{name}，性别：{gender}，年龄：{age}。"
            "请包含外貌特征、性格特点、背景故事等内容。风格要求：{style}",
            ["name", "gender", "age", "style"]
        )
        
        # 场景描述模板
        self._templates[GenerationType.SCENE_DESCRIPTION] = PromptTemplate(
            "请为小说创作一个场景描述。场景名称：{name}，地点：{location}，时间：{time}。"
            "请包含环境描述、氛围营造、感官细节等内容。风格要求：{style}",
            ["name", "location", "time", "style"]
        )
        
        # 情节发展模板
        self._templates[GenerationType.PLOT_DEVELOPMENT] = PromptTemplate(
            "请为小说创作情节发展。当前情况：{current_situation}，目标：{goal}。"
            "请提供合理的情节推进，包含冲突、转折、发展等元素。风格要求：{style}",
            ["current_situation", "goal", "style"]
        )
        
        # 对话生成模板
        self._templates[GenerationType.DIALOGUE] = PromptTemplate(
            "请为小说创作对话。参与角色：{characters}，场景：{scene}，情境：{situation}。"
            "请创作符合角色性格的自然对话。风格要求：{style}",
            ["characters", "scene", "situation", "style"]
        )
        
        # 内容优化模板
        self._templates[GenerationType.CONTENT_OPTIMIZATION] = PromptTemplate(
            "请优化以下小说内容：\n{content}\n\n"
            "优化要求：{requirements}。请保持原意的同时提升文字质量、流畅度和表现力。",
            ["content", "requirements"]
        )
    
    def get_template(self, generation_type: GenerationType) -> Optional[PromptTemplate]:
        """获取模板"""
        return self._templates.get(generation_type)
    
    def add_template(self, generation_type: GenerationType, template: PromptTemplate):
        """添加模板"""
        self._templates[generation_type] = template
    
    def format_prompt(self, generation_type: GenerationType, **kwargs) -> str:
        """格式化提示词"""
        template = self.get_template(generation_type)
        if not template:
            raise ValueError(f"未找到生成类型 {generation_type} 的模板")
        
        if not template.validate_variables(**kwargs):
            raise ValueError(f"模板变量不完整，需要：{template.variables}")
        
        return template.format(**kwargs)
