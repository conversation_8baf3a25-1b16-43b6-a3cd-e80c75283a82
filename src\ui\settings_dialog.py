"""
设置对话框模块

实现应用程序的设置和配置功能
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QStackedWidget, QWidget, QFormLayout,
    QLineEdit, QComboBox, QPushButton, QCheckBox, QSpinBox,
    QGroupBox, QTextEdit, QLabel, QFileDialog, QMessageBox,
    QTabWidget, QSlider, QColorDialog, QFontDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor

from ..core.models.enums import AIProvider


class GeneralSettingsWidget(QWidget):
    """常规设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题", "自动"])
        ui_layout.addRow("主题:", self.theme_combo)
        
        # 语言选择
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "繁体中文", "English"])
        ui_layout.addRow("语言:", self.language_combo)
        
        # 字体设置
        font_layout = QHBoxLayout()
        self.font_label = QLabel("Microsoft YaHei, 12pt")
        self.font_btn = QPushButton("选择字体...")
        self.font_btn.clicked.connect(self.select_font)
        font_layout.addWidget(self.font_label)
        font_layout.addWidget(self.font_btn)
        ui_layout.addRow("编辑器字体:", font_layout)
        
        layout.addWidget(ui_group)
        
        # 编辑器设置
        editor_group = QGroupBox("编辑器设置")
        editor_layout = QFormLayout(editor_group)
        
        # 自动保存
        self.auto_save_check = QCheckBox("启用自动保存")
        self.auto_save_check.setChecked(True)
        editor_layout.addRow("", self.auto_save_check)
        
        # 自动保存间隔
        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(30, 3600)
        self.auto_save_interval.setValue(300)
        self.auto_save_interval.setSuffix(" 秒")
        editor_layout.addRow("自动保存间隔:", self.auto_save_interval)
        
        # 显示行号
        self.show_line_numbers = QCheckBox("显示行号")
        self.show_line_numbers.setChecked(True)
        editor_layout.addRow("", self.show_line_numbers)
        
        # 自动换行
        self.word_wrap = QCheckBox("自动换行")
        self.word_wrap.setChecked(True)
        editor_layout.addRow("", self.word_wrap)
        
        # 拼写检查
        self.spell_check = QCheckBox("启用拼写检查")
        editor_layout.addRow("", self.spell_check)
        
        layout.addWidget(editor_group)
        
        # 文件设置
        file_group = QGroupBox("文件设置")
        file_layout = QFormLayout(file_group)
        
        # 默认保存路径
        save_path_layout = QHBoxLayout()
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setPlaceholderText("选择默认保存路径")
        self.save_path_btn = QPushButton("浏览...")
        self.save_path_btn.clicked.connect(self.select_save_path)
        save_path_layout.addWidget(self.save_path_edit)
        save_path_layout.addWidget(self.save_path_btn)
        file_layout.addRow("默认保存路径:", save_path_layout)
        
        # 备份设置
        self.backup_enabled = QCheckBox("启用自动备份")
        self.backup_enabled.setChecked(True)
        file_layout.addRow("", self.backup_enabled)
        
        self.backup_count = QSpinBox()
        self.backup_count.setRange(1, 50)
        self.backup_count.setValue(5)
        self.backup_count.setSuffix(" 个")
        file_layout.addRow("保留备份数量:", self.backup_count)
        
        layout.addWidget(file_group)
        
        layout.addStretch()
        
    def select_font(self):
        """选择字体"""
        current_font = QFont("Microsoft YaHei", 12)
        font, ok = QFontDialog.getFont(current_font, self)
        if ok:
            self.font_label.setText(f"{font.family()}, {font.pointSize()}pt")
            
    def select_save_path(self):
        """选择保存路径"""
        path = QFileDialog.getExistingDirectory(self, "选择默认保存路径")
        if path:
            self.save_path_edit.setText(path)


class AISettingsWidget(QWidget):
    """AI设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 创建AI服务商标签页
        self.tab_widget = QTabWidget()
        
        # OpenAI设置
        openai_tab = self.create_openai_tab()
        self.tab_widget.addTab(openai_tab, "OpenAI")
        
        # DeepSeek设置
        deepseek_tab = self.create_deepseek_tab()
        self.tab_widget.addTab(deepseek_tab, "DeepSeek")
        
        # 智谱AI设置
        zhipu_tab = self.create_zhipu_tab()
        self.tab_widget.addTab(zhipu_tab, "智谱AI")
        
        # Anthropic设置
        anthropic_tab = self.create_anthropic_tab()
        self.tab_widget.addTab(anthropic_tab, "Anthropic")
        
        layout.addWidget(self.tab_widget)
        
        # 通用AI设置
        general_group = QGroupBox("通用设置")
        general_layout = QFormLayout(general_group)
        
        # 默认AI服务商
        self.default_provider_combo = QComboBox()
        for provider in AIProvider:
            self.default_provider_combo.addItem(provider.value, provider)
        general_layout.addRow("默认AI服务商:", self.default_provider_combo)
        
        # 请求超时
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setValue(60)
        self.timeout_spin.setSuffix(" 秒")
        general_layout.addRow("请求超时:", self.timeout_spin)
        
        # 重试次数
        self.retry_count = QSpinBox()
        self.retry_count.setRange(0, 10)
        self.retry_count.setValue(3)
        self.retry_count.setSuffix(" 次")
        general_layout.addRow("重试次数:", self.retry_count)
        
        layout.addWidget(general_group)
        
    def create_openai_tab(self) -> QWidget:
        """创建OpenAI设置标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # API密钥
        self.openai_api_key = QLineEdit()
        self.openai_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.openai_api_key.setPlaceholderText("输入OpenAI API密钥")
        layout.addRow("API密钥:", self.openai_api_key)
        
        # API基础URL
        self.openai_base_url = QLineEdit()
        self.openai_base_url.setPlaceholderText("https://api.openai.com/v1")
        layout.addRow("API基础URL:", self.openai_base_url)
        
        # 模型选择
        self.openai_model = QComboBox()
        self.openai_model.addItems([
            "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo",
            "gpt-4o", "gpt-4o-mini"
        ])
        layout.addRow("默认模型:", self.openai_model)
        
        # 测试连接按钮
        self.openai_test_btn = QPushButton("测试连接")
        self.openai_test_btn.clicked.connect(lambda: self.test_connection("openai"))
        layout.addRow("", self.openai_test_btn)
        
        return widget
        
    def create_deepseek_tab(self) -> QWidget:
        """创建DeepSeek设置标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # API密钥
        self.deepseek_api_key = QLineEdit()
        self.deepseek_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.deepseek_api_key.setPlaceholderText("输入DeepSeek API密钥")
        layout.addRow("API密钥:", self.deepseek_api_key)
        
        # API基础URL
        self.deepseek_base_url = QLineEdit()
        self.deepseek_base_url.setPlaceholderText("https://api.deepseek.com/v1")
        layout.addRow("API基础URL:", self.deepseek_base_url)
        
        # 模型选择
        self.deepseek_model = QComboBox()
        self.deepseek_model.addItems([
            "deepseek-chat", "deepseek-coder"
        ])
        layout.addRow("默认模型:", self.deepseek_model)
        
        # 测试连接按钮
        self.deepseek_test_btn = QPushButton("测试连接")
        self.deepseek_test_btn.clicked.connect(lambda: self.test_connection("deepseek"))
        layout.addRow("", self.deepseek_test_btn)
        
        return widget
        
    def create_zhipu_tab(self) -> QWidget:
        """创建智谱AI设置标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # API密钥
        self.zhipu_api_key = QLineEdit()
        self.zhipu_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.zhipu_api_key.setPlaceholderText("输入智谱AI API密钥")
        layout.addRow("API密钥:", self.zhipu_api_key)
        
        # API基础URL
        self.zhipu_base_url = QLineEdit()
        self.zhipu_base_url.setPlaceholderText("https://open.bigmodel.cn/api/paas/v4")
        layout.addRow("API基础URL:", self.zhipu_base_url)
        
        # 模型选择
        self.zhipu_model = QComboBox()
        self.zhipu_model.addItems([
            "glm-4", "glm-4-turbo", "glm-3-turbo"
        ])
        layout.addRow("默认模型:", self.zhipu_model)
        
        # 测试连接按钮
        self.zhipu_test_btn = QPushButton("测试连接")
        self.zhipu_test_btn.clicked.connect(lambda: self.test_connection("zhipu"))
        layout.addRow("", self.zhipu_test_btn)
        
        return widget
        
    def create_anthropic_tab(self) -> QWidget:
        """创建Anthropic设置标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # API密钥
        self.anthropic_api_key = QLineEdit()
        self.anthropic_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.anthropic_api_key.setPlaceholderText("输入Anthropic API密钥")
        layout.addRow("API密钥:", self.anthropic_api_key)
        
        # API基础URL
        self.anthropic_base_url = QLineEdit()
        self.anthropic_base_url.setPlaceholderText("https://api.anthropic.com")
        layout.addRow("API基础URL:", self.anthropic_base_url)
        
        # 模型选择
        self.anthropic_model = QComboBox()
        self.anthropic_model.addItems([
            "claude-3-opus-20240229", "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307", "claude-3-5-sonnet-20241022"
        ])
        layout.addRow("默认模型:", self.anthropic_model)
        
        # 测试连接按钮
        self.anthropic_test_btn = QPushButton("测试连接")
        self.anthropic_test_btn.clicked.connect(lambda: self.test_connection("anthropic"))
        layout.addRow("", self.anthropic_test_btn)
        
        return widget
        
    def test_connection(self, provider: str):
        """测试AI服务连接"""
        # TODO: 实现真正的连接测试
        QMessageBox.information(
            self,
            "连接测试",
            f"{provider.upper()} 连接测试功能正在开发中"
        )


class TemplateSettingsWidget(QWidget):
    """模板设置组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 提示词模板
        prompt_group = QGroupBox("提示词模板")
        prompt_layout = QVBoxLayout(prompt_group)
        
        # 模板类型选择
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("模板类型:"))
        
        self.template_type_combo = QComboBox()
        self.template_type_combo.addItems([
            "角色描述", "场景描述", "情节发展", "对话生成", "内容优化"
        ])
        template_layout.addWidget(self.template_type_combo)
        template_layout.addStretch()
        
        prompt_layout.addLayout(template_layout)
        
        # 提示词编辑
        self.prompt_edit = QTextEdit()
        self.prompt_edit.setMinimumHeight(200)
        self.prompt_edit.setPlaceholderText("在这里编辑提示词模板...")
        prompt_layout.addWidget(self.prompt_edit)
        
        # 模板操作按钮
        template_btn_layout = QHBoxLayout()
        
        self.save_template_btn = QPushButton("保存模板")
        self.reset_template_btn = QPushButton("重置为默认")
        self.import_template_btn = QPushButton("导入模板")
        self.export_template_btn = QPushButton("导出模板")
        
        template_btn_layout.addWidget(self.save_template_btn)
        template_btn_layout.addWidget(self.reset_template_btn)
        template_btn_layout.addWidget(self.import_template_btn)
        template_btn_layout.addWidget(self.export_template_btn)
        template_btn_layout.addStretch()
        
        prompt_layout.addLayout(template_btn_layout)
        
        layout.addWidget(prompt_group)
        
        # 知识库设置
        knowledge_group = QGroupBox("知识库设置")
        knowledge_layout = QFormLayout(knowledge_group)
        
        # 知识库路径
        kb_path_layout = QHBoxLayout()
        self.kb_path_edit = QLineEdit()
        self.kb_path_edit.setPlaceholderText("选择知识库路径")
        self.kb_path_btn = QPushButton("浏览...")
        kb_path_layout.addWidget(self.kb_path_edit)
        kb_path_layout.addWidget(self.kb_path_btn)
        knowledge_layout.addRow("知识库路径:", kb_path_layout)
        
        # 启用知识库
        self.enable_kb = QCheckBox("启用知识库增强")
        knowledge_layout.addRow("", self.enable_kb)
        
        layout.addWidget(knowledge_group)
        
        layout.addStretch()


class SettingsDialog(QDialog):
    """设置对话框主类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.setMinimumSize(800, 600)
        
        layout = QHBoxLayout(self)
        
        # 左侧设置分类列表
        self.category_list = QListWidget()
        self.category_list.setMaximumWidth(150)
        self.category_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: #f8f9fa;
            }
            
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QListWidget::item:selected {
                background: #007bff;
                color: white;
            }
        """)
        
        # 添加设置分类
        categories = ["常规设置", "AI设置", "模板设置"]
        for category in categories:
            item = QListWidgetItem(category)
            self.category_list.addItem(item)
            
        layout.addWidget(self.category_list)
        
        # 右侧设置内容
        content_layout = QVBoxLayout()
        
        # 设置页面堆叠
        self.settings_stack = QStackedWidget()
        
        # 添加设置页面
        self.general_settings = GeneralSettingsWidget()
        self.ai_settings = AISettingsWidget()
        self.template_settings = TemplateSettingsWidget()
        
        self.settings_stack.addWidget(self.general_settings)
        self.settings_stack.addWidget(self.ai_settings)
        self.settings_stack.addWidget(self.template_settings)
        
        content_layout.addWidget(self.settings_stack)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存")
        self.cancel_btn = QPushButton("取消")
        self.apply_btn = QPushButton("应用")
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.apply_btn)
        
        content_layout.addLayout(button_layout)
        
        layout.addLayout(content_layout)
        
        # 连接信号
        self.setup_connections()
        
        # 默认选择第一个分类
        self.category_list.setCurrentRow(0)
        
    def setup_connections(self):
        """设置信号连接"""
        self.category_list.currentRowChanged.connect(self.settings_stack.setCurrentIndex)
        
        self.save_btn.clicked.connect(self.save_and_close)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.clicked.connect(self.apply_settings)
        
    def load_settings(self):
        """加载设置"""
        # TODO: 从配置文件加载设置
        pass
        
    def save_settings(self):
        """保存设置"""
        # TODO: 保存设置到配置文件
        try:
            # 收集所有设置数据
            settings_data = self.collect_settings_data()
            
            # 保存到配置文件
            # self.save_to_config_file(settings_data)
            
            return True
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存设置失败: {e}")
            return False
            
    def collect_settings_data(self) -> Dict[str, Any]:
        """收集设置数据"""
        return {
            'general': {
                'theme': self.general_settings.theme_combo.currentText(),
                'language': self.general_settings.language_combo.currentText(),
                'auto_save': self.general_settings.auto_save_check.isChecked(),
                'auto_save_interval': self.general_settings.auto_save_interval.value(),
                'show_line_numbers': self.general_settings.show_line_numbers.isChecked(),
                'word_wrap': self.general_settings.word_wrap.isChecked(),
                'spell_check': self.general_settings.spell_check.isChecked(),
                'save_path': self.general_settings.save_path_edit.text(),
                'backup_enabled': self.general_settings.backup_enabled.isChecked(),
                'backup_count': self.general_settings.backup_count.value(),
            },
            'ai': {
                'default_provider': self.ai_settings.default_provider_combo.currentData(),
                'timeout': self.ai_settings.timeout_spin.value(),
                'retry_count': self.ai_settings.retry_count.value(),
                'openai': {
                    'api_key': self.ai_settings.openai_api_key.text(),
                    'base_url': self.ai_settings.openai_base_url.text(),
                    'model': self.ai_settings.openai_model.currentText(),
                },
                'deepseek': {
                    'api_key': self.ai_settings.deepseek_api_key.text(),
                    'base_url': self.ai_settings.deepseek_base_url.text(),
                    'model': self.ai_settings.deepseek_model.currentText(),
                },
                'zhipu': {
                    'api_key': self.ai_settings.zhipu_api_key.text(),
                    'base_url': self.ai_settings.zhipu_base_url.text(),
                    'model': self.ai_settings.zhipu_model.currentText(),
                },
                'anthropic': {
                    'api_key': self.ai_settings.anthropic_api_key.text(),
                    'base_url': self.ai_settings.anthropic_base_url.text(),
                    'model': self.ai_settings.anthropic_model.currentText(),
                },
            },
            'template': {
                'knowledge_base_path': self.template_settings.kb_path_edit.text(),
                'enable_knowledge_base': self.template_settings.enable_kb.isChecked(),
            }
        }
        
    def apply_settings(self):
        """应用设置"""
        if self.save_settings():
            QMessageBox.information(self, "设置", "设置已应用")
            
    def save_and_close(self):
        """保存并关闭"""
        if self.save_settings():
            self.accept()
