["tests/unit/test_ai/test_adapters.py::TestAnthropicAdapter::test_build_system_prompt", "tests/unit/test_ai/test_adapters.py::TestAnthropicAdapter::test_generate_content_success", "tests/unit/test_ai/test_adapters.py::TestAnthropicAdapter::test_get_available_models", "tests/unit/test_ai/test_adapters.py::TestAnthropicAdapter::test_init", "tests/unit/test_ai/test_adapters.py::TestAnthropicAdapter::test_init_without_anthropic_library", "tests/unit/test_ai/test_adapters.py::TestDeepSeekAdapter::test_build_system_prompt", "tests/unit/test_ai/test_adapters.py::TestDeepSeekAdapter::test_get_available_models", "tests/unit/test_ai/test_adapters.py::TestDeepSeekAdapter::test_init", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_build_messages", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_build_system_prompt", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_estimate_tokens", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_generate_content_stream", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_generate_content_success", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_get_available_models", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_handle_error_authentication", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_handle_error_rate_limit", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_init", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_init_without_openai_library", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_initialize", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_validate_api_key_failure", "tests/unit/test_ai/test_adapters.py::TestOpenAIAdapter::test_validate_api_key_success", "tests/unit/test_ai/test_adapters.py::TestZhipuAdapter::test_build_system_prompt", "tests/unit/test_ai/test_adapters.py::TestZhipuAdapter::test_get_available_models", "tests/unit/test_ai/test_adapters.py::TestZhipuAdapter::test_init", "tests/unit/test_ai/test_base.py::TestAIProvider::test_provider_values", "tests/unit/test_ai/test_base.py::TestAIServiceErrors::test_ai_service_error", "tests/unit/test_ai/test_base.py::TestAIServiceErrors::test_authentication_error", "tests/unit/test_ai/test_base.py::TestAIServiceErrors::test_rate_limit_error", "tests/unit/test_ai/test_base.py::TestGenerationContext::test_init_full", "tests/unit/test_ai/test_base.py::TestGenerationContext::test_init_minimal", "tests/unit/test_ai/test_base.py::TestGenerationOptions::test_custom_values", "tests/unit/test_ai/test_base.py::TestGenerationOptions::test_default_values", "tests/unit/test_ai/test_base.py::TestGenerationResult::test_init", "tests/unit/test_ai/test_base.py::TestGenerationType::test_generation_types", "tests/unit/test_ai/test_base.py::TestPromptManager::test_add_template", "tests/unit/test_ai/test_base.py::TestPromptManager::test_format_prompt_missing_template", "tests/unit/test_ai/test_base.py::TestPromptManager::test_format_prompt_missing_variables", "tests/unit/test_ai/test_base.py::TestPromptManager::test_format_prompt_success", "tests/unit/test_ai/test_base.py::TestPromptManager::test_get_template", "tests/unit/test_ai/test_base.py::TestPromptManager::test_init", "tests/unit/test_ai/test_base.py::TestPromptTemplate::test_format", "tests/unit/test_ai/test_base.py::TestPromptTemplate::test_init", "tests/unit/test_ai/test_base.py::TestPromptTemplate::test_validate_variables_failure", "tests/unit/test_ai/test_base.py::TestPromptTemplate::test_validate_variables_success", "tests/unit/test_ai/test_base.py::TestStreamChunk::test_init_full", "tests/unit/test_ai/test_base.py::TestStreamChunk::test_init_minimal", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_stream", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_success", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_with_custom_prompt", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_generate_content_with_retry", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_get_available_providers", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_get_prompt_manager", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_health_check", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_init", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_initialize_success", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_initialize_with_errors", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_select_fallback_service", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_select_service_default", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_select_service_preferred", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_select_service_random", "tests/unit/test_ai/test_manager.py::TestAIServiceManager::test_validate_service", "tests/unit/test_ai/test_manager.py::TestContentGenerator::test_generate_character_description", "tests/unit/test_ai/test_manager.py::TestContentGenerator::test_generate_dialogue", "tests/unit/test_ai/test_manager.py::TestContentGenerator::test_generate_plot_development", "tests/unit/test_ai/test_manager.py::TestContentGenerator::test_generate_scene_description", "tests/unit/test_ai/test_manager.py::TestContentGenerator::test_optimize_content", "tests/unit/test_models/test_base.py::TestBaseModel::test_calculate_checksum", "tests/unit/test_models/test_base.py::TestBaseModel::test_clone", "tests/unit/test_models/test_base.py::TestBaseModel::test_from_dict", "tests/unit/test_models/test_base.py::TestBaseModel::test_from_json", "tests/unit/test_models/test_base.py::TestBaseModel::test_init_with_data", "tests/unit/test_models/test_base.py::TestBaseModel::test_init_with_defaults", "tests/unit/test_models/test_base.py::TestBaseModel::test_merge_custom_fields", "tests/unit/test_models/test_base.py::TestBaseModel::test_tag_operations", "tests/unit/test_models/test_base.py::TestBaseModel::test_to_dict", "tests/unit/test_models/test_base.py::TestBaseModel::test_to_json", "tests/unit/test_models/test_base.py::TestBaseModel::test_update_timestamp", "tests/unit/test_models/test_base.py::TestBaseModel::test_validate_model", "tests/unit/test_models/test_base.py::TestModelValidator::test_validate_list_length", "tests/unit/test_models/test_base.py::TestModelValidator::test_validate_optional_string", "tests/unit/test_models/test_base.py::TestModelValidator::test_validate_range", "tests/unit/test_models/test_base.py::TestModelValidator::test_validate_required_string", "tests/unit/test_models/test_base.py::TestValidationResult::test_add_error", "tests/unit/test_models/test_base.py::TestValidationResult::test_add_warning", "tests/unit/test_models/test_base.py::TestValidationResult::test_init_default", "tests/unit/test_models/test_base.py::TestValidationResult::test_merge", "tests/unit/test_models/test_base.py::TestVersionedModelClass::test_create_version_snapshot", "tests/unit/test_models/test_base.py::TestVersionedModelClass::test_get_version_history", "tests/unit/test_models/test_base.py::TestVersionedModelClass::test_restore_nonexistent_version", "tests/unit/test_models/test_base.py::TestVersionedModelClass::test_restore_version", "tests/unit/test_models/test_character.py::TestCharacter::test_add_character_development", "tests/unit/test_models/test_character.py::TestCharacter::test_add_key_moment", "tests/unit/test_models/test_character.py::TestCharacter::test_add_relationship", "tests/unit/test_models/test_character.py::TestCharacter::test_calculate_relationship_score", "tests/unit/test_models/test_character.py::TestCharacter::test_get_age", "tests/unit/test_models/test_character.py::TestCharacter::test_get_display_name", "tests/unit/test_models/test_character.py::TestCharacter::test_get_relationship", "tests/unit/test_models/test_character.py::TestCharacter::test_get_relationships_by_type", "tests/unit/test_models/test_character.py::TestCharacter::test_init_full", "tests/unit/test_models/test_character.py::TestCharacter::test_init_minimal", "tests/unit/test_models/test_character.py::TestCharacter::test_remove_relationship", "tests/unit/test_models/test_character.py::TestCharacter::test_update_relationship", "tests/unit/test_models/test_character.py::TestCharacter::test_validate_full_name", "tests/unit/test_models/test_character.py::TestCharacterAppearance::test_init_default", "tests/unit/test_models/test_character.py::TestCharacterAppearance::test_init_with_data", "tests/unit/test_models/test_character.py::TestCharacterAppearance::test_validate_age", "tests/unit/test_models/test_character.py::TestCharacterBackground::test_init_default", "tests/unit/test_models/test_character.py::TestCharacterBackground::test_init_with_data", "tests/unit/test_models/test_character.py::TestCharacterPersonality::test_init_default", "tests/unit/test_models/test_character.py::TestCharacterPersonality::test_init_with_data", "tests/unit/test_models/test_character.py::TestCharacterRelationship::test_init_full", "tests/unit/test_models/test_character.py::TestCharacterRelationship::test_init_minimal", "tests/unit/test_models/test_character.py::TestCharacterRelationship::test_validate_levels", "tests/unit/test_models/test_character.py::TestCharacterRelationship::test_validate_relationship_duration", "tests/unit/test_models/test_character.py::TestCharacterValidator::test_validate_invalid_character", "tests/unit/test_models/test_character.py::TestCharacterValidator::test_validate_valid_character", "tests/unit/test_models/test_event.py::TestEvent::test_add_participant", "tests/unit/test_models/test_event.py::TestEvent::test_add_trigger_and_consequence", "tests/unit/test_models/test_event.py::TestEvent::test_get_impact_score", "tests/unit/test_models/test_event.py::TestEvent::test_get_main_participants", "tests/unit/test_models/test_event.py::TestEvent::test_get_participant", "tests/unit/test_models/test_event.py::TestEvent::test_init_full", "tests/unit/test_models/test_event.py::TestEvent::test_init_minimal", "tests/unit/test_models/test_event.py::TestEvent::test_init_with_summary", "tests/unit/test_models/test_event.py::TestEvent::test_remove_participant", "tests/unit/test_models/test_event.py::TestEvent::test_update_participant", "tests/unit/test_models/test_event.py::TestEvent::test_validate_level_fields", "tests/unit/test_models/test_event.py::TestEvent::test_validate_model", "tests/unit/test_models/test_event.py::TestEventImpactData::test_init_default", "tests/unit/test_models/test_event.py::TestEventImpactData::test_init_with_data", "tests/unit/test_models/test_event.py::TestEventImpactData::test_validate_impact_range", "tests/unit/test_models/test_event.py::TestEventImpactData::test_validate_level_range", "tests/unit/test_models/test_event.py::TestEventParticipation::test_init_full", "tests/unit/test_models/test_event.py::TestEventParticipation::test_init_minimal", "tests/unit/test_models/test_event.py::TestEventParticipation::test_validate_involvement_level", "tests/unit/test_models/test_event.py::TestEventValidator::test_validate_incomplete_event", "tests/unit/test_models/test_event.py::TestEventValidator::test_validate_valid_event", "tests/unit/test_models/test_project.py::TestAIConfiguration::test_init_default", "tests/unit/test_models/test_project.py::TestAIConfiguration::test_validate_max_tokens", "tests/unit/test_models/test_project.py::TestAIConfiguration::test_validate_temperature", "tests/unit/test_models/test_project.py::TestProjectSettings::test_init_default", "tests/unit/test_models/test_project.py::TestProjectSettings::test_validate_auto_save_interval", "tests/unit/test_models/test_project.py::TestProjectSettings::test_validate_font_size", "tests/unit/test_models/test_project.py::TestProjectValidator::test_validate_edge_cases", "tests/unit/test_models/test_project.py::TestProjectValidator::test_validate_invalid_project", "tests/unit/test_models/test_project.py::TestProjectValidator::test_validate_valid_project", "tests/unit/test_models/test_project.py::TestWritingProject::test_add_writing_time", "tests/unit/test_models/test_project.py::TestWritingProject::test_calculate_completion_percentage", "tests/unit/test_models/test_project.py::TestWritingProject::test_get_daily_progress", "tests/unit/test_models/test_project.py::TestWritingProject::test_init_full", "tests/unit/test_models/test_project.py::TestWritingProject::test_init_minimal", "tests/unit/test_models/test_project.py::TestWritingProject::test_mark_opened", "tests/unit/test_models/test_project.py::TestWritingProject::test_update_completion_percentage", "tests/unit/test_models/test_project.py::TestWritingProject::test_update_statistics", "tests/unit/test_models/test_project.py::TestWritingProject::test_validate_completion_percentage", "tests/unit/test_models/test_project.py::TestWritingProject::test_validate_counts", "tests/unit/test_models/test_project.py::TestWritingProject::test_validate_name", "tests/unit/test_models/test_scene.py::TestScene::test_add_character", "tests/unit/test_models/test_scene.py::TestScene::test_add_event", "tests/unit/test_models/test_scene.py::TestScene::test_get_atmosphere_score", "tests/unit/test_models/test_scene.py::TestScene::test_init_full", "tests/unit/test_models/test_scene.py::TestScene::test_init_minimal", "tests/unit/test_models/test_scene.py::TestScene::test_init_with_name", "tests/unit/test_models/test_scene.py::TestScene::test_remove_character", "tests/unit/test_models/test_scene.py::TestScene::test_use_in_chapter", "tests/unit/test_models/test_scene.py::TestScene::test_validate_invalid_scene", "tests/unit/test_models/test_scene.py::TestScene::test_validate_model", "tests/unit/test_models/test_scene.py::TestSceneAtmosphere::test_init_default", "tests/unit/test_models/test_scene.py::TestSceneAtmosphere::test_init_with_data", "tests/unit/test_models/test_scene.py::TestSceneAtmosphere::test_validate_tension_level", "tests/unit/test_models/test_scene.py::TestSceneEnvironment::test_init_default", "tests/unit/test_models/test_scene.py::TestSceneEnvironment::test_init_with_data", "tests/unit/test_models/test_scene.py::TestSceneLocation::test_init_default", "tests/unit/test_models/test_scene.py::TestSceneLocation::test_init_with_data", "tests/unit/test_models/test_scene.py::TestSceneLocation::test_validate_name_length", "tests/unit/test_models/test_scene.py::TestSceneTime::test_init_default", "tests/unit/test_models/test_scene.py::TestSceneTime::test_init_with_data", "tests/unit/test_models/test_scene.py::TestSceneValidator::test_validate_incomplete_scene", "tests/unit/test_models/test_scene.py::TestSceneValidator::test_validate_valid_scene", "tests/unit/test_storage/test_backup.py::TestBackupInfo::test_from_dict", "tests/unit/test_storage/test_backup.py::TestBackupInfo::test_init_full", "tests/unit/test_storage/test_backup.py::TestBackupInfo::test_init_minimal", "tests/unit/test_storage/test_backup.py::TestBackupInfo::test_to_dict", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_cleanup_old_backups", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_compression_error_handling", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_concurrent_backup_creation", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_create_backup", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_create_backup_with_compression", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_delete_backup", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_error_handling_invalid_backup_id", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_error_handling_invalid_source", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_error_handling_permission_denied", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_get_backup_info", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_get_backup_statistics", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_init", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_list_backups", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_restore_backup", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_verify_backup_integrity", "tests/unit/test_storage/test_backup.py::TestBackupManager::test_verify_corrupted_backup", "tests/unit/test_storage/test_cache.py::TestCacheEntry::test_init", "tests/unit/test_storage/test_cache.py::TestCacheEntry::test_is_expired", "tests/unit/test_storage/test_cache.py::TestCacheEntry::test_touch", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_cleanup", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_clear", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_delete", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_disable_disk_cache", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_disk_cache_fallback", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_get_stats", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_init", "tests/unit/test_storage/test_cache.py::TestCacheManager::test_memory_cache_priority", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_clear", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_complex_data", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_delete", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_get_stats", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_init", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_set_and_get", "tests/unit/test_storage/test_cache.py::TestDiskCache::test_ttl_expiration", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_cleanup_expired", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_clear", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_delete", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_get_stats", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_init", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_lru_eviction", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_set_and_get", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_ttl_expiration", "tests/unit/test_storage/test_cache.py::TestLRUCache::test_update_existing_key", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_atomic_write", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_create_project_structure", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_create_project_structure_duplicate", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_delete_character", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_delete_project", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_init", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_list_characters", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_list_events", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_list_projects", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_list_scenes", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_load_nonexistent_character", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_load_nonexistent_project", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_character", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_event", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_project", "tests/unit/test_storage/test_file_storage.py::TestFileStorage::test_save_and_load_scene", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_backup_manager_integration", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_backup_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_cache_manager_integration", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_cache_operations", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_concurrent_access", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_delete_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_error_handling_invalid_path", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_error_handling_load_nonexistent_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_file_storage_integration", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_get_project_statistics", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_init_custom_options", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_init_default", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_list_projects", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_load_character", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_load_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_restore_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_save_character", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_save_project", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_validate_project_integrity", "tests/unit/test_storage/test_manager.py::TestStorageManager::test_version_control_integration", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_custom_encoders", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_deserialization_error", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_deserialize_basic_types", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_deserialize_character", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_deserialize_from_file", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_deserialize_project", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_get_metadata", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_roundtrip_serialization", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialization_error", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_basic_types", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_character", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_datetime", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_enum", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_project", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_to_file", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_serialize_uuid", "tests/unit/test_storage/test_serializer.py::TestJSONSerializer::test_validate_json", "tests/unit/test_ui/test_gui_integration.py::TestAIAssistantIntegration::test_ai_assistant_creation", "tests/unit/test_ui/test_gui_integration.py::TestAIAssistantIntegration::test_chat_interface", "tests/unit/test_ui/test_gui_integration.py::TestAIAssistantIntegration::test_content_generation", "tests/unit/test_ui/test_gui_integration.py::TestDataManagersIntegration::test_character_list_functionality", "tests/unit/test_ui/test_gui_integration.py::TestDataManagersIntegration::test_character_manager_creation", "tests/unit/test_ui/test_gui_integration.py::TestDataManagersIntegration::test_event_manager_creation", "tests/unit/test_ui/test_gui_integration.py::TestDataManagersIntegration::test_scene_manager_creation", "tests/unit/test_ui/test_gui_integration.py::TestDialogsIntegration::test_project_dialog_creation", "tests/unit/test_ui/test_gui_integration.py::TestDialogsIntegration::test_project_dialog_validation", "tests/unit/test_ui/test_gui_integration.py::TestDialogsIntegration::test_settings_dialog_creation", "tests/unit/test_ui/test_gui_integration.py::TestDialogsIntegration::test_settings_dialog_tabs", "tests/unit/test_ui/test_gui_integration.py::TestErrorHandling::test_ai_service_error_handling", "tests/unit/test_ui/test_gui_integration.py::TestErrorHandling::test_storage_error_handling", "tests/unit/test_ui/test_gui_integration.py::TestKeyboardShortcuts::test_main_window_shortcuts", "tests/unit/test_ui/test_gui_integration.py::TestKeyboardShortcuts::test_writing_interface_shortcuts", "tests/unit/test_ui/test_gui_integration.py::TestMainWindowIntegration::test_main_window_startup", "tests/unit/test_ui/test_gui_integration.py::TestMainWindowIntegration::test_project_creation_workflow", "tests/unit/test_ui/test_gui_integration.py::TestMainWindowIntegration::test_project_loading_workflow", "tests/unit/test_ui/test_gui_integration.py::TestMainWindowIntegration::test_welcome_widget_functionality", "tests/unit/test_ui/test_gui_integration.py::TestWritingInterfaceIntegration::test_auto_save_functionality", "tests/unit/test_ui/test_gui_integration.py::TestWritingInterfaceIntegration::test_chapter_management", "tests/unit/test_ui/test_gui_integration.py::TestWritingInterfaceIntegration::test_text_editor_functionality", "tests/unit/test_ui/test_gui_integration.py::TestWritingInterfaceIntegration::test_writing_interface_creation", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_close_event_no_project", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_close_event_with_project", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_create_new_project", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_main_window_creation", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_main_window_initial_state", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_main_window_size", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_show_new_project_dialog", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_show_open_project_dialog", "tests/unit/test_ui/test_main_window.py::TestMainWindow::test_show_settings_dialog", "tests/unit/test_ui/test_main_window.py::TestMainWindowIntegration::test_signal_connections", "tests/unit/test_ui/test_main_window.py::TestMainWindowIntegration::test_welcome_to_writing_workflow", "tests/unit/test_ui/test_main_window.py::TestWelcomeWidget::test_signal_emission", "tests/unit/test_ui/test_main_window.py::TestWelcomeWidget::test_welcome_widget_creation", "tests/unit/test_ui/test_main_window.py::TestWelcomeWidget::test_welcome_widget_ui_elements", "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_critical_character_completeness", "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_relationship_consistency", "tests/unit/test_validation/test_business_rules.py::TestCharacterBusinessRules::test_validate_relationship_timeline", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_critical_event_completeness", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_impact_consistency", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_participant_existence", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_participants", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_event_timeline_consistency", "tests/unit/test_validation/test_business_rules.py::TestEventBusinessRules::test_validate_turning_point_event", "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_ai_configuration", "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_completed_project_with_100_percent", "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_completed_project_with_low_percentage", "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_not_started_project_with_progress", "tests/unit/test_validation/test_business_rules.py::TestProjectBusinessRules::test_validate_statistics_consistency", "tests/unit/test_validation/test_business_rules.py::TestSceneBusinessRules::test_validate_critical_scene_completeness", "tests/unit/test_validation/test_business_rules.py::TestSceneBusinessRules::test_validate_scene_usage_consistency", "tests/unit/test_validation/test_business_rules.py::TestSceneBusinessRules::test_validate_scene_usage_count_error", "tests/unit/test_validation/test_rules.py::TestCustomValidationRule::test_get_error_message", "tests/unit/test_validation/test_rules.py::TestCustomValidationRule::test_validate_with_custom_function", "tests/unit/test_validation/test_rules.py::TestDateTimeValidationRule::test_validate_date_range", "tests/unit/test_validation/test_rules.py::TestDateTimeValidationRule::test_validate_future_only", "tests/unit/test_validation/test_rules.py::TestDateTimeValidationRule::test_validate_past_only", "tests/unit/test_validation/test_rules.py::TestEmailValidationRule::test_validate_invalid_emails", "tests/unit/test_validation/test_rules.py::TestEmailValidationRule::test_validate_valid_emails", "tests/unit/test_validation/test_rules.py::TestEnumValidationRule::test_validate_enum_instance", "tests/unit/test_validation/test_rules.py::TestEnumValidationRule::test_validate_enum_value", "tests/unit/test_validation/test_rules.py::TestListValidationRule::test_validate_length", "tests/unit/test_validation/test_rules.py::TestListValidationRule::test_validate_unique_items", "tests/unit/test_validation/test_rules.py::TestListValidationRule::test_validate_with_item_validator", "tests/unit/test_validation/test_rules.py::TestNumericValidationRule::test_validate_integer", "tests/unit/test_validation/test_rules.py::TestNumericValidationRule::test_validate_positive", "tests/unit/test_validation/test_rules.py::TestNumericValidationRule::test_validate_positive_with_zero", "tests/unit/test_validation/test_rules.py::TestNumericValidationRule::test_validate_range", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_get_error_message", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_init_default", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_init_with_params", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_empty_string_required", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_forbidden_chars", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_max_length", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_min_length", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_none_not_required", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_none_required", "tests/unit/test_validation/test_rules.py::TestStringValidationRule::test_validate_pattern", "tests/unit/test_validation/test_rules.py::TestURLValidationRule::test_validate_invalid_urls", "tests/unit/test_validation/test_rules.py::TestURLValidationRule::test_validate_valid_urls", "tests/unit/test_validation/test_rules.py::TestUUIDValidationRule::test_validate_uuid_instance", "tests/unit/test_validation/test_rules.py::TestUUIDValidationRule::test_validate_uuid_string", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_add_multiple_rules", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_add_rule", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_init", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_validate_field_failure", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_validate_field_no_rules", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_validate_field_success", "tests/unit/test_validation/test_validators.py::TestFieldValidator::test_validate_model", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_add_error", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_add_info", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_add_warning", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_get_related_models", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_get_validation_result", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_has_errors", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_init_default", "tests/unit/test_validation/test_validators.py::TestValidationContext::test_init_with_params", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_init", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_register_business_rule_validator", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_register_field_validator", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_register_relationship_validator", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_validate_success", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_validate_with_business_rule_validator", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_validate_with_field_validator", "tests/unit/test_validation/test_validators.py::TestValidationEngine::test_validate_with_relationship_validator"]