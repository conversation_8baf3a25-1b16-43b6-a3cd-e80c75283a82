"""JSON序列化器测试"""

import json
import tempfile
from datetime import datetime
from pathlib import Path
from uuid import UUID, uuid4
import pytest

from src.core.storage.serializer import JSONSerializer, SerializationError, DeserializationError
from src.core.models.project import WritingProject, ProjectType, ProjectStatus
from src.core.models.character import Character, Gender
from src.core.models.enums import ElementType, ImportanceLevel


class TestJSONSerializer:
    """JSON序列化器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.serializer = JSONSerializer()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_serialize_basic_types(self):
        """测试基本类型序列化"""
        # 测试基本数据类型
        test_data = {
            'string': 'test',
            'integer': 42,
            'float': 3.14,
            'boolean': True,
            'none': None,
            'list': [1, 2, 3],
            'dict': {'key': 'value'}
        }
        
        json_str = self.serializer.serialize(test_data)
        assert json_str is not None
        assert isinstance(json_str, str)
        
        # 验证JSON格式
        parsed = json.loads(json_str)
        assert parsed['string'] == 'test'
        assert parsed['integer'] == 42
        assert parsed['float'] == 3.14
        assert parsed['boolean'] is True
        assert parsed['none'] is None
        assert parsed['list'] == [1, 2, 3]
        assert parsed['dict'] == {'key': 'value'}
    
    def test_serialize_uuid(self):
        """测试UUID序列化"""
        test_uuid = uuid4()
        json_str = self.serializer.serialize(test_uuid)
        
        parsed = json.loads(json_str)
        assert parsed == str(test_uuid)
    
    def test_serialize_datetime(self):
        """测试datetime序列化"""
        test_datetime = datetime.now()
        json_str = self.serializer.serialize(test_datetime)
        
        parsed = json.loads(json_str)
        assert parsed == test_datetime.isoformat()
    
    def test_serialize_enum(self):
        """测试枚举序列化"""
        test_enum = ProjectType.FANTASY
        json_str = self.serializer.serialize(test_enum)
        
        parsed = json.loads(json_str)
        assert parsed == test_enum.value
    
    def test_serialize_project(self):
        """测试项目对象序列化"""
        project = WritingProject(
            name="测试项目",
            author="测试作者",
            project_type=ProjectType.FANTASY,
            description="测试描述",
            status=ProjectStatus.DRAFT
        )
        
        json_str = self.serializer.serialize(project)
        assert json_str is not None
        
        # 验证包含元数据
        parsed = json.loads(json_str)
        assert '__metadata__' in parsed
        assert parsed['__metadata__']['class_name'] == 'WritingProject'
        assert parsed['name'] == "测试项目"
        assert parsed['author'] == "测试作者"
    
    def test_serialize_character(self):
        """测试角色对象序列化"""
        character = Character(
            full_name="测试角色",
            gender=Gender.MALE,
            element_type=ElementType.CHARACTER,
            importance=ImportanceLevel.HIGH
        )
        
        json_str = self.serializer.serialize(character)
        assert json_str is not None
        
        parsed = json.loads(json_str)
        assert '__metadata__' in parsed
        assert parsed['__metadata__']['class_name'] == 'Character'
        assert parsed['full_name'] == "测试角色"
    
    def test_serialize_to_file(self):
        """测试序列化到文件"""
        test_data = {'key': 'value', 'number': 42}
        file_path = self.temp_dir / "test.json"
        
        json_str = self.serializer.serialize(test_data, file_path)
        
        # 验证文件存在
        assert file_path.exists()
        
        # 验证文件内容
        file_content = file_path.read_text(encoding='utf-8')
        parsed = json.loads(file_content)
        assert parsed['key'] == 'value'
        assert parsed['number'] == 42
    
    def test_deserialize_basic_types(self):
        """测试基本类型反序列化"""
        test_data = {
            'string': 'test',
            'integer': 42,
            'float': 3.14,
            'boolean': True,
            'none': None
        }
        
        json_str = json.dumps(test_data)
        result = self.serializer.deserialize(json_str)
        
        assert result['string'] == 'test'
        assert result['integer'] == 42
        assert result['float'] == 3.14
        assert result['boolean'] is True
        assert result['none'] is None
    
    def test_deserialize_project(self):
        """测试项目对象反序列化"""
        project = WritingProject(
            name="测试项目",
            author="测试作者",
            project_type=ProjectType.FANTASY
        )
        
        # 序列化
        json_str = self.serializer.serialize(project)
        
        # 反序列化
        restored_project = self.serializer.deserialize(json_str, target_class=WritingProject)
        
        assert isinstance(restored_project, WritingProject)
        assert restored_project.name == "测试项目"
        assert restored_project.author == "测试作者"
        # 由于use_enum_values=True，枚举字段会被序列化为字符串值
        assert restored_project.project_type == ProjectType.FANTASY.value
        assert isinstance(restored_project.id, UUID)
    
    def test_deserialize_character(self):
        """测试角色对象反序列化"""
        character = Character(
            full_name="测试角色",
            gender=Gender.FEMALE,
            element_type=ElementType.CHARACTER
        )
        
        # 序列化
        json_str = self.serializer.serialize(character)
        
        # 反序列化
        restored_character = self.serializer.deserialize(json_str, target_class=Character)
        
        assert isinstance(restored_character, Character)
        assert restored_character.full_name == "测试角色"
        # 由于use_enum_values=True，枚举字段会被序列化为字符串值
        assert restored_character.gender == Gender.FEMALE.value
        assert restored_character.element_type == ElementType.CHARACTER.value
        assert isinstance(restored_character.id, UUID)
    
    def test_deserialize_from_file(self):
        """测试从文件反序列化"""
        test_data = {'key': 'value', 'number': 42}
        file_path = self.temp_dir / "test.json"
        
        # 写入文件
        file_path.write_text(json.dumps(test_data), encoding='utf-8')
        
        # 从文件反序列化
        result = self.serializer.deserialize(file_path=file_path)
        
        assert result['key'] == 'value'
        assert result['number'] == 42
    
    def test_roundtrip_serialization(self):
        """测试往返序列化"""
        project = WritingProject(
            name="往返测试项目",
            author="测试作者",
            project_type=ProjectType.SCIFI,
            description="测试往返序列化",
            word_count=1000,
            chapter_count=5
        )
        
        # 序列化
        json_str = self.serializer.serialize(project)
        
        # 反序列化
        restored_project = self.serializer.deserialize(json_str, target_class=WritingProject)
        
        # 验证数据完整性
        assert restored_project.name == project.name
        assert restored_project.author == project.author
        # 由于use_enum_values=True，枚举字段会被序列化为字符串值
        # 原始对象的枚举字段也会被转换为字符串值
        assert restored_project.project_type == project.project_type
        assert restored_project.description == project.description
        assert restored_project.word_count == project.word_count
        assert restored_project.chapter_count == project.chapter_count
        assert restored_project.id == project.id
    
    def test_validate_json(self):
        """测试JSON验证"""
        # 有效JSON
        valid_json = '{"key": "value"}'
        assert self.serializer.validate_json(valid_json) is True
        
        # 无效JSON
        invalid_json = '{"key": "value"'
        assert self.serializer.validate_json(invalid_json) is False
    
    def test_get_metadata(self):
        """测试获取元数据"""
        project = WritingProject(name="测试项目")
        json_str = self.serializer.serialize(project)
        
        metadata = self.serializer.get_metadata(json_str)
        
        assert metadata['class_name'] == 'WritingProject'
        assert metadata['module_name'] == project.__class__.__module__
        assert 'serialized_at' in metadata
        assert 'serializer_version' in metadata
    
    def test_serialization_error(self):
        """测试序列化错误"""
        # 测试无效的JSON编码器
        class BadEncoder:
            def __init__(self):
                pass

            def __str__(self):
                raise Exception("Cannot convert to string")

        # 创建包含无法序列化对象的数据
        test_data = {'bad_object': BadEncoder()}

        with pytest.raises(SerializationError):
            self.serializer.serialize(test_data)
    
    def test_deserialization_error(self):
        """测试反序列化错误"""
        # 无效JSON
        with pytest.raises(DeserializationError):
            self.serializer.deserialize('invalid json')
        
        # 不存在的文件
        with pytest.raises(DeserializationError):
            self.serializer.deserialize(file_path=Path("nonexistent.json"))
    
    def test_custom_encoders(self):
        """测试自定义编码器"""
        # 测试集合类型
        test_set = {1, 2, 3}
        json_str = self.serializer.serialize(test_set)
        parsed = json.loads(json_str)
        assert sorted(parsed) == [1, 2, 3]
        
        # 测试frozenset
        test_frozenset = frozenset([4, 5, 6])
        json_str = self.serializer.serialize(test_frozenset)
        parsed = json.loads(json_str)
        assert sorted(parsed) == [4, 5, 6]
