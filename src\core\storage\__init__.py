"""存储管理模块

提供数据持久化和文件管理功能，包括：
- 文件存储管理
- JSON序列化
- 缓存机制
- 备份恢复
- 版本控制
- 数据迁移
"""

# 导入存储组件
from .manager import StorageManager, StorageError
from .file_storage import FileStorage, FileStorageError
from .cache import CacheManager, LRUCache, DiskCache, CacheError
from .backup import BackupManager, BackupInfo, BackupError
from .version_control import VersionControlManager, VersionInfo, ChangeType
from .serializer import JSONSerializer, SerializationError, DeserializationError

__all__ = [
    # 主要管理器
    "StorageManager",
    "StorageError",

    # 文件存储
    "FileStorage",
    "FileStorageError",

    # 缓存管理
    "CacheManager",
    "LRUCache",
    "DiskCache",
    "CacheError",

    # 备份管理
    "BackupManager",
    "BackupInfo",
    "BackupError",

    # 版本控制
    "VersionControlManager",
    "VersionInfo",
    "ChangeType",

    # 序列化
    "JSONSerializer",
    "SerializationError",
    "DeserializationError"
]
