# 笔落App Pre-commit配置文件
# 在提交前自动运行代码质量检查和格式化

repos:
  # 基础检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        description: 删除行尾空白字符
      - id: end-of-file-fixer
        description: 确保文件以换行符结尾
      - id: check-yaml
        description: 检查YAML文件语法
      - id: check-toml
        description: 检查TOML文件语法
      - id: check-json
        description: 检查JSON文件语法
      - id: check-merge-conflict
        description: 检查合并冲突标记
      - id: check-added-large-files
        description: 检查大文件
        args: ['--maxkb=1000']
      - id: check-case-conflict
        description: 检查文件名大小写冲突
      - id: check-executables-have-shebangs
        description: 检查可执行文件是否有shebang
      - id: check-shebang-scripts-are-executable
        description: 检查有shebang的脚本是否可执行

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        description: 使用Black格式化Python代码
        language_version: python3
        args: [--line-length=88]

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        description: 使用isort排序Python导入
        args: [--profile=black, --line-length=88]

  # 代码质量检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        description: 使用Flake8检查代码质量
        additional_dependencies:
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        description: 使用MyPy进行类型检查
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-python-dateutil
        args: [--ignore-missing-imports]

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        description: 使用Bandit检查安全问题
        args: [-r, src/, -f, json]
        exclude: tests/

  # 文档字符串检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        description: 检查文档字符串格式
        args: [--convention=google]

  # 依赖安全检查
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        description: 检查依赖包安全漏洞

# 配置选项
default_language_version:
  python: python3.11

# 在CI环境中跳过某些检查
ci:
  skip: [python-safety-dependencies-check]

# 自动修复
fail_fast: false
default_stages: [commit]
