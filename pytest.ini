[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 标记
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    gui: GUI测试
    slow: 慢速测试
    benchmark: 基准测试
    smoke: 冒烟测试

# 输出配置
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90

# 覆盖率配置
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
