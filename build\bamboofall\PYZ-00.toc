('E:\\project\\bamboofall_py_3\\build\\bamboofall\\PYZ-00.pyz',
 [('PIL', 'D:\\python3\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\python3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\python3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\python3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\python3\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\python3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\python3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\python3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\python3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\python3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\python3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\python3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\python3\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'D:\\python3\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\python3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\python3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6', 'D:\\python3\\Lib\\site-packages\\PyQt6\\__init__.py', 'PYMODULE'),
  ('__future__', 'D:\\python3\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\python3\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'D:\\python3\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\python3\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\python3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\python3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'D:\\python3\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\python3\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\python3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\python3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\python3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyrepl', 'D:\\python3\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\python3\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\python3\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\python3\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'D:\\python3\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\python3\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'D:\\python3\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\python3\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\python3\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\python3\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\python3\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\python3\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\python3\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\python3\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'D:\\python3\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'D:\\python3\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\python3\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'D:\\python3\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\python3\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\python3\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\python3\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\python3\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\python3\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\python3\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_pytest',
   'D:\\python3\\Lib\\site-packages\\_pytest\\__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_code\\__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_code\\code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_code\\source.py',
   'PYMODULE'),
  ('_pytest._io',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_io\\__init__.py',
   'PYMODULE'),
  ('_pytest._io.pprint',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_io\\pprint.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_io\\saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_io\\terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_io\\wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_py\\__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_py\\error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_py\\path.py',
   'PYMODULE'),
  ('_pytest._version',
   'D:\\python3\\Lib\\site-packages\\_pytest\\_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   'D:\\python3\\Lib\\site-packages\\_pytest\\assertion\\__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   'D:\\python3\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   'D:\\python3\\Lib\\site-packages\\_pytest\\assertion\\truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   'D:\\python3\\Lib\\site-packages\\_pytest\\assertion\\util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   'D:\\python3\\Lib\\site-packages\\_pytest\\cacheprovider.py',
   'PYMODULE'),
  ('_pytest.compat',
   'D:\\python3\\Lib\\site-packages\\_pytest\\compat.py',
   'PYMODULE'),
  ('_pytest.config',
   'D:\\python3\\Lib\\site-packages\\_pytest\\config\\__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   'D:\\python3\\Lib\\site-packages\\_pytest\\config\\argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   'D:\\python3\\Lib\\site-packages\\_pytest\\config\\compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   'D:\\python3\\Lib\\site-packages\\_pytest\\config\\exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   'D:\\python3\\Lib\\site-packages\\_pytest\\config\\findpaths.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   'D:\\python3\\Lib\\site-packages\\_pytest\\deprecated.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   'D:\\python3\\Lib\\site-packages\\_pytest\\fixtures.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   'D:\\python3\\Lib\\site-packages\\_pytest\\helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   'D:\\python3\\Lib\\site-packages\\_pytest\\hookspec.py',
   'PYMODULE'),
  ('_pytest.main',
   'D:\\python3\\Lib\\site-packages\\_pytest\\main.py',
   'PYMODULE'),
  ('_pytest.mark',
   'D:\\python3\\Lib\\site-packages\\_pytest\\mark\\__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   'D:\\python3\\Lib\\site-packages\\_pytest\\mark\\expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   'D:\\python3\\Lib\\site-packages\\_pytest\\mark\\structures.py',
   'PYMODULE'),
  ('_pytest.nodes',
   'D:\\python3\\Lib\\site-packages\\_pytest\\nodes.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   'D:\\python3\\Lib\\site-packages\\_pytest\\outcomes.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   'D:\\python3\\Lib\\site-packages\\_pytest\\pathlib.py',
   'PYMODULE'),
  ('_pytest.python',
   'D:\\python3\\Lib\\site-packages\\_pytest\\python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   'D:\\python3\\Lib\\site-packages\\_pytest\\python_api.py',
   'PYMODULE'),
  ('_pytest.raises',
   'D:\\python3\\Lib\\site-packages\\_pytest\\raises.py',
   'PYMODULE'),
  ('_pytest.reports',
   'D:\\python3\\Lib\\site-packages\\_pytest\\reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   'D:\\python3\\Lib\\site-packages\\_pytest\\runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   'D:\\python3\\Lib\\site-packages\\_pytest\\scope.py',
   'PYMODULE'),
  ('_pytest.stash',
   'D:\\python3\\Lib\\site-packages\\_pytest\\stash.py',
   'PYMODULE'),
  ('_pytest.terminal',
   'D:\\python3\\Lib\\site-packages\\_pytest\\terminal.py',
   'PYMODULE'),
  ('_pytest.timing',
   'D:\\python3\\Lib\\site-packages\\_pytest\\timing.py',
   'PYMODULE'),
  ('_pytest.tracemalloc',
   'D:\\python3\\Lib\\site-packages\\_pytest\\tracemalloc.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   'D:\\python3\\Lib\\site-packages\\_pytest\\warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   'D:\\python3\\Lib\\site-packages\\_pytest\\warnings.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\python3\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\python3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('annotated_types',
   'D:\\python3\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anthropic',
   'D:\\python3\\Lib\\site-packages\\anthropic\\__init__.py',
   'PYMODULE'),
  ('anthropic._base_client',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_base_client.py',
   'PYMODULE'),
  ('anthropic._client',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_client.py',
   'PYMODULE'),
  ('anthropic._compat',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_compat.py',
   'PYMODULE'),
  ('anthropic._constants',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_constants.py',
   'PYMODULE'),
  ('anthropic._decoders', '-', 'PYMODULE'),
  ('anthropic._decoders.jsonl',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_decoders\\jsonl.py',
   'PYMODULE'),
  ('anthropic._exceptions',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_exceptions.py',
   'PYMODULE'),
  ('anthropic._files',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_files.py',
   'PYMODULE'),
  ('anthropic._legacy_response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_legacy_response.py',
   'PYMODULE'),
  ('anthropic._models',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_models.py',
   'PYMODULE'),
  ('anthropic._qs',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_qs.py',
   'PYMODULE'),
  ('anthropic._resource',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_resource.py',
   'PYMODULE'),
  ('anthropic._response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_response.py',
   'PYMODULE'),
  ('anthropic._streaming',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_streaming.py',
   'PYMODULE'),
  ('anthropic._types',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_types.py',
   'PYMODULE'),
  ('anthropic._utils',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\__init__.py',
   'PYMODULE'),
  ('anthropic._utils._httpx',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_httpx.py',
   'PYMODULE'),
  ('anthropic._utils._logs',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_logs.py',
   'PYMODULE'),
  ('anthropic._utils._proxy',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_proxy.py',
   'PYMODULE'),
  ('anthropic._utils._reflection',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_reflection.py',
   'PYMODULE'),
  ('anthropic._utils._resources_proxy',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_resources_proxy.py',
   'PYMODULE'),
  ('anthropic._utils._streams',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_streams.py',
   'PYMODULE'),
  ('anthropic._utils._sync',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_sync.py',
   'PYMODULE'),
  ('anthropic._utils._transform',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_transform.py',
   'PYMODULE'),
  ('anthropic._utils._typing',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_typing.py',
   'PYMODULE'),
  ('anthropic._utils._utils',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_utils\\_utils.py',
   'PYMODULE'),
  ('anthropic._version',
   'D:\\python3\\Lib\\site-packages\\anthropic\\_version.py',
   'PYMODULE'),
  ('anthropic.lib',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\__init__.py',
   'PYMODULE'),
  ('anthropic.lib._extras',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\_extras\\__init__.py',
   'PYMODULE'),
  ('anthropic.lib._extras._common',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\_extras\\_common.py',
   'PYMODULE'),
  ('anthropic.lib._extras._google_auth',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\_extras\\_google_auth.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\__init__.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock._auth',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\_auth.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock._beta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\_beta.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock._beta_messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\_beta_messages.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock._client',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\_client.py',
   'PYMODULE'),
  ('anthropic.lib.bedrock._stream_decoder',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\bedrock\\_stream_decoder.py',
   'PYMODULE'),
  ('anthropic.lib.streaming',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\streaming\\__init__.py',
   'PYMODULE'),
  ('anthropic.lib.streaming._beta_messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\streaming\\_beta_messages.py',
   'PYMODULE'),
  ('anthropic.lib.streaming._beta_types',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\streaming\\_beta_types.py',
   'PYMODULE'),
  ('anthropic.lib.streaming._messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\streaming\\_messages.py',
   'PYMODULE'),
  ('anthropic.lib.streaming._types',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\streaming\\_types.py',
   'PYMODULE'),
  ('anthropic.lib.vertex',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\vertex\\__init__.py',
   'PYMODULE'),
  ('anthropic.lib.vertex._auth',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\vertex\\_auth.py',
   'PYMODULE'),
  ('anthropic.lib.vertex._beta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\vertex\\_beta.py',
   'PYMODULE'),
  ('anthropic.lib.vertex._beta_messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\vertex\\_beta_messages.py',
   'PYMODULE'),
  ('anthropic.lib.vertex._client',
   'D:\\python3\\Lib\\site-packages\\anthropic\\lib\\vertex\\_client.py',
   'PYMODULE'),
  ('anthropic.pagination',
   'D:\\python3\\Lib\\site-packages\\anthropic\\pagination.py',
   'PYMODULE'),
  ('anthropic.resources',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\__init__.py',
   'PYMODULE'),
  ('anthropic.resources.beta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('anthropic.resources.beta.beta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('anthropic.resources.beta.files',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\files.py',
   'PYMODULE'),
  ('anthropic.resources.beta.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\messages\\__init__.py',
   'PYMODULE'),
  ('anthropic.resources.beta.messages.batches',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\messages\\batches.py',
   'PYMODULE'),
  ('anthropic.resources.beta.messages.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\messages\\messages.py',
   'PYMODULE'),
  ('anthropic.resources.beta.models',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\beta\\models.py',
   'PYMODULE'),
  ('anthropic.resources.completions',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\completions.py',
   'PYMODULE'),
  ('anthropic.resources.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\messages\\__init__.py',
   'PYMODULE'),
  ('anthropic.resources.messages.batches',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\messages\\batches.py',
   'PYMODULE'),
  ('anthropic.resources.messages.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\messages\\messages.py',
   'PYMODULE'),
  ('anthropic.resources.models',
   'D:\\python3\\Lib\\site-packages\\anthropic\\resources\\models.py',
   'PYMODULE'),
  ('anthropic.types',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\__init__.py',
   'PYMODULE'),
  ('anthropic.types.anthropic_beta_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\anthropic_beta_param.py',
   'PYMODULE'),
  ('anthropic.types.base64_image_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\base64_image_source_param.py',
   'PYMODULE'),
  ('anthropic.types.base64_pdf_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\base64_pdf_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\__init__.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_base64_image_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_base64_image_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_base64_pdf_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_base64_pdf_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_base64_pdf_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_base64_pdf_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_cache_control_ephemeral_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_cache_control_ephemeral_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_cache_creation',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_cache_creation.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_char_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_char_location.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_char_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_char_location_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_content_block_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_content_block_location.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_content_block_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_content_block_location_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_page_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_page_location.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_page_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_page_location_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_search_result_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_search_result_location.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_search_result_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_search_result_location_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citation_web_search_result_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citation_web_search_result_location_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citations_config_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citations_config_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citations_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citations_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_citations_web_search_result_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_citations_web_search_result_location.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_output_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_output_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_output_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_output_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_result_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_20250522_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_20250522_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_block_content',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_block_content.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_block_param_content_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_block_param_content_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_error.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_error_code',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_error_code.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_code_execution_tool_result_error_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_code_execution_tool_result_error_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_container',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_container.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_container_upload_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_container_upload_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_container_upload_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_container_upload_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_content_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_content_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_content_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_content_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_content_block_source_content_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_content_block_source_content_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_content_block_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_content_block_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_file_document_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_file_document_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_file_image_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_file_image_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_image_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_image_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_input_json_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_input_json_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_mcp_tool_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_mcp_tool_result_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_mcp_tool_use_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_mcp_tool_use_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_mcp_tool_use_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_mcp_tool_use_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_message',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_message.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_message_delta_usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_message_delta_usage.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_message_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_message_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_message_tokens_count',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_message_tokens_count.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_metadata_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_metadata_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_model_info',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_model_info.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_plain_text_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_plain_text_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_content_block_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_content_block_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_content_block_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_content_block_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_content_block_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_content_block_start_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_content_block_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_content_block_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_message_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_message_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_message_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_message_start_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_message_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_message_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_raw_message_stream_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_raw_message_stream_event.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_redacted_thinking_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_redacted_thinking_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_redacted_thinking_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_redacted_thinking_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_request_document_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_request_document_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_request_mcp_server_tool_configuration_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_request_mcp_server_tool_configuration_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_request_mcp_server_url_definition_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_request_mcp_server_url_definition_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_request_mcp_tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_request_mcp_tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_search_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_search_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_server_tool_usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_server_tool_usage.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_server_tool_use_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_server_tool_use_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_server_tool_use_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_server_tool_use_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_signature_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_signature_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_stop_reason',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_stop_reason.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_text_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_text_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_text_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_text_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_text_citation',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_text_citation.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_text_citation_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_text_citation_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_text_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_text_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_config_disabled_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_config_disabled_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_config_enabled_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_config_enabled_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_config_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_config_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_thinking_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_thinking_delta.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_bash_20241022_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_bash_20241022_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_bash_20250124_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_bash_20250124_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_choice_any_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_choice_any_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_choice_auto_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_choice_auto_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_choice_none_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_choice_none_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_choice_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_choice_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_choice_tool_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_choice_tool_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_computer_use_20241022_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_computer_use_20241022_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_computer_use_20250124_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_computer_use_20250124_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_text_editor_20241022_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_text_editor_20241022_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_text_editor_20250124_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_text_editor_20250124_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_text_editor_20250429_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_text_editor_20250429_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_text_editor_20250728_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_text_editor_20250728_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_union_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_union_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_use_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_use_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_tool_use_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_tool_use_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_url_image_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_url_image_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_url_pdf_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_url_pdf_source_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_usage.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_result_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_20250305_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_20250305_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_request_error_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_request_error_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_block.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_block_content',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_block_content.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_block_param_content_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_block_param_content_param.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_error.py',
   'PYMODULE'),
  ('anthropic.types.beta.beta_web_search_tool_result_error_code',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\beta_web_search_tool_result_error_code.py',
   'PYMODULE'),
  ('anthropic.types.beta.deleted_file',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\deleted_file.py',
   'PYMODULE'),
  ('anthropic.types.beta.file_list_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\file_list_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.file_metadata',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\file_metadata.py',
   'PYMODULE'),
  ('anthropic.types.beta.file_upload_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\file_upload_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.message_count_tokens_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\message_count_tokens_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.message_create_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\message_create_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\__init__.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.batch_create_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\batch_create_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.batch_list_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\batch_list_params.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_deleted_message_batch',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_deleted_message_batch.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_canceled_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_canceled_result.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_errored_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_errored_result.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_expired_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_expired_result.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_individual_response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_individual_response.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_request_counts',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_request_counts.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_result.py',
   'PYMODULE'),
  ('anthropic.types.beta.messages.beta_message_batch_succeeded_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\messages\\beta_message_batch_succeeded_result.py',
   'PYMODULE'),
  ('anthropic.types.beta.model_list_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta\\model_list_params.py',
   'PYMODULE'),
  ('anthropic.types.beta_api_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_api_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_authentication_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_authentication_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_billing_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_billing_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_error_response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_error_response.py',
   'PYMODULE'),
  ('anthropic.types.beta_gateway_timeout_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_gateway_timeout_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_invalid_request_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_invalid_request_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_not_found_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_not_found_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_overloaded_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_overloaded_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_permission_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_permission_error.py',
   'PYMODULE'),
  ('anthropic.types.beta_rate_limit_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\beta_rate_limit_error.py',
   'PYMODULE'),
  ('anthropic.types.cache_control_ephemeral_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\cache_control_ephemeral_param.py',
   'PYMODULE'),
  ('anthropic.types.cache_creation',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\cache_creation.py',
   'PYMODULE'),
  ('anthropic.types.citation_char_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_char_location.py',
   'PYMODULE'),
  ('anthropic.types.citation_char_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_char_location_param.py',
   'PYMODULE'),
  ('anthropic.types.citation_content_block_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_content_block_location.py',
   'PYMODULE'),
  ('anthropic.types.citation_content_block_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_content_block_location_param.py',
   'PYMODULE'),
  ('anthropic.types.citation_page_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_page_location.py',
   'PYMODULE'),
  ('anthropic.types.citation_page_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_page_location_param.py',
   'PYMODULE'),
  ('anthropic.types.citation_search_result_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_search_result_location_param.py',
   'PYMODULE'),
  ('anthropic.types.citation_web_search_result_location_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citation_web_search_result_location_param.py',
   'PYMODULE'),
  ('anthropic.types.citations_config_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citations_config_param.py',
   'PYMODULE'),
  ('anthropic.types.citations_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citations_delta.py',
   'PYMODULE'),
  ('anthropic.types.citations_search_result_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citations_search_result_location.py',
   'PYMODULE'),
  ('anthropic.types.citations_web_search_result_location',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\citations_web_search_result_location.py',
   'PYMODULE'),
  ('anthropic.types.completion',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\completion.py',
   'PYMODULE'),
  ('anthropic.types.completion_create_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\completion_create_params.py',
   'PYMODULE'),
  ('anthropic.types.content_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block.py',
   'PYMODULE'),
  ('anthropic.types.content_block_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.content_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_param.py',
   'PYMODULE'),
  ('anthropic.types.content_block_source_content_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_source_content_param.py',
   'PYMODULE'),
  ('anthropic.types.content_block_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_source_param.py',
   'PYMODULE'),
  ('anthropic.types.content_block_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_start_event.py',
   'PYMODULE'),
  ('anthropic.types.content_block_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\content_block_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.document_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\document_block_param.py',
   'PYMODULE'),
  ('anthropic.types.image_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\image_block_param.py',
   'PYMODULE'),
  ('anthropic.types.input_json_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\input_json_delta.py',
   'PYMODULE'),
  ('anthropic.types.message',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message.py',
   'PYMODULE'),
  ('anthropic.types.message_count_tokens_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_count_tokens_params.py',
   'PYMODULE'),
  ('anthropic.types.message_count_tokens_tool_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_count_tokens_tool_param.py',
   'PYMODULE'),
  ('anthropic.types.message_create_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_create_params.py',
   'PYMODULE'),
  ('anthropic.types.message_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.message_delta_usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_delta_usage.py',
   'PYMODULE'),
  ('anthropic.types.message_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_param.py',
   'PYMODULE'),
  ('anthropic.types.message_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_start_event.py',
   'PYMODULE'),
  ('anthropic.types.message_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.message_stream_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_stream_event.py',
   'PYMODULE'),
  ('anthropic.types.message_tokens_count',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\message_tokens_count.py',
   'PYMODULE'),
  ('anthropic.types.messages',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\__init__.py',
   'PYMODULE'),
  ('anthropic.types.messages.batch_create_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\batch_create_params.py',
   'PYMODULE'),
  ('anthropic.types.messages.batch_list_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\batch_list_params.py',
   'PYMODULE'),
  ('anthropic.types.messages.deleted_message_batch',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\deleted_message_batch.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_canceled_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_canceled_result.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_errored_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_errored_result.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_expired_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_expired_result.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_individual_response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_individual_response.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_request_counts',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_request_counts.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_result.py',
   'PYMODULE'),
  ('anthropic.types.messages.message_batch_succeeded_result',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\messages\\message_batch_succeeded_result.py',
   'PYMODULE'),
  ('anthropic.types.metadata_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\metadata_param.py',
   'PYMODULE'),
  ('anthropic.types.model',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\model.py',
   'PYMODULE'),
  ('anthropic.types.model_info',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\model_info.py',
   'PYMODULE'),
  ('anthropic.types.model_list_params',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\model_list_params.py',
   'PYMODULE'),
  ('anthropic.types.model_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\model_param.py',
   'PYMODULE'),
  ('anthropic.types.plain_text_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\plain_text_source_param.py',
   'PYMODULE'),
  ('anthropic.types.raw_content_block_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_content_block_delta.py',
   'PYMODULE'),
  ('anthropic.types.raw_content_block_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_content_block_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_content_block_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_content_block_start_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_content_block_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_content_block_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_message_delta_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_message_delta_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_message_start_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_message_start_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_message_stop_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_message_stop_event.py',
   'PYMODULE'),
  ('anthropic.types.raw_message_stream_event',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\raw_message_stream_event.py',
   'PYMODULE'),
  ('anthropic.types.redacted_thinking_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\redacted_thinking_block.py',
   'PYMODULE'),
  ('anthropic.types.redacted_thinking_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\redacted_thinking_block_param.py',
   'PYMODULE'),
  ('anthropic.types.search_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\search_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.server_tool_usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\server_tool_usage.py',
   'PYMODULE'),
  ('anthropic.types.server_tool_use_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\server_tool_use_block.py',
   'PYMODULE'),
  ('anthropic.types.server_tool_use_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\server_tool_use_block_param.py',
   'PYMODULE'),
  ('anthropic.types.shared',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('anthropic.types.shared.api_error_object',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\api_error_object.py',
   'PYMODULE'),
  ('anthropic.types.shared.authentication_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\authentication_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.billing_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\billing_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.error_object',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\error_object.py',
   'PYMODULE'),
  ('anthropic.types.shared.error_response',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\error_response.py',
   'PYMODULE'),
  ('anthropic.types.shared.gateway_timeout_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\gateway_timeout_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.invalid_request_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\invalid_request_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.not_found_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\not_found_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.overloaded_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\overloaded_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.permission_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\permission_error.py',
   'PYMODULE'),
  ('anthropic.types.shared.rate_limit_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\shared\\rate_limit_error.py',
   'PYMODULE'),
  ('anthropic.types.signature_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\signature_delta.py',
   'PYMODULE'),
  ('anthropic.types.stop_reason',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\stop_reason.py',
   'PYMODULE'),
  ('anthropic.types.text_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\text_block.py',
   'PYMODULE'),
  ('anthropic.types.text_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\text_block_param.py',
   'PYMODULE'),
  ('anthropic.types.text_citation',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\text_citation.py',
   'PYMODULE'),
  ('anthropic.types.text_citation_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\text_citation_param.py',
   'PYMODULE'),
  ('anthropic.types.text_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\text_delta.py',
   'PYMODULE'),
  ('anthropic.types.thinking_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_block.py',
   'PYMODULE'),
  ('anthropic.types.thinking_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_block_param.py',
   'PYMODULE'),
  ('anthropic.types.thinking_config_disabled_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_config_disabled_param.py',
   'PYMODULE'),
  ('anthropic.types.thinking_config_enabled_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_config_enabled_param.py',
   'PYMODULE'),
  ('anthropic.types.thinking_config_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_config_param.py',
   'PYMODULE'),
  ('anthropic.types.thinking_delta',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\thinking_delta.py',
   'PYMODULE'),
  ('anthropic.types.tool_bash_20250124_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_bash_20250124_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_choice_any_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_choice_any_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_choice_auto_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_choice_auto_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_choice_none_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_choice_none_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_choice_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_choice_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_choice_tool_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_choice_tool_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_text_editor_20250124_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_text_editor_20250124_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_text_editor_20250429_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_text_editor_20250429_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_text_editor_20250728_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_text_editor_20250728_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_union_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_union_param.py',
   'PYMODULE'),
  ('anthropic.types.tool_use_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_use_block.py',
   'PYMODULE'),
  ('anthropic.types.tool_use_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\tool_use_block_param.py',
   'PYMODULE'),
  ('anthropic.types.url_image_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\url_image_source_param.py',
   'PYMODULE'),
  ('anthropic.types.url_pdf_source_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\url_pdf_source_param.py',
   'PYMODULE'),
  ('anthropic.types.usage',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\usage.py',
   'PYMODULE'),
  ('anthropic.types.web_search_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_result_block.py',
   'PYMODULE'),
  ('anthropic.types.web_search_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_20250305_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_20250305_param.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_request_error_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_request_error_param.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_result_block',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_result_block.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_result_block_content',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_result_block_content.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_result_block_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_result_block_param.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_result_block_param_content_param',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_result_block_param_content_param.py',
   'PYMODULE'),
  ('anthropic.types.web_search_tool_result_error',
   'D:\\python3\\Lib\\site-packages\\anthropic\\types\\web_search_tool_result_error.py',
   'PYMODULE'),
  ('anyio', 'D:\\python3\\Lib\\site-packages\\anyio\\__init__.py', 'PYMODULE'),
  ('anyio._backends',
   'D:\\python3\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'D:\\python3\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'D:\\python3\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._contextmanagers',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_contextmanagers.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'D:\\python3\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'D:\\python3\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'D:\\python3\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'D:\\python3\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'D:\\python3\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'D:\\python3\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'D:\\python3\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'D:\\python3\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'D:\\python3\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse', 'D:\\python3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\python3\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\python3\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\python3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\python3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\python3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\python3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants', 'D:\\python3\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\python3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\python3\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\python3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\python3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\python3\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\python3\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\python3\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\python3\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\python3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\python3\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\python3\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\python3\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\python3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\python3\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\python3\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\python3\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\python3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\python3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\python3\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\python3\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\python3\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\python3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\python3\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\python3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\python3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\python3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr', 'D:\\python3\\Lib\\site-packages\\attr\\__init__.py', 'PYMODULE'),
  ('attr._cmp', 'D:\\python3\\Lib\\site-packages\\attr\\_cmp.py', 'PYMODULE'),
  ('attr._compat',
   'D:\\python3\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\python3\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\python3\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make', 'D:\\python3\\Lib\\site-packages\\attr\\_make.py', 'PYMODULE'),
  ('attr._next_gen',
   'D:\\python3\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\python3\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\python3\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\python3\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\python3\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\python3\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\python3\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs', 'D:\\python3\\Lib\\site-packages\\attrs\\__init__.py', 'PYMODULE'),
  ('attrs.converters',
   'D:\\python3\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'D:\\python3\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'D:\\python3\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'D:\\python3\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'D:\\python3\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backports',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\python3\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\python3\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\python3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\python3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\python3\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\python3\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\python3\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\python3\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\python3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\python3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\python3\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\python3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\python3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\python3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'D:\\python3\\Lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\python3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\python3\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'D:\\python3\\Lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\python3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\python3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\python3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\python3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\python3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\python3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click', 'D:\\python3\\Lib\\site-packages\\click\\__init__.py', 'PYMODULE'),
  ('click._compat',
   'D:\\python3\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\python3\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\python3\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\python3\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core', 'D:\\python3\\Lib\\site-packages\\click\\core.py', 'PYMODULE'),
  ('click.decorators',
   'D:\\python3\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\python3\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\python3\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\python3\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\python3\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\python3\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\python3\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'D:\\python3\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\python3\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\python3\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\python3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\python3\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\python3\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\python3\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\python3\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\python3\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\python3\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\python3\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\python3\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'D:\\python3\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\python3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\python3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\python3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\python3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\python3\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\python3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\python3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\python3\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\python3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\python3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\python3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\python3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\python3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\python3\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\python3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\python3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\python3\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\python3\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\python3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\python3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\python3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\python3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\python3\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\python3\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'D:\\python3\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\python3\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\python3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\python3\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\python3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\python3\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\python3\\Lib\\dis.py', 'PYMODULE'),
  ('distro',
   'D:\\python3\\Lib\\site-packages\\distro\\__init__.py',
   'PYMODULE'),
  ('distro.distro',
   'D:\\python3\\Lib\\site-packages\\distro\\distro.py',
   'PYMODULE'),
  ('dotenv',
   'D:\\python3\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\python3\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\python3\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\python3\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\python3\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\python3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\python3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\python3\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\python3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\python3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\python3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\python3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\python3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\python3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\python3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\python3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\python3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\python3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\python3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\python3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\python3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\python3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\python3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\python3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\python3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\python3\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\python3\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\python3\\Lib\\gzip.py', 'PYMODULE'),
  ('h11', 'D:\\python3\\Lib\\site-packages\\h11\\__init__.py', 'PYMODULE'),
  ('h11._abnf', 'D:\\python3\\Lib\\site-packages\\h11\\_abnf.py', 'PYMODULE'),
  ('h11._connection',
   'D:\\python3\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\python3\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\python3\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\python3\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\python3\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state', 'D:\\python3\\Lib\\site-packages\\h11\\_state.py', 'PYMODULE'),
  ('h11._util', 'D:\\python3\\Lib\\site-packages\\h11\\_util.py', 'PYMODULE'),
  ('h11._version',
   'D:\\python3\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\python3\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\python3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\python3\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\python3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\python3\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\python3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\python3\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\python3\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\python3\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\python3\\Lib\\http\\server.py', 'PYMODULE'),
  ('httpcore',
   'D:\\python3\\Lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'D:\\python3\\Lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx', 'D:\\python3\\Lib\\site-packages\\httpx\\__init__.py', 'PYMODULE'),
  ('httpx.__version__',
   'D:\\python3\\Lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api', 'D:\\python3\\Lib\\site-packages\\httpx\\_api.py', 'PYMODULE'),
  ('httpx._auth',
   'D:\\python3\\Lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'D:\\python3\\Lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'D:\\python3\\Lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'D:\\python3\\Lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'D:\\python3\\Lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'D:\\python3\\Lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'D:\\python3\\Lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'D:\\python3\\Lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'D:\\python3\\Lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'D:\\python3\\Lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'D:\\python3\\Lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'D:\\python3\\Lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'D:\\python3\\Lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'D:\\python3\\Lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'D:\\python3\\Lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna', 'D:\\python3\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\python3\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\python3\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\python3\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\python3\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\python3\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\python3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\python3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\python3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\python3\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\python3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\python3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\python3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\python3\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\python3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\python3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\python3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\python3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('iniconfig',
   'D:\\python3\\Lib\\site-packages\\iniconfig\\__init__.py',
   'PYMODULE'),
  ('iniconfig._parse',
   'D:\\python3\\Lib\\site-packages\\iniconfig\\_parse.py',
   'PYMODULE'),
  ('iniconfig.exceptions',
   'D:\\python3\\Lib\\site-packages\\iniconfig\\exceptions.py',
   'PYMODULE'),
  ('inspect', 'D:\\python3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\python3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'D:\\python3\\Lib\\site-packages\\jaraco\\context\\__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   'D:\\python3\\Lib\\site-packages\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('jiter', 'D:\\python3\\Lib\\site-packages\\jiter\\__init__.py', 'PYMODULE'),
  ('json', 'D:\\python3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\python3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\python3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\python3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\python3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\python3\\Lib\\lzma.py', 'PYMODULE'),
  ('markdown_it',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\__init__.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\_punycode.py',
   'PYMODULE'),
  ('markdown_it.common',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\__init__.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\entities.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\html_blocks.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\html_re.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\normalize_url.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\common\\utils.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\helpers\\__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\helpers\\parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.main',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\main.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\parser_block.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\parser_inline.py',
   'PYMODULE'),
  ('markdown_it.presets',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\presets\\__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\presets\\commonmark.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\presets\\default.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\presets\\zero.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\renderer.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\ruler.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\blockquote.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\html_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\state_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_block\\table.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\block.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_core\\text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\entity.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\rules_inline\\text.py',
   'PYMODULE'),
  ('markdown_it.token',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\token.py',
   'PYMODULE'),
  ('markdown_it.utils',
   'D:\\python3\\Lib\\site-packages\\markdown_it\\utils.py',
   'PYMODULE'),
  ('mdurl', 'D:\\python3\\Lib\\site-packages\\mdurl\\__init__.py', 'PYMODULE'),
  ('mdurl._decode',
   'D:\\python3\\Lib\\site-packages\\mdurl\\_decode.py',
   'PYMODULE'),
  ('mdurl._encode',
   'D:\\python3\\Lib\\site-packages\\mdurl\\_encode.py',
   'PYMODULE'),
  ('mdurl._format',
   'D:\\python3\\Lib\\site-packages\\mdurl\\_format.py',
   'PYMODULE'),
  ('mdurl._parse',
   'D:\\python3\\Lib\\site-packages\\mdurl\\_parse.py',
   'PYMODULE'),
  ('mdurl._url', 'D:\\python3\\Lib\\site-packages\\mdurl\\_url.py', 'PYMODULE'),
  ('mimetypes', 'D:\\python3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'D:\\python3\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'D:\\python3\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'D:\\python3\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\python3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\python3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\python3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\python3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\python3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\python3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\python3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\python3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\python3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\python3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\python3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\python3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\python3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\python3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\python3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\python3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\python3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\python3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\python3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\python3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\python3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\python3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\python3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('mypy.bogus_type',
   'D:\\python3\\Lib\\site-packages\\mypy\\bogus_type.py',
   'PYMODULE'),
  ('mypy.version',
   'D:\\python3\\Lib\\site-packages\\mypy\\version.py',
   'PYMODULE'),
  ('mypy_extensions',
   'D:\\python3\\Lib\\site-packages\\mypy_extensions.py',
   'PYMODULE'),
  ('netrc', 'D:\\python3\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\python3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\python3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\python3\\Lib\\opcode.py', 'PYMODULE'),
  ('openai',
   'D:\\python3\\Lib\\site-packages\\openai\\__init__.py',
   'PYMODULE'),
  ('openai._base_client',
   'D:\\python3\\Lib\\site-packages\\openai\\_base_client.py',
   'PYMODULE'),
  ('openai._client',
   'D:\\python3\\Lib\\site-packages\\openai\\_client.py',
   'PYMODULE'),
  ('openai._compat',
   'D:\\python3\\Lib\\site-packages\\openai\\_compat.py',
   'PYMODULE'),
  ('openai._constants',
   'D:\\python3\\Lib\\site-packages\\openai\\_constants.py',
   'PYMODULE'),
  ('openai._exceptions',
   'D:\\python3\\Lib\\site-packages\\openai\\_exceptions.py',
   'PYMODULE'),
  ('openai._extras',
   'D:\\python3\\Lib\\site-packages\\openai\\_extras\\__init__.py',
   'PYMODULE'),
  ('openai._extras._common',
   'D:\\python3\\Lib\\site-packages\\openai\\_extras\\_common.py',
   'PYMODULE'),
  ('openai._extras.numpy_proxy',
   'D:\\python3\\Lib\\site-packages\\openai\\_extras\\numpy_proxy.py',
   'PYMODULE'),
  ('openai._extras.pandas_proxy',
   'D:\\python3\\Lib\\site-packages\\openai\\_extras\\pandas_proxy.py',
   'PYMODULE'),
  ('openai._extras.sounddevice_proxy',
   'D:\\python3\\Lib\\site-packages\\openai\\_extras\\sounddevice_proxy.py',
   'PYMODULE'),
  ('openai._files',
   'D:\\python3\\Lib\\site-packages\\openai\\_files.py',
   'PYMODULE'),
  ('openai._legacy_response',
   'D:\\python3\\Lib\\site-packages\\openai\\_legacy_response.py',
   'PYMODULE'),
  ('openai._models',
   'D:\\python3\\Lib\\site-packages\\openai\\_models.py',
   'PYMODULE'),
  ('openai._module_client',
   'D:\\python3\\Lib\\site-packages\\openai\\_module_client.py',
   'PYMODULE'),
  ('openai._qs', 'D:\\python3\\Lib\\site-packages\\openai\\_qs.py', 'PYMODULE'),
  ('openai._resource',
   'D:\\python3\\Lib\\site-packages\\openai\\_resource.py',
   'PYMODULE'),
  ('openai._response',
   'D:\\python3\\Lib\\site-packages\\openai\\_response.py',
   'PYMODULE'),
  ('openai._streaming',
   'D:\\python3\\Lib\\site-packages\\openai\\_streaming.py',
   'PYMODULE'),
  ('openai._types',
   'D:\\python3\\Lib\\site-packages\\openai\\_types.py',
   'PYMODULE'),
  ('openai._utils',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\__init__.py',
   'PYMODULE'),
  ('openai._utils._logs',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_logs.py',
   'PYMODULE'),
  ('openai._utils._proxy',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_proxy.py',
   'PYMODULE'),
  ('openai._utils._reflection',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_reflection.py',
   'PYMODULE'),
  ('openai._utils._resources_proxy',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_resources_proxy.py',
   'PYMODULE'),
  ('openai._utils._streams',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_streams.py',
   'PYMODULE'),
  ('openai._utils._sync',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_sync.py',
   'PYMODULE'),
  ('openai._utils._transform',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_transform.py',
   'PYMODULE'),
  ('openai._utils._typing',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_typing.py',
   'PYMODULE'),
  ('openai._utils._utils',
   'D:\\python3\\Lib\\site-packages\\openai\\_utils\\_utils.py',
   'PYMODULE'),
  ('openai._version',
   'D:\\python3\\Lib\\site-packages\\openai\\_version.py',
   'PYMODULE'),
  ('openai.lib',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\__init__.py',
   'PYMODULE'),
  ('openai.lib._old_api',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_old_api.py',
   'PYMODULE'),
  ('openai.lib._parsing',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_parsing\\__init__.py',
   'PYMODULE'),
  ('openai.lib._parsing._completions',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_parsing\\_completions.py',
   'PYMODULE'),
  ('openai.lib._parsing._responses',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_parsing\\_responses.py',
   'PYMODULE'),
  ('openai.lib._pydantic',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_pydantic.py',
   'PYMODULE'),
  ('openai.lib._tools',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\_tools.py',
   'PYMODULE'),
  ('openai.lib.azure',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\azure.py',
   'PYMODULE'),
  ('openai.lib.streaming',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming._assistants',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\_assistants.py',
   'PYMODULE'),
  ('openai.lib.streaming._deltas',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\_deltas.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._completions',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_completions.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._events',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._types',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._events',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._responses',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._types',
   'D:\\python3\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_types.py',
   'PYMODULE'),
  ('openai.pagination',
   'D:\\python3\\Lib\\site-packages\\openai\\pagination.py',
   'PYMODULE'),
  ('openai.resources',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio.audio',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\audio\\audio.py',
   'PYMODULE'),
  ('openai.resources.audio.speech',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\audio\\speech.py',
   'PYMODULE'),
  ('openai.resources.audio.transcriptions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\audio\\transcriptions.py',
   'PYMODULE'),
  ('openai.resources.audio.translations',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\audio\\translations.py',
   'PYMODULE'),
  ('openai.resources.batches',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\batches.py',
   'PYMODULE'),
  ('openai.resources.beta',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.assistants',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\assistants.py',
   'PYMODULE'),
  ('openai.resources.beta.beta',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.realtime',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.sessions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.transcription_sessions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\transcription_sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.threads',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.messages',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\messages.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.steps',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\steps.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.threads',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py',
   'PYMODULE'),
  ('openai.resources.chat',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.chat',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\chat\\chat.py',
   'PYMODULE'),
  ('openai.resources.chat.completions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.completions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.messages',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\chat\\completions\\messages.py',
   'PYMODULE'),
  ('openai.resources.completions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\completions.py',
   'PYMODULE'),
  ('openai.resources.containers',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.containers',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\containers\\containers.py',
   'PYMODULE'),
  ('openai.resources.containers.files',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\containers\\files\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.files.content',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\containers\\files\\content.py',
   'PYMODULE'),
  ('openai.resources.containers.files.files',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\containers\\files\\files.py',
   'PYMODULE'),
  ('openai.resources.conversations',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\conversations\\__init__.py',
   'PYMODULE'),
  ('openai.resources.conversations.conversations',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\conversations\\conversations.py',
   'PYMODULE'),
  ('openai.resources.conversations.items',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\conversations\\items.py',
   'PYMODULE'),
  ('openai.resources.embeddings',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\embeddings.py',
   'PYMODULE'),
  ('openai.resources.evals',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.evals',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\evals\\evals.py',
   'PYMODULE'),
  ('openai.resources.evals.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.output_items',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\evals\\runs\\output_items.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\evals\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.files',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\files.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.alpha',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\alpha.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.graders',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\graders.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.checkpoints',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.permissions',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\permissions.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.fine_tuning',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\fine_tuning.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.checkpoints',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.jobs',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\jobs.py',
   'PYMODULE'),
  ('openai.resources.images',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\images.py',
   'PYMODULE'),
  ('openai.resources.models',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\models.py',
   'PYMODULE'),
  ('openai.resources.moderations',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\moderations.py',
   'PYMODULE'),
  ('openai.resources.responses',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.resources.responses.input_items',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\responses\\input_items.py',
   'PYMODULE'),
  ('openai.resources.responses.responses',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\responses\\responses.py',
   'PYMODULE'),
  ('openai.resources.uploads',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.uploads.parts',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\uploads\\parts.py',
   'PYMODULE'),
  ('openai.resources.uploads.uploads',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\uploads\\uploads.py',
   'PYMODULE'),
  ('openai.resources.vector_stores',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.file_batches',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\vector_stores\\file_batches.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.files',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\vector_stores\\files.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.vector_stores',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\vector_stores\\vector_stores.py',
   'PYMODULE'),
  ('openai.resources.webhooks',
   'D:\\python3\\Lib\\site-packages\\openai\\resources\\webhooks.py',
   'PYMODULE'),
  ('openai.types',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio.speech_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\speech_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.speech_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\speech_model.py',
   'PYMODULE'),
  ('openai.types.audio.transcription',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_include',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_include.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_segment',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_segment.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_stream_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_stream_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_done_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_verbose',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_verbose.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_word',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\transcription_word.py',
   'PYMODULE'),
  ('openai.types.audio.translation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\translation.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\translation_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\translation_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.translation_verbose',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio\\translation_verbose.py',
   'PYMODULE'),
  ('openai.types.audio_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio_model.py',
   'PYMODULE'),
  ('openai.types.audio_response_format',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\audio_response_format.py',
   'PYMODULE'),
  ('openai.types.auto_file_chunking_strategy_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\auto_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.batch',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\batch.py',
   'PYMODULE'),
  ('openai.types.batch_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\batch_create_params.py',
   'PYMODULE'),
  ('openai.types.batch_error',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\batch_error.py',
   'PYMODULE'),
  ('openai.types.batch_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\batch_list_params.py',
   'PYMODULE'),
  ('openai.types.batch_request_counts',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\batch_request_counts.py',
   'PYMODULE'),
  ('openai.types.beta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.assistant',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_stream_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_stream_event.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\assistant_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\function_tool.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_created_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_created_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_deleted_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_deleted_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_completed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_failed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncated_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.error_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\error_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_cleared_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_cleared_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_committed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_committed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_started_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_started_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_stopped_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_stopped_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.rate_limits_updated_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\rate_limits_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_connect_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_connect_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_status',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_status.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_usage',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_usage.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_server_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_created_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_created_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_updated_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_updated_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.thread',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\thread.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_and_run_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\thread_create_and_run_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\thread_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\thread_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.thread_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\thread_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_annotation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_delta_annotation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_annotation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_delta_annotation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_part_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_part_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_content_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_delta_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.required_action_function_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\required_action_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_status',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_status.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_submit_tool_outputs_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_submit_tool_outputs_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_logs',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_logs.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_output_image',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_output_image.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.message_creation_step_details',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\message_creation_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_message_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_include',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_include.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_retrieve_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta_object.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_calls_step_details',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_calls_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\text.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta_block',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta_block.py',
   'PYMODULE'),
  ('openai.types.chat',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_allowed_tool_choice_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_allowed_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_allowed_tools_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_allowed_tools_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_assistant_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_assistant_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_chunk',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_chunk.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_input_audio_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_input_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_refusal_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_refusal_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_custom_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_custom_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_deleted.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_developer_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_developer_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_call_option_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_call_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_tool.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_custom_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_custom_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_custom_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_custom_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_function_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_function_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_union_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_union_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_modality',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_modality.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_custom_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_custom_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_prediction_content_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_prediction_content_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_reasoning_effort',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_role',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_role.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_store_message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_store_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_stream_options_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_stream_options_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_system_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_system_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_token_logprob',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_token_logprob.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_choice_option_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_union_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_union_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_user_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_user_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.completion_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\completion_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\completion_update_params.py',
   'PYMODULE'),
  ('openai.types.chat.completions',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.completions.message_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\completions\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_chat_completion',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\parsed_chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_function_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat\\parsed_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\chat_model.py',
   'PYMODULE'),
  ('openai.types.completion',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\completion.py',
   'PYMODULE'),
  ('openai.types.completion_choice',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\completion_choice.py',
   'PYMODULE'),
  ('openai.types.completion_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.completion_usage',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\completion_usage.py',
   'PYMODULE'),
  ('openai.types.container_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\container_create_params.py',
   'PYMODULE'),
  ('openai.types.container_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\container_create_response.py',
   'PYMODULE'),
  ('openai.types.container_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\container_list_params.py',
   'PYMODULE'),
  ('openai.types.container_list_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\container_list_response.py',
   'PYMODULE'),
  ('openai.types.container_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\container_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.containers',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\file_create_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\file_list_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\containers\\file_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.conversations',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\__init__.py',
   'PYMODULE'),
  ('openai.types.conversations.computer_screenshot_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\computer_screenshot_content.py',
   'PYMODULE'),
  ('openai.types.conversations.container_file_citation_body',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\container_file_citation_body.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation_create_params.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation_deleted_resource',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation_deleted_resource.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation_item.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation_item_list',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation_item_list.py',
   'PYMODULE'),
  ('openai.types.conversations.conversation_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\conversation_update_params.py',
   'PYMODULE'),
  ('openai.types.conversations.file_citation_body',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\file_citation_body.py',
   'PYMODULE'),
  ('openai.types.conversations.input_file_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\input_file_content.py',
   'PYMODULE'),
  ('openai.types.conversations.input_image_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\input_image_content.py',
   'PYMODULE'),
  ('openai.types.conversations.input_text_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\input_text_content.py',
   'PYMODULE'),
  ('openai.types.conversations.item_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\item_create_params.py',
   'PYMODULE'),
  ('openai.types.conversations.item_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\item_list_params.py',
   'PYMODULE'),
  ('openai.types.conversations.item_retrieve_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\item_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.conversations.lob_prob',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\lob_prob.py',
   'PYMODULE'),
  ('openai.types.conversations.message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\message.py',
   'PYMODULE'),
  ('openai.types.conversations.output_text_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\output_text_content.py',
   'PYMODULE'),
  ('openai.types.conversations.refusal_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\refusal_content.py',
   'PYMODULE'),
  ('openai.types.conversations.summary_text_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\summary_text_content.py',
   'PYMODULE'),
  ('openai.types.conversations.text_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\text_content.py',
   'PYMODULE'),
  ('openai.types.conversations.top_log_prob',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\top_log_prob.py',
   'PYMODULE'),
  ('openai.types.conversations.url_citation_body',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\conversations\\url_citation_body.py',
   'PYMODULE'),
  ('openai.types.create_embedding_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\create_embedding_response.py',
   'PYMODULE'),
  ('openai.types.embedding',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\embedding.py',
   'PYMODULE'),
  ('openai.types.embedding_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\embedding_create_params.py',
   'PYMODULE'),
  ('openai.types.embedding_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\embedding_model.py',
   'PYMODULE'),
  ('openai.types.eval_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_create_params.py',
   'PYMODULE'),
  ('openai.types.eval_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_create_response.py',
   'PYMODULE'),
  ('openai.types.eval_custom_data_source_config',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_custom_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_delete_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_delete_response.py',
   'PYMODULE'),
  ('openai.types.eval_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_list_params.py',
   'PYMODULE'),
  ('openai.types.eval_list_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_list_response.py',
   'PYMODULE'),
  ('openai.types.eval_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.eval_stored_completions_data_source_config',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_stored_completions_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_update_params.py',
   'PYMODULE'),
  ('openai.types.eval_update_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\eval_update_response.py',
   'PYMODULE'),
  ('openai.types.evals',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.eval_api_error',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\eval_api_error.py',
   'PYMODULE'),
  ('openai.types.evals.run_cancel_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_cancel_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_create_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_delete_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_delete_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\run_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.file_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_content.py',
   'PYMODULE'),
  ('openai.types.file_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.file_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_deleted.py',
   'PYMODULE'),
  ('openai.types.file_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.file_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_object.py',
   'PYMODULE'),
  ('openai.types.file_purpose',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\file_purpose.py',
   'PYMODULE'),
  ('openai.types.fine_tuning',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_delete_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_delete_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_event.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_integration',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration_object.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_events_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_events_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.checkpoint_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\checkpoint_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.fine_tuning_job_checkpoint',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\fine_tuning_job_checkpoint.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method_param.py',
   'PYMODULE'),
  ('openai.types.graders',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\__init__.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\multi_grader.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\multi_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\python_grader.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\python_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader_param.py',
   'PYMODULE'),
  ('openai.types.image',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image.py',
   'PYMODULE'),
  ('openai.types.image_create_variation_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_create_variation_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_edit_completed_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_edit_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_partial_image_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_edit_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_stream_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_edit_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_gen_completed_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_partial_image_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_gen_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_stream_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_gen_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_generate_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_generate_params.py',
   'PYMODULE'),
  ('openai.types.image_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\image_model.py',
   'PYMODULE'),
  ('openai.types.images_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\images_response.py',
   'PYMODULE'),
  ('openai.types.model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\model.py',
   'PYMODULE'),
  ('openai.types.model_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\model_deleted.py',
   'PYMODULE'),
  ('openai.types.moderation',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation.py',
   'PYMODULE'),
  ('openai.types.moderation_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_create_params.py',
   'PYMODULE'),
  ('openai.types.moderation_create_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_create_response.py',
   'PYMODULE'),
  ('openai.types.moderation_image_url_input_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_image_url_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_model.py',
   'PYMODULE'),
  ('openai.types.moderation_multi_modal_input_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_multi_modal_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_text_input_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\moderation_text_input_param.py',
   'PYMODULE'),
  ('openai.types.other_file_chunking_strategy_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\other_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.responses',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\computer_tool.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\computer_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.custom_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\custom_tool.py',
   'PYMODULE'),
  ('openai.types.responses.custom_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\custom_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\function_tool.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.input_item_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\input_item_list_params.py',
   'PYMODULE'),
  ('openai.types.responses.parsed_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\parsed_response.py',
   'PYMODULE'),
  ('openai.types.responses.response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_interpreting_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_interpreting_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_conversation_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_conversation_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_create_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_created_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_input_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_input_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_input_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_input_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_output',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_output.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_output_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_output_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_error',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_error.py',
   'PYMODULE'),
  ('openai.types.responses.response_error_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_error_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_failed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_searching_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_output_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_generating_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_generating_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_partial_image_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_includable',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_includable.py',
   'PYMODULE'),
  ('openai.types.responses.response_incomplete_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_incomplete_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_content.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_content_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_file.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_file_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_image.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_image_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_input_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_item_list',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_item_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_failed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_failed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_message.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_annotation_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_annotation_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_prompt.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_prompt_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_queued_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_queued_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_added_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_text_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_text_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_retrieve_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_status',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_status.py',
   'PYMODULE'),
  ('openai.types.responses.response_stream_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_stream_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_delta_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_done_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_usage',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_usage.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_completed_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_in_progress_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_searching_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_allowed',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_allowed.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_allowed_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_allowed_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_custom',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_custom.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_custom_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_custom_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_options',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_options.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.shared',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared.all_models',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\all_models.py',
   'PYMODULE'),
  ('openai.types.shared.chat_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared.comparison_filter',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared.compound_filter',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared.custom_tool_input_format',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\custom_tool_input_format.py',
   'PYMODULE'),
  ('openai.types.shared.error_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\error_object.py',
   'PYMODULE'),
  ('openai.types.shared.function_definition',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared.function_parameters',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared.metadata',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning_effort',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_schema',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text_grammar',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\response_format_text_grammar.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text_python',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\response_format_text_python.py',
   'PYMODULE'),
  ('openai.types.shared.responses_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared\\responses_model.py',
   'PYMODULE'),
  ('openai.types.shared_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared_params.chat_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared_params.comparison_filter',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.compound_filter',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.custom_tool_input_format',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\custom_tool_input_format.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_definition',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_parameters',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared_params.metadata',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning_effort',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_schema',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_text',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared_params.responses_model',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\shared_params\\responses_model.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object_param.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_param',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.upload',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\upload.py',
   'PYMODULE'),
  ('openai.types.upload_complete_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\upload_complete_params.py',
   'PYMODULE'),
  ('openai.types.upload_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\upload_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.types.uploads.part_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\uploads\\part_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads.upload_part',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\uploads\\upload_part.py',
   'PYMODULE'),
  ('openai.types.vector_store',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store.py',
   'PYMODULE'),
  ('openai.types.vector_store_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_deleted.py',
   'PYMODULE'),
  ('openai.types.vector_store_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_search_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_search_response.py',
   'PYMODULE'),
  ('openai.types.vector_store_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_store_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_list_files_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_list_files_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_content_response',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_content_response.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_create_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_list_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_update_params',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\file_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_batch',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_batch.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_deleted',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_deleted.py',
   'PYMODULE'),
  ('openai.types.webhooks',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\__init__.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_cancelled_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\batch_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_completed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\batch_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_expired_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\batch_expired_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_failed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\batch_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_canceled_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_canceled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_failed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_succeeded_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_cancelled_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_failed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_succeeded_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_cancelled_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\response_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_completed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\response_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_failed_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\response_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_incomplete_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\response_incomplete_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.unwrap_webhook_event',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\webhooks\\unwrap_webhook_event.py',
   'PYMODULE'),
  ('openai.types.websocket_connection_options',
   'D:\\python3\\Lib\\site-packages\\openai\\types\\websocket_connection_options.py',
   'PYMODULE'),
  ('openai.version',
   'D:\\python3\\Lib\\site-packages\\openai\\version.py',
   'PYMODULE'),
  ('outcome',
   'D:\\python3\\Lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'D:\\python3\\Lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'D:\\python3\\Lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'D:\\python3\\Lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\python3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\python3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\python3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\python3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\python3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\python3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\python3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\python3\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\python3\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\python3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\python3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\python3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\python3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\python3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\python3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\python3\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\python3\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\python3\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'D:\\python3\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\python3\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\python3\\Lib\\platform.py', 'PYMODULE'),
  ('pluggy',
   'D:\\python3\\Lib\\site-packages\\pluggy\\__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_version.py',
   'PYMODULE'),
  ('pluggy._warnings',
   'D:\\python3\\Lib\\site-packages\\pluggy\\_warnings.py',
   'PYMODULE'),
  ('pprint', 'D:\\python3\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\python3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\python3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\python3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pty', 'D:\\python3\\Lib\\pty.py', 'PYMODULE'),
  ('py', 'D:\\python3\\Lib\\site-packages\\py.py', 'PYMODULE'),
  ('py_compile', 'D:\\python3\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\python3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\python3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\python3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\python3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\python3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\python3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\python3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\python3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\python3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\python3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\python3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydantic',
   'D:\\python3\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'D:\\python3\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'D:\\python3\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'D:\\python3\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'D:\\python3\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'D:\\python3\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'D:\\python3\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'D:\\python3\\Lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'D:\\python3\\Lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'D:\\python3\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'D:\\python3\\Lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'D:\\python3\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'D:\\python3\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'D:\\python3\\Lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'D:\\python3\\Lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'D:\\python3\\Lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'D:\\python3\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'D:\\python3\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'D:\\python3\\Lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'D:\\python3\\Lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'D:\\python3\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'D:\\python3\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'D:\\python3\\Lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'D:\\python3\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'D:\\python3\\Lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'D:\\python3\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'D:\\python3\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'D:\\python3\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'D:\\python3\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'D:\\python3\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'D:\\python3\\Lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'D:\\python3\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'D:\\python3\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'D:\\python3\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'D:\\python3\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'D:\\python3\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'D:\\python3\\Lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'D:\\python3\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'D:\\python3\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'D:\\python3\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'D:\\python3\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc', 'D:\\python3\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\python3\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\python3\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pygments',
   'D:\\python3\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'D:\\python3\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'D:\\python3\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'D:\\python3\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'D:\\python3\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'D:\\python3\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'D:\\python3\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'D:\\python3\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'D:\\python3\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'D:\\python3\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'D:\\python3\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'D:\\python3\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'D:\\python3\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'D:\\python3\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'D:\\python3\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('queue', 'D:\\python3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\python3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\python3\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\python3\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\python3\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\python3\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\python3\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\python3\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\python3\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\python3\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\python3\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\python3\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\python3\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\python3\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\python3\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\python3\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\python3\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\python3\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\python3\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\python3\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rich', 'D:\\python3\\Lib\\site-packages\\rich\\__init__.py', 'PYMODULE'),
  ('rich.__main__',
   'D:\\python3\\Lib\\site-packages\\rich\\__main__.py',
   'PYMODULE'),
  ('rich._cell_widths',
   'D:\\python3\\Lib\\site-packages\\rich\\_cell_widths.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   'D:\\python3\\Lib\\site-packages\\rich\\_emoji_codes.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   'D:\\python3\\Lib\\site-packages\\rich\\_emoji_replace.py',
   'PYMODULE'),
  ('rich._export_format',
   'D:\\python3\\Lib\\site-packages\\rich\\_export_format.py',
   'PYMODULE'),
  ('rich._extension',
   'D:\\python3\\Lib\\site-packages\\rich\\_extension.py',
   'PYMODULE'),
  ('rich._fileno',
   'D:\\python3\\Lib\\site-packages\\rich\\_fileno.py',
   'PYMODULE'),
  ('rich._inspect',
   'D:\\python3\\Lib\\site-packages\\rich\\_inspect.py',
   'PYMODULE'),
  ('rich._log_render',
   'D:\\python3\\Lib\\site-packages\\rich\\_log_render.py',
   'PYMODULE'),
  ('rich._loop', 'D:\\python3\\Lib\\site-packages\\rich\\_loop.py', 'PYMODULE'),
  ('rich._null_file',
   'D:\\python3\\Lib\\site-packages\\rich\\_null_file.py',
   'PYMODULE'),
  ('rich._palettes',
   'D:\\python3\\Lib\\site-packages\\rich\\_palettes.py',
   'PYMODULE'),
  ('rich._pick', 'D:\\python3\\Lib\\site-packages\\rich\\_pick.py', 'PYMODULE'),
  ('rich._ratio',
   'D:\\python3\\Lib\\site-packages\\rich\\_ratio.py',
   'PYMODULE'),
  ('rich._spinners',
   'D:\\python3\\Lib\\site-packages\\rich\\_spinners.py',
   'PYMODULE'),
  ('rich._stack',
   'D:\\python3\\Lib\\site-packages\\rich\\_stack.py',
   'PYMODULE'),
  ('rich._timer',
   'D:\\python3\\Lib\\site-packages\\rich\\_timer.py',
   'PYMODULE'),
  ('rich._win32_console',
   'D:\\python3\\Lib\\site-packages\\rich\\_win32_console.py',
   'PYMODULE'),
  ('rich._windows',
   'D:\\python3\\Lib\\site-packages\\rich\\_windows.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   'D:\\python3\\Lib\\site-packages\\rich\\_windows_renderer.py',
   'PYMODULE'),
  ('rich._wrap', 'D:\\python3\\Lib\\site-packages\\rich\\_wrap.py', 'PYMODULE'),
  ('rich.abc', 'D:\\python3\\Lib\\site-packages\\rich\\abc.py', 'PYMODULE'),
  ('rich.align', 'D:\\python3\\Lib\\site-packages\\rich\\align.py', 'PYMODULE'),
  ('rich.ansi', 'D:\\python3\\Lib\\site-packages\\rich\\ansi.py', 'PYMODULE'),
  ('rich.box', 'D:\\python3\\Lib\\site-packages\\rich\\box.py', 'PYMODULE'),
  ('rich.cells', 'D:\\python3\\Lib\\site-packages\\rich\\cells.py', 'PYMODULE'),
  ('rich.color', 'D:\\python3\\Lib\\site-packages\\rich\\color.py', 'PYMODULE'),
  ('rich.color_triplet',
   'D:\\python3\\Lib\\site-packages\\rich\\color_triplet.py',
   'PYMODULE'),
  ('rich.columns',
   'D:\\python3\\Lib\\site-packages\\rich\\columns.py',
   'PYMODULE'),
  ('rich.console',
   'D:\\python3\\Lib\\site-packages\\rich\\console.py',
   'PYMODULE'),
  ('rich.constrain',
   'D:\\python3\\Lib\\site-packages\\rich\\constrain.py',
   'PYMODULE'),
  ('rich.containers',
   'D:\\python3\\Lib\\site-packages\\rich\\containers.py',
   'PYMODULE'),
  ('rich.control',
   'D:\\python3\\Lib\\site-packages\\rich\\control.py',
   'PYMODULE'),
  ('rich.default_styles',
   'D:\\python3\\Lib\\site-packages\\rich\\default_styles.py',
   'PYMODULE'),
  ('rich.emoji', 'D:\\python3\\Lib\\site-packages\\rich\\emoji.py', 'PYMODULE'),
  ('rich.errors',
   'D:\\python3\\Lib\\site-packages\\rich\\errors.py',
   'PYMODULE'),
  ('rich.file_proxy',
   'D:\\python3\\Lib\\site-packages\\rich\\file_proxy.py',
   'PYMODULE'),
  ('rich.filesize',
   'D:\\python3\\Lib\\site-packages\\rich\\filesize.py',
   'PYMODULE'),
  ('rich.highlighter',
   'D:\\python3\\Lib\\site-packages\\rich\\highlighter.py',
   'PYMODULE'),
  ('rich.json', 'D:\\python3\\Lib\\site-packages\\rich\\json.py', 'PYMODULE'),
  ('rich.jupyter',
   'D:\\python3\\Lib\\site-packages\\rich\\jupyter.py',
   'PYMODULE'),
  ('rich.live', 'D:\\python3\\Lib\\site-packages\\rich\\live.py', 'PYMODULE'),
  ('rich.live_render',
   'D:\\python3\\Lib\\site-packages\\rich\\live_render.py',
   'PYMODULE'),
  ('rich.markdown',
   'D:\\python3\\Lib\\site-packages\\rich\\markdown.py',
   'PYMODULE'),
  ('rich.markup',
   'D:\\python3\\Lib\\site-packages\\rich\\markup.py',
   'PYMODULE'),
  ('rich.measure',
   'D:\\python3\\Lib\\site-packages\\rich\\measure.py',
   'PYMODULE'),
  ('rich.padding',
   'D:\\python3\\Lib\\site-packages\\rich\\padding.py',
   'PYMODULE'),
  ('rich.pager', 'D:\\python3\\Lib\\site-packages\\rich\\pager.py', 'PYMODULE'),
  ('rich.palette',
   'D:\\python3\\Lib\\site-packages\\rich\\palette.py',
   'PYMODULE'),
  ('rich.panel', 'D:\\python3\\Lib\\site-packages\\rich\\panel.py', 'PYMODULE'),
  ('rich.pretty',
   'D:\\python3\\Lib\\site-packages\\rich\\pretty.py',
   'PYMODULE'),
  ('rich.progress',
   'D:\\python3\\Lib\\site-packages\\rich\\progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   'D:\\python3\\Lib\\site-packages\\rich\\progress_bar.py',
   'PYMODULE'),
  ('rich.protocol',
   'D:\\python3\\Lib\\site-packages\\rich\\protocol.py',
   'PYMODULE'),
  ('rich.region',
   'D:\\python3\\Lib\\site-packages\\rich\\region.py',
   'PYMODULE'),
  ('rich.repr', 'D:\\python3\\Lib\\site-packages\\rich\\repr.py', 'PYMODULE'),
  ('rich.rule', 'D:\\python3\\Lib\\site-packages\\rich\\rule.py', 'PYMODULE'),
  ('rich.scope', 'D:\\python3\\Lib\\site-packages\\rich\\scope.py', 'PYMODULE'),
  ('rich.screen',
   'D:\\python3\\Lib\\site-packages\\rich\\screen.py',
   'PYMODULE'),
  ('rich.segment',
   'D:\\python3\\Lib\\site-packages\\rich\\segment.py',
   'PYMODULE'),
  ('rich.spinner',
   'D:\\python3\\Lib\\site-packages\\rich\\spinner.py',
   'PYMODULE'),
  ('rich.status',
   'D:\\python3\\Lib\\site-packages\\rich\\status.py',
   'PYMODULE'),
  ('rich.style', 'D:\\python3\\Lib\\site-packages\\rich\\style.py', 'PYMODULE'),
  ('rich.styled',
   'D:\\python3\\Lib\\site-packages\\rich\\styled.py',
   'PYMODULE'),
  ('rich.syntax',
   'D:\\python3\\Lib\\site-packages\\rich\\syntax.py',
   'PYMODULE'),
  ('rich.table', 'D:\\python3\\Lib\\site-packages\\rich\\table.py', 'PYMODULE'),
  ('rich.terminal_theme',
   'D:\\python3\\Lib\\site-packages\\rich\\terminal_theme.py',
   'PYMODULE'),
  ('rich.text', 'D:\\python3\\Lib\\site-packages\\rich\\text.py', 'PYMODULE'),
  ('rich.theme', 'D:\\python3\\Lib\\site-packages\\rich\\theme.py', 'PYMODULE'),
  ('rich.themes',
   'D:\\python3\\Lib\\site-packages\\rich\\themes.py',
   'PYMODULE'),
  ('rich.traceback',
   'D:\\python3\\Lib\\site-packages\\rich\\traceback.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\python3\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\python3\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\python3\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\python3\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\python3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\python3\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\python3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\python3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\python3\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\python3\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\python3\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\python3\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\python3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\python3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\python3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\python3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\python3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\python3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\python3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\python3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\python3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\python3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\python3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\python3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\python3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\python3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\python3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\python3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\python3\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\python3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\python3\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\python3\\Lib\\site.py', 'PYMODULE'),
  ('sniffio',
   'D:\\python3\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'D:\\python3\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'D:\\python3\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'D:\\python3\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\python3\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\python3\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('sortedcontainers',
   'D:\\python3\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'D:\\python3\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'D:\\python3\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'D:\\python3\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('ssl', 'D:\\python3\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\python3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\python3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\python3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\python3\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\python3\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'D:\\python3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\python3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\python3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\python3\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\python3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\python3\\Lib\\tokenize.py', 'PYMODULE'),
  ('toml', 'D:\\python3\\Lib\\site-packages\\toml\\__init__.py', 'PYMODULE'),
  ('toml.decoder',
   'D:\\python3\\Lib\\site-packages\\toml\\decoder.py',
   'PYMODULE'),
  ('toml.encoder',
   'D:\\python3\\Lib\\site-packages\\toml\\encoder.py',
   'PYMODULE'),
  ('toml.tz', 'D:\\python3\\Lib\\site-packages\\toml\\tz.py', 'PYMODULE'),
  ('tomllib', 'D:\\python3\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\python3\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\python3\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\python3\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('trio', 'D:\\python3\\Lib\\site-packages\\trio\\__init__.py', 'PYMODULE'),
  ('trio._abc', 'D:\\python3\\Lib\\site-packages\\trio\\_abc.py', 'PYMODULE'),
  ('trio._channel',
   'D:\\python3\\Lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'D:\\python3\\Lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'D:\\python3\\Lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls', 'D:\\python3\\Lib\\site-packages\\trio\\_dtls.py', 'PYMODULE'),
  ('trio._file_io',
   'D:\\python3\\Lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'D:\\python3\\Lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path', 'D:\\python3\\Lib\\site-packages\\trio\\_path.py', 'PYMODULE'),
  ('trio._signals',
   'D:\\python3\\Lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'D:\\python3\\Lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl', 'D:\\python3\\Lib\\site-packages\\trio\\_ssl.py', 'PYMODULE'),
  ('trio._subprocess',
   'D:\\python3\\Lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'D:\\python3\\Lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'D:\\python3\\Lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'D:\\python3\\Lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'D:\\python3\\Lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync', 'D:\\python3\\Lib\\site-packages\\trio\\_sync.py', 'PYMODULE'),
  ('trio._threads',
   'D:\\python3\\Lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'D:\\python3\\Lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'D:\\python3\\Lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util', 'D:\\python3\\Lib\\site-packages\\trio\\_util.py', 'PYMODULE'),
  ('trio._version',
   'D:\\python3\\Lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'D:\\python3\\Lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'D:\\python3\\Lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc', 'D:\\python3\\Lib\\site-packages\\trio\\abc.py', 'PYMODULE'),
  ('trio.from_thread',
   'D:\\python3\\Lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'D:\\python3\\Lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'D:\\python3\\Lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'D:\\python3\\Lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'D:\\python3\\Lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'D:\\python3\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\python3\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\python3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'D:\\python3\\Lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'D:\\python3\\Lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'D:\\python3\\Lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('tzdata',
   'D:\\python3\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'D:\\python3\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('urllib', 'D:\\python3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\python3\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\python3\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\python3\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\python3\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\python3\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\python3\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\python3\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\python3\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\python3\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\python3\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\python3\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\python3\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\python3\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\python3\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\python3\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\python3\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\python3\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\python3\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\python3\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\python3\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\python3\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\python3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\python3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\python3\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\python3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\python3\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\python3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\python3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\python3\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\python3\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\python3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\python3\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\python3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\python3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\python3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\python3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\python3\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\python3\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\python3\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\python3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\python3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\python3\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\python3\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\python3\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\python3\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\python3\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'D:\\python3\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('zipfile', 'D:\\python3\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\python3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\python3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\python3\\Lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo', 'D:\\python3\\Lib\\zoneinfo\\__init__.py', 'PYMODULE'),
  ('zoneinfo._common', 'D:\\python3\\Lib\\zoneinfo\\_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', 'D:\\python3\\Lib\\zoneinfo\\_tzpath.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\python3\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
