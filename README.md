# 笔落App - AI辅助小说创作工具

[![CI](https://github.com/bamboofall/bamboofall-app/workflows/CI/badge.svg)](https://github.com/bamboofall/bamboofall-app/actions)
[![codecov](https://codecov.io/gh/bamboofall/bamboofall-app/branch/main/graph/badge.svg)](https://codecov.io/gh/bamboofall/bamboofall-app)
[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

笔落App是一款基于AI技术的小说创作辅助工具，旨在帮助作者提高创作效率，激发创作灵感。

## ✨ 主要功能

- 📝 **智能创作辅助**: 基于多种AI模型的内容生成和优化
- 📊 **三维故事结构**: 大纲→主线→章节的层次化管理
- 👥 **角色管理**: 完整的角色档案、关系网络和出场统计
- 🎬 **场景事件**: 详细的场景描述和事件时间线管理
- 💾 **项目管理**: 完整的项目保存、备份和版本控制
- 📤 **多格式导出**: 支持TXT、PDF、EPUB、DOCX等格式
- 🎨 **现代界面**: 基于PyQt6的现代化用户界面

## 🚀 快速开始

### 系统要求

- Python 3.11 或更高版本
- 操作系统: Windows 10+, macOS 10.15+, Ubuntu 20.04+
- 内存: 至少 4GB RAM
- 存储: 至少 1GB 可用空间

### 安装方式

#### 方式一: 自动设置脚本（推荐）

**Linux/macOS:**
```bash
# 克隆项目
git clone https://github.com/bamboofall/bamboofall-app.git
cd bamboofall-app

# 运行设置脚本
python scripts/setup_dev.py
```

**Windows:**
```cmd
# 克隆项目
git clone https://github.com/bamboofall/bamboofall-app.git
cd bamboofall-app

# 运行设置脚本
scripts\setup_dev.bat
```

#### 方式二: 手动安装

```bash
# 1. 创建虚拟环境
python -m venv .venv

# 2. 激活虚拟环境
# Linux/macOS:
source .venv/bin/activate
# Windows:
.venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 安装开发工具
pip install -e .[dev]

# 5. 设置pre-commit钩子
pre-commit install
```

### 配置

1. 复制环境配置文件:
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置AI服务API密钥:
```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here

# 其他AI服务配置...
```

### 运行应用

```bash
# 激活虚拟环境（如果未激活）
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 启动应用
python src/main.py
```

## 🛠️ 开发指南

### 项目结构

```
bamboofall-app/
├── src/                    # 源代码
│   ├── core/              # 核心业务逻辑
│   │   ├── models/        # 数据模型
│   │   ├── storage/       # 存储系统
│   │   ├── ai/           # AI服务
│   │   └── events/       # 事件系统
│   ├── ui/               # 用户界面
│   ├── utils/            # 工具函数
│   ├── config/           # 配置管理
│   └── main.py           # 应用入口
├── tests/                # 测试代码
├── docs/                 # 项目文档
├── resources/            # 资源文件
└── scripts/              # 脚本工具
```

### 开发工具

项目使用Makefile简化常用操作:

```bash
# 查看所有可用命令
make help

# 运行测试
make test

# 代码格式化
make format

# 代码质量检查
make lint

# 类型检查
make type-check

# 运行所有检查
make check-all
```

### 代码规范

- **格式化**: 使用 [Black](https://black.readthedocs.io/) 进行代码格式化
- **导入排序**: 使用 [isort](https://pycqa.github.io/isort/) 排序导入语句
- **代码质量**: 使用 [Flake8](https://flake8.pycqa.org/) 进行代码检查
- **类型检查**: 使用 [MyPy](https://mypy.readthedocs.io/) 进行静态类型检查
- **提交钩子**: 使用 [pre-commit](https://pre-commit.com/) 确保代码质量

### 测试

```bash
# 运行所有测试
pytest tests/

# 运行测试并生成覆盖率报告
pytest tests/ --cov=src --cov-report=html

# 运行特定测试
pytest tests/unit/test_models.py
```

## 📚 技术栈

- **GUI框架**: PyQt6
- **AI服务**: OpenAI, DeepSeek, 智谱AI, Anthropic
- **数据验证**: Pydantic
- **文档处理**: python-docx, reportlab, ebooklib
- **测试框架**: pytest, pytest-qt
- **代码质量**: black, flake8, mypy, pre-commit

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 贡献流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](https://github.com/bamboofall/bamboofall-app/issues)
3. 创建新的 Issue

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**笔落App开发团队**  
📧 <EMAIL>  
🌐 https://bamboofall.com
