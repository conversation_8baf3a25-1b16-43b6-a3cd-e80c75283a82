"""存储管理器

统一管理所有存储功能，包括：
- 文件存储
- 缓存管理
- 备份恢复
- 版本控制
"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type
from uuid import UUID
from datetime import datetime

from .file_storage import FileStorage, FileStorageError
from .cache import CacheManager
from .backup import BackupManager, BackupInfo
from .version_control import VersionControlManager, VersionInfo, ChangeType
from .serializer import JSONSerializer
from ..models.project import WritingProject
from ..models.character import Character
from ..models.scene import Scene
from ..models.event import Event
from ..validation.validators import ValidationEngine


class StorageError(Exception):
    """存储错误"""
    pass


class StorageManager:
    """存储管理器
    
    统一管理项目数据的存储、缓存、备份和版本控制功能
    """
    
    def __init__(self, base_path: Union[str, Path], 
                 enable_cache: bool = True,
                 enable_backup: bool = True,
                 enable_version_control: bool = True,
                 enable_validation: bool = True):
        """初始化存储管理器
        
        Args:
            base_path: 基础存储路径
            enable_cache: 是否启用缓存
            enable_backup: 是否启用备份
            enable_version_control: 是否启用版本控制
            enable_validation: 是否启用数据验证
        """
        self.base_path = Path(base_path)
        self.enable_cache = enable_cache
        self.enable_backup = enable_backup
        self.enable_version_control = enable_version_control
        self.enable_validation = enable_validation
        
        # 确保基础目录存在
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个管理器
        self.file_storage = FileStorage(self.base_path / "projects")
        
        if enable_cache:
            self.cache_manager = CacheManager(
                cache_dir=self.base_path / "cache",
                memory_cache_size=1000,
                disk_cache_size_mb=100
            )
        
        if enable_backup:
            self.backup_manager = BackupManager(
                backup_dir=self.base_path / "backups",
                file_storage=self.file_storage
            )
        
        if enable_version_control:
            self.version_manager = VersionControlManager(
                versions_dir=self.base_path / "versions"
            )
        
        if enable_validation:
            self.validator = ValidationEngine()
        
        self.serializer = JSONSerializer()
    
    # 项目管理
    def create_project(self, project: WritingProject) -> WritingProject:
        """创建新项目"""
        try:
            # 数据验证
            if self.enable_validation:
                validation_result = self.validator.validate(project)
                if not validation_result.is_valid:
                    raise StorageError(f"项目数据验证失败: {validation_result.get_error_summary()}")
            
            # 创建项目目录结构
            project_path = self.file_storage.create_project_structure(project.id)
            
            # 保存项目数据
            self.file_storage.save_project(project, project_path)
            
            # 创建版本记录
            if self.enable_version_control:
                self.version_manager.create_version(project, ChangeType.CREATE)
            
            # 创建初始备份
            if self.enable_backup:
                self.backup_manager.create_backup(project.id, "initial", "项目创建时的初始备份")
            
            # 缓存项目数据
            if self.enable_cache:
                cache_key = f"project:{project.id}"
                self.cache_manager.set(cache_key, project)
            
            return project
            
        except Exception as e:
            raise StorageError(f"创建项目失败: {e}") from e
    
    def load_project(self, project_id: UUID) -> WritingProject:
        """加载项目"""
        try:
            # 尝试从缓存获取
            if self.enable_cache:
                cache_key = f"project:{project_id}"
                cached_project = self.cache_manager.get(cache_key)
                if cached_project:
                    return cached_project
            
            # 从文件加载
            project = self.file_storage.load_project(project_id, WritingProject)
            
            # 缓存项目数据
            if self.enable_cache:
                cache_key = f"project:{project_id}"
                self.cache_manager.set(cache_key, project)
            
            return project
            
        except Exception as e:
            raise StorageError(f"加载项目失败: {e}") from e
    
    def save_project(self, project: WritingProject) -> WritingProject:
        """保存项目"""
        try:
            # 数据验证
            if self.enable_validation:
                validation_result = self.validator.validate(project)
                if not validation_result.is_valid:
                    raise StorageError(f"项目数据验证失败: {validation_result.get_error_summary()}")
            
            # 更新时间戳 - 创建新的项目对象而不是修改现有对象
            updated_project = project.model_copy(update={'updated_at': datetime.now()})

            # 保存到文件
            self.file_storage.save_project(updated_project)
            
            # 创建版本记录
            if self.enable_version_control:
                self.version_manager.create_version(project, ChangeType.UPDATE)
            
            # 更新缓存
            if self.enable_cache:
                cache_key = f"project:{project.id}"
                self.cache_manager.set(cache_key, project)
            
            return project
            
        except Exception as e:
            raise StorageError(f"保存项目失败: {e}") from e
    
    def delete_project(self, project_id: UUID) -> bool:
        """删除项目"""
        try:
            # 创建删除前备份
            if self.enable_backup:
                self.backup_manager.create_backup(project_id, "pre_delete", "删除前备份")
            
            # 删除文件
            success = self.file_storage.delete_project(project_id)
            
            # 清除缓存
            if self.enable_cache:
                cache_key = f"project:{project_id}"
                self.cache_manager.delete(cache_key)
            
            return success
            
        except Exception as e:
            raise StorageError(f"删除项目失败: {e}") from e
    
    # 角色管理
    def save_character(self, character: Character, project_id: UUID) -> Character:
        """保存角色"""
        try:
            # 数据验证
            if self.enable_validation:
                validation_result = self.validator.validate(character)
                if not validation_result.is_valid:
                    raise StorageError(f"角色数据验证失败: {validation_result.get_error_summary()}")
            
            # 更新时间戳 - 创建新的角色对象而不是修改现有对象
            updated_character = character.model_copy(update={'updated_at': datetime.now()})

            # 保存到文件
            self.file_storage.save_character(updated_character, project_id)
            
            # 创建版本记录
            if self.enable_version_control:
                change_type = ChangeType.CREATE if character.version == 1 else ChangeType.UPDATE
                self.version_manager.create_version(character, change_type)
            
            # 更新缓存
            if self.enable_cache:
                cache_key = f"character:{project_id}:{character.id}"
                self.cache_manager.set(cache_key, character)
            
            return character
            
        except Exception as e:
            raise StorageError(f"保存角色失败: {e}") from e
    
    def load_character(self, character_id: UUID, project_id: UUID) -> Character:
        """加载角色"""
        try:
            # 尝试从缓存获取
            if self.enable_cache:
                cache_key = f"character:{project_id}:{character_id}"
                cached_character = self.cache_manager.get(cache_key)
                if cached_character:
                    return cached_character
            
            # 从文件加载
            character = self.file_storage.load_character(character_id, project_id, Character)
            
            # 缓存角色数据
            if self.enable_cache:
                cache_key = f"character:{project_id}:{character_id}"
                self.cache_manager.set(cache_key, character)
            
            return character
            
        except Exception as e:
            raise StorageError(f"加载角色失败: {e}") from e
    
    def delete_character(self, character_id: UUID, project_id: UUID) -> bool:
        """删除角色"""
        try:
            # 删除文件
            success = self.file_storage.delete_character(character_id, project_id)
            
            # 清除缓存
            if self.enable_cache:
                cache_key = f"character:{project_id}:{character_id}"
                self.cache_manager.delete(cache_key)
            
            return success
            
        except Exception as e:
            raise StorageError(f"删除角色失败: {e}") from e
    
    # 场景管理
    def save_scene(self, scene: Scene, project_id: UUID) -> Scene:
        """保存场景"""
        try:
            # 数据验证
            if self.enable_validation:
                validation_result = self.validator.validate(scene)
                if not validation_result.is_valid:
                    raise StorageError(f"场景数据验证失败: {validation_result.get_error_summary()}")
            
            # 更新时间戳 - 创建新的场景对象而不是修改现有对象
            updated_scene = scene.model_copy(update={'updated_at': datetime.now()})

            # 保存到文件
            self.file_storage.save_scene(updated_scene, project_id)
            
            # 创建版本记录
            if self.enable_version_control:
                change_type = ChangeType.CREATE if scene.version == 1 else ChangeType.UPDATE
                self.version_manager.create_version(scene, change_type)
            
            # 更新缓存
            if self.enable_cache:
                cache_key = f"scene:{project_id}:{scene.id}"
                self.cache_manager.set(cache_key, scene)
            
            return scene
            
        except Exception as e:
            raise StorageError(f"保存场景失败: {e}") from e
    
    def load_scene(self, scene_id: UUID, project_id: UUID) -> Scene:
        """加载场景"""
        try:
            # 尝试从缓存获取
            if self.enable_cache:
                cache_key = f"scene:{project_id}:{scene_id}"
                cached_scene = self.cache_manager.get(cache_key)
                if cached_scene:
                    return cached_scene
            
            # 从文件加载
            scene = self.file_storage.load_scene(scene_id, project_id, Scene)
            
            # 缓存场景数据
            if self.enable_cache:
                cache_key = f"scene:{project_id}:{scene_id}"
                self.cache_manager.set(cache_key, scene)
            
            return scene
            
        except Exception as e:
            raise StorageError(f"加载场景失败: {e}") from e
    
    # 事件管理
    def save_event(self, event: Event, project_id: UUID) -> Event:
        """保存事件"""
        try:
            # 数据验证
            if self.enable_validation:
                validation_result = self.validator.validate(event)
                if not validation_result.is_valid:
                    raise StorageError(f"事件数据验证失败: {validation_result.get_error_summary()}")
            
            # 更新时间戳 - 创建新的事件对象而不是修改现有对象
            updated_event = event.model_copy(update={'updated_at': datetime.now()})

            # 保存到文件
            self.file_storage.save_event(updated_event, project_id)
            
            # 创建版本记录
            if self.enable_version_control:
                change_type = ChangeType.CREATE if event.version == 1 else ChangeType.UPDATE
                self.version_manager.create_version(event, change_type)
            
            # 更新缓存
            if self.enable_cache:
                cache_key = f"event:{project_id}:{event.id}"
                self.cache_manager.set(cache_key, event)
            
            return event
            
        except Exception as e:
            raise StorageError(f"保存事件失败: {e}") from e
    
    def load_event(self, event_id: UUID, project_id: UUID) -> Event:
        """加载事件"""
        try:
            # 尝试从缓存获取
            if self.enable_cache:
                cache_key = f"event:{project_id}:{event_id}"
                cached_event = self.cache_manager.get(cache_key)
                if cached_event:
                    return cached_event
            
            # 从文件加载
            event = self.file_storage.load_event(event_id, project_id, Event)
            
            # 缓存事件数据
            if self.enable_cache:
                cache_key = f"event:{project_id}:{event_id}"
                self.cache_manager.set(cache_key, event)
            
            return event
            
        except Exception as e:
            raise StorageError(f"加载事件失败: {e}") from e
    
    # 备份管理
    def create_backup(self, project_id: UUID, description: str = "") -> BackupInfo:
        """创建备份"""
        if not self.enable_backup:
            raise StorageError("备份功能未启用")
        
        return self.backup_manager.create_backup(project_id, "manual", description)
    
    def restore_backup(self, backup_id: str, target_project_id: Optional[UUID] = None) -> UUID:
        """恢复备份"""
        if not self.enable_backup:
            raise StorageError("备份功能未启用")
        
        return self.backup_manager.restore_backup(backup_id, target_project_id)
    
    # 版本控制
    def get_object_versions(self, object_id: UUID) -> List[VersionInfo]:
        """获取对象版本历史"""
        if not self.enable_version_control:
            raise StorageError("版本控制功能未启用")
        
        return self.version_manager.get_object_versions(object_id)
    
    def rollback_to_version(self, version_id: str, target_class: Type) -> Any:
        """回滚到指定版本"""
        if not self.enable_version_control:
            raise StorageError("版本控制功能未启用")
        
        return self.version_manager.rollback_to_version(version_id, target_class)
    
    # 统计和维护
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = {
            'projects': len(self.file_storage.list_projects())
        }
        
        if self.enable_cache:
            stats['cache'] = self.cache_manager.get_stats()
        
        if self.enable_backup:
            stats['backup'] = self.backup_manager.get_backup_stats()
        
        if self.enable_version_control:
            stats['version_control'] = self.version_manager.get_version_stats()
        
        return stats
    
    def cleanup(self, project_id: Optional[UUID] = None):
        """清理存储空间"""
        if self.enable_cache:
            self.cache_manager.cleanup()
        
        if self.enable_backup and project_id:
            self.backup_manager.cleanup_old_backups(project_id)
        
        if self.enable_version_control and project_id:
            self.version_manager.cleanup_old_versions(project_id)
