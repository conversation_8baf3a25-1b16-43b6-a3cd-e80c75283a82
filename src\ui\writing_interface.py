"""
创作界面模块

实现主要的创作界面，包括文本编辑器、章节管理、AI辅助等功能
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTextEdit, QTreeWidget, QTreeWidgetItem, QTabWidget,
    QPushButton, QLabel, QFrame, QToolBar, QMenuBar,
    QStatusBar, QDockWidget, QListWidget, QGroupBox,
    QFormLayout, QLineEdit, QSpinBox, QComboBox,
    QMessageBox, QFileDialog, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread
from PyQt6.QtGui import QFont, QTextCharFormat, QColor, QAction, QIcon

from src.core.models.project import WritingProject
from src.core.models.character import Character
from src.core.models.scene import Scene
from src.core.models.event import Event
from src.ui.ai_assistant import AIAssistantWidget
from src.ui.character_manager import CharacterManagerWidget
from src.ui.scene_manager import SceneManagerWidget
from src.ui.event_manager import EventManagerWidget


class RichTextEditor(QTextEdit):
    """富文本编辑器"""
    
    # 信号定义
    word_count_changed = pyqtSignal(int)
    content_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_editor()
        self.setup_connections()
        
    def setup_editor(self):
        """设置编辑器"""
        # 设置字体
        font = QFont("Microsoft YaHei", 12)
        self.setFont(font)
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                selection-background-color: #007bff;
                selection-color: white;
                line-height: 1.6;
            }
        """)
        
        # 设置占位符文本
        self.setPlaceholderText("开始你的创作之旅...")
        
    def setup_connections(self):
        """设置信号连接"""
        self.textChanged.connect(self.on_text_changed)
        
    def on_text_changed(self):
        """文本改变处理"""
        # 计算字数
        text = self.toPlainText()
        word_count = len(text.replace(' ', '').replace('\n', ''))
        self.word_count_changed.emit(word_count)
        self.content_changed.emit()
        
    def insert_text(self, text: str):
        """插入文本"""
        cursor = self.textCursor()
        cursor.insertText(text)
        
    def get_selected_text(self) -> str:
        """获取选中的文本"""
        return self.textCursor().selectedText()
        
    def replace_selected_text(self, text: str):
        """替换选中的文本"""
        cursor = self.textCursor()
        cursor.insertText(text)


class ChapterTreeWidget(QTreeWidget):
    """章节树形控件"""
    
    # 信号定义
    chapter_selected = pyqtSignal(str)  # 章节ID
    chapter_added = pyqtSignal(str)     # 章节名称
    chapter_deleted = pyqtSignal(str)   # 章节ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_tree()
        
    def setup_tree(self):
        """设置树形控件"""
        self.setHeaderLabel("章节结构")
        self.setRootIsDecorated(True)
        self.setAlternatingRowColors(True)
        
        # 设置样式
        self.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ced4da;
                border-radius: 4px;
                background: white;
                alternate-background-color: #f8f9fa;
            }
            
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e9ecef;
            }
            
            QTreeWidget::item:selected {
                background: #007bff;
                color: white;
            }
            
            QTreeWidget::item:hover {
                background: #e3f2fd;
            }
        """)
        
        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
        
    def add_chapter(self, title: str, parent_item: Optional[QTreeWidgetItem] = None):
        """添加章节"""
        item = QTreeWidgetItem()
        item.setText(0, title)
        item.setData(0, Qt.ItemDataRole.UserRole, f"chapter_{len(self.findItems('', Qt.MatchFlag.MatchContains | Qt.MatchFlag.MatchRecursive))}")
        
        if parent_item:
            parent_item.addChild(item)
        else:
            self.addTopLevelItem(item)
            
        return item
        
    def on_item_clicked(self, item: QTreeWidgetItem, column: int):
        """处理项目点击"""
        chapter_id = item.data(0, Qt.ItemDataRole.UserRole)
        if chapter_id:
            self.chapter_selected.emit(chapter_id)


class WritingStatsWidget(QWidget):
    """写作统计组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 统计信息
        stats_group = QGroupBox("写作统计")
        stats_layout = QFormLayout(stats_group)
        
        self.word_count_label = QLabel("0")
        self.char_count_label = QLabel("0")
        self.chapter_count_label = QLabel("0")
        self.progress_bar = QProgressBar()
        
        stats_layout.addRow("字数:", self.word_count_label)
        stats_layout.addRow("字符数:", self.char_count_label)
        stats_layout.addRow("章节数:", self.chapter_count_label)
        stats_layout.addRow("进度:", self.progress_bar)
        
        layout.addWidget(stats_group)
        
        # 今日统计
        today_group = QGroupBox("今日统计")
        today_layout = QFormLayout(today_group)
        
        self.today_words_label = QLabel("0")
        self.today_time_label = QLabel("0分钟")
        
        today_layout.addRow("今日字数:", self.today_words_label)
        today_layout.addRow("写作时间:", self.today_time_label)
        
        layout.addWidget(today_group)
        
        layout.addStretch()
        
    def update_stats(self, word_count: int, char_count: int, chapter_count: int, target_words: int = 100000):
        """更新统计信息"""
        self.word_count_label.setText(f"{word_count:,}")
        self.char_count_label.setText(f"{char_count:,}")
        self.chapter_count_label.setText(str(chapter_count))
        
        # 更新进度条
        progress = min(100, int(word_count / target_words * 100))
        self.progress_bar.setValue(progress)
        self.progress_bar.setFormat(f"{progress}% ({word_count:,}/{target_words:,})")


class WritingInterface(QWidget):
    """创作界面主类"""
    
    def __init__(self, project: WritingProject, parent=None):
        super().__init__(parent)
        self.project = project
        self.current_chapter_id: Optional[str] = None
        self.auto_save_timer = QTimer()
        
        self.setup_ui()
        self.setup_connections()
        self.setup_auto_save()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # 中央编辑区域
        central_widget = self.create_central_widget()
        main_splitter.addWidget(central_widget)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([250, 600, 300])
        main_layout.addWidget(main_splitter)
        
        # 状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
        
    def create_toolbar(self) -> QToolBar:
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        
        # 文件操作
        save_action = QAction("保存", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 编辑操作
        undo_action = QAction("撤销", self)
        undo_action.setShortcut("Ctrl+Z")
        toolbar.addAction(undo_action)
        
        redo_action = QAction("重做", self)
        redo_action.setShortcut("Ctrl+Y")
        toolbar.addAction(redo_action)
        
        toolbar.addSeparator()
        
        # AI辅助
        ai_action = QAction("AI辅助", self)
        ai_action.triggered.connect(self.show_ai_assistant)
        toolbar.addAction(ai_action)
        
        return toolbar
        
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 章节管理
        self.chapter_tree = ChapterTreeWidget()
        layout.addWidget(self.chapter_tree)
        
        # 章节操作按钮
        button_layout = QHBoxLayout()
        
        add_chapter_btn = QPushButton("新增章节")
        add_chapter_btn.clicked.connect(self.add_chapter)
        
        delete_chapter_btn = QPushButton("删除章节")
        delete_chapter_btn.clicked.connect(self.delete_chapter)
        
        button_layout.addWidget(add_chapter_btn)
        button_layout.addWidget(delete_chapter_btn)
        
        layout.addLayout(button_layout)
        
        return panel
        
    def create_central_widget(self) -> QWidget:
        """创建中央编辑区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 章节标题
        self.chapter_title_label = QLabel("选择章节开始创作")
        self.chapter_title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        self.chapter_title_label.setStyleSheet("color: #495057; padding: 10px;")
        layout.addWidget(self.chapter_title_label)
        
        # 文本编辑器
        self.text_editor = RichTextEditor()
        layout.addWidget(self.text_editor)
        
        return widget
        
    def create_right_panel(self) -> QTabWidget:
        """创建右侧面板"""
        tab_widget = QTabWidget()
        
        # 写作统计
        self.stats_widget = WritingStatsWidget()
        tab_widget.addTab(self.stats_widget, "统计")
        
        # AI助手
        self.ai_assistant = AIAssistantWidget(self.project)
        tab_widget.addTab(self.ai_assistant, "AI助手")
        
        # 角色管理
        self.character_manager = CharacterManagerWidget(self.project)
        tab_widget.addTab(self.character_manager, "角色")
        
        # 场景管理
        self.scene_manager = SceneManagerWidget(self.project)
        tab_widget.addTab(self.scene_manager, "场景")
        
        # 事件管理
        self.event_manager = EventManagerWidget(self.project)
        tab_widget.addTab(self.event_manager, "事件")
        
        return tab_widget
        
    def create_status_bar(self) -> QWidget:
        """创建状态栏"""
        status_bar = QWidget()
        layout = QHBoxLayout(status_bar)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 状态信息
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 字数统计
        self.word_count_status = QLabel("字数: 0")
        layout.addWidget(self.word_count_status)
        
        # 自动保存状态
        self.auto_save_status = QLabel("自动保存: 开启")
        layout.addWidget(self.auto_save_status)
        
        # 设置样式
        status_bar.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
            QLabel {
                color: #6c757d;
                font-size: 12px;
            }
        """)
        
        return status_bar
        
    def setup_connections(self):
        """设置信号连接"""
        # 章节树信号
        self.chapter_tree.chapter_selected.connect(self.load_chapter)
        
        # 编辑器信号
        self.text_editor.word_count_changed.connect(self.update_word_count)
        self.text_editor.content_changed.connect(self.on_content_changed)
        
        # AI助手信号
        self.ai_assistant.text_generated.connect(self.insert_generated_text)
        
    def setup_auto_save(self):
        """设置自动保存"""
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # 30秒自动保存
        
    def add_chapter(self):
        """添加章节"""
        # 简单实现，实际应该弹出对话框
        chapter_count = self.chapter_tree.topLevelItemCount()
        title = f"第{chapter_count + 1}章"
        
        item = self.chapter_tree.add_chapter(title)
        self.chapter_tree.setCurrentItem(item)
        
    def delete_chapter(self):
        """删除章节"""
        current_item = self.chapter_tree.currentItem()
        if current_item:
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除章节 '{current_item.text(0)}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                index = self.chapter_tree.indexOfTopLevelItem(current_item)
                self.chapter_tree.takeTopLevelItem(index)
                
    def load_chapter(self, chapter_id: str):
        """加载章节内容"""
        self.current_chapter_id = chapter_id
        
        # 更新章节标题
        current_item = self.chapter_tree.currentItem()
        if current_item:
            self.chapter_title_label.setText(current_item.text(0))
            
        # TODO: 从存储加载章节内容
        # 这里先清空编辑器
        self.text_editor.clear()
        
    def save_project(self):
        """保存项目"""
        try:
            # TODO: 实现项目保存逻辑
            self.status_label.setText("项目已保存")
            QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存项目时发生错误: {e}")
            
    def auto_save(self):
        """自动保存"""
        try:
            # TODO: 实现自动保存逻辑
            self.auto_save_status.setText("自动保存: 已保存")
            QTimer.singleShot(2000, lambda: self.auto_save_status.setText("自动保存: 开启"))
        except Exception as e:
            print(f"自动保存失败: {e}")
            
    def update_word_count(self, count: int):
        """更新字数统计"""
        self.word_count_status.setText(f"字数: {count:,}")
        
        # 更新统计面板
        char_count = len(self.text_editor.toPlainText())
        chapter_count = self.chapter_tree.topLevelItemCount()
        self.stats_widget.update_stats(count, char_count, chapter_count)
        
    def on_content_changed(self):
        """内容改变处理"""
        # 标记项目为已修改
        pass
        
    def insert_generated_text(self, text: str):
        """插入AI生成的文本"""
        self.text_editor.insert_text(text)
        
    def show_ai_assistant(self):
        """显示AI助手"""
        # 切换到AI助手标签页
        right_panel = self.findChild(QTabWidget)
        if right_panel:
            right_panel.setCurrentWidget(self.ai_assistant)
