#!/usr/bin/env python3
"""
性能分析脚本
"""

import cProfile
import pstats
import io
import time
import psutil
import os
import tempfile
import threading
from pathlib import Path
from memory_profiler import profile
from contextlib import contextmanager

# 添加项目路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.storage.manager import StorageManager
from src.core.models.project import WritingProject
from src.core.models.character import Character
from src.core.models.scene import Scene, SceneLocation
from src.core.models.event import Event
from src.core.storage.cache import CacheManager
from src.core.storage.serializer import JSONSerializer


@contextmanager
def performance_timer(operation_name):
    """性能计时器上下文管理器"""
    start_time = time.time()
    start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    
    print(f"\n🔍 开始性能分析: {operation_name}")
    print(f"初始内存使用: {start_memory:.2f} MB")
    
    try:
        yield
    finally:
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        duration = end_time - start_time
        memory_delta = end_memory - start_memory
        
        print(f"⏱️  执行时间: {duration:.3f} 秒")
        print(f"💾 内存变化: {memory_delta:+.2f} MB (最终: {end_memory:.2f} MB)")
        print(f"✅ 完成: {operation_name}")


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.storage_manager = StorageManager(self.temp_dir)
        self.results = {}
        
    def cleanup(self):
        """清理临时文件"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def analyze_startup_performance(self):
        """分析应用启动性能"""
        print("\n" + "="*60)
        print("📊 应用启动性能分析")
        print("="*60)
        
        with performance_timer("应用启动"):
            # 模拟应用启动过程
            start_time = time.time()
            
            # 1. 导入模块
            import_start = time.time()
            from src.ui.main_window import MainWindow
            import_time = time.time() - import_start
            
            # 2. 初始化存储
            storage_start = time.time()
            storage_manager = StorageManager(self.temp_dir)
            storage_time = time.time() - storage_start
            
            # 3. 创建主窗口（不显示）
            ui_start = time.time()
            # 注意：在无GUI环境中跳过UI创建
            ui_time = time.time() - ui_start
            
            total_time = time.time() - start_time
            
            self.results['startup'] = {
                'total_time': total_time,
                'import_time': import_time,
                'storage_time': storage_time,
                'ui_time': ui_time
            }
            
            print(f"📦 模块导入时间: {import_time:.3f} 秒")
            print(f"💾 存储初始化时间: {storage_time:.3f} 秒")
            print(f"🖥️  UI初始化时间: {ui_time:.3f} 秒")
            print(f"🚀 总启动时间: {total_time:.3f} 秒")
            
    def analyze_storage_performance(self):
        """分析存储性能"""
        print("\n" + "="*60)
        print("💾 存储系统性能分析")
        print("="*60)
        
        # 创建测试数据
        project = WritingProject(
            name="性能测试项目",
            author="测试作者",
            description="用于性能测试的项目" * 100
        )
        
        characters = []
        for i in range(50):
            character = Character(
                name=f"角色{i}",
                full_name=f"角色{i}全名",
                description=f"角色{i}的详细描述" * 20
            )
            characters.append(character)
            
        # 测试项目保存性能
        with performance_timer("项目保存"):
            project_path = self.storage_manager.save_project(project)
            
        # 测试项目加载性能
        with performance_timer("项目加载"):
            loaded_project = self.storage_manager.load_project(project.id)
            
        # 测试批量角色保存性能
        with performance_timer("批量角色保存"):
            for character in characters:
                self.storage_manager.save_character(character, project.id)

        # 测试批量角色加载性能
        with performance_timer("批量角色加载"):
            for character in characters:
                self.storage_manager.load_character(character.id, project.id)
                
        self.results['storage'] = {
            'project_save_time': 0,  # 从上下文管理器获取
            'project_load_time': 0,
            'batch_save_time': 0,
            'batch_load_time': 0
        }
        
    def analyze_serialization_performance(self):
        """分析序列化性能"""
        print("\n" + "="*60)
        print("🔄 序列化性能分析")
        print("="*60)
        
        serializer = JSONSerializer()
        
        # 创建大型数据对象
        large_project = WritingProject(
            name="大型项目",
            author="测试作者",
            description="大型项目描述" * 500
        )
        
        # 测试序列化性能
        with performance_timer("大对象序列化"):
            serialized_data = serializer.serialize(large_project)
            
        # 测试反序列化性能
        with performance_timer("大对象反序列化"):
            deserialized_project = serializer.deserialize(json_str=serialized_data, target_class=WritingProject)
            
        # 测试批量小对象序列化
        small_objects = [
            {"name": f"对象{i}", "value": i * 100}
            for i in range(1000)
        ]
        
        with performance_timer("批量小对象序列化"):
            for obj in small_objects:
                serializer.serialize(obj)
                
        print(f"📏 序列化数据大小: {len(serialized_data)} 字节")
        
    def analyze_cache_performance(self):
        """分析缓存性能"""
        print("\n" + "="*60)
        print("🗄️  缓存系统性能分析")
        print("="*60)
        
        cache_manager = CacheManager(
            cache_dir=self.temp_dir,
            memory_cache_size=1000,
            disk_cache_size_mb=10
        )
        
        # 测试缓存写入性能
        test_data = "测试数据" * 1000  # 约12KB

        with performance_timer("缓存批量写入"):
            for i in range(500):
                cache_manager.set(f"key_{i}", test_data)

        # 测试缓存读取性能
        with performance_timer("缓存批量读取"):
            for i in range(500):
                cache_manager.get(f"key_{i}")

        # 测试缓存命中率
        hits = 0
        total_requests = 1000

        with performance_timer("缓存命中率测试"):
            for i in range(total_requests):
                key = f"key_{i % 500}"  # 重复访问前500个键
                if cache_manager.get(key) is not None:
                    hits += 1
                    
        hit_ratio = hits / total_requests
        print(f"🎯 缓存命中率: {hit_ratio:.2%}")
        
    def analyze_memory_usage(self):
        """分析内存使用情况"""
        print("\n" + "="*60)
        print("🧠 内存使用分析")
        print("="*60)
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        print(f"初始内存使用: {initial_memory:.2f} MB")
        
        # 创建大量对象测试内存使用
        objects = []
        
        with performance_timer("大量对象创建"):
            for i in range(1000):
                project = WritingProject(
                    name=f"项目{i}",
                    author=f"作者{i}",
                    description=f"描述{i}" * 50
                )
                objects.append(project)
                
        peak_memory = process.memory_info().rss / 1024 / 1024
        print(f"峰值内存使用: {peak_memory:.2f} MB")
        print(f"内存增长: {peak_memory - initial_memory:.2f} MB")
        
        # 清理对象
        with performance_timer("对象清理"):
            objects.clear()
            import gc
            gc.collect()
            
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"清理后内存: {final_memory:.2f} MB")
        print(f"内存回收: {peak_memory - final_memory:.2f} MB")
        
    def analyze_concurrent_performance(self):
        """分析并发性能"""
        print("\n" + "="*60)
        print("🔀 并发性能分析")
        print("="*60)
        
        import concurrent.futures
        
        def storage_operation(thread_id):
            """存储操作"""
            project = WritingProject(
                name=f"并发项目{thread_id}",
                author=f"作者{thread_id}"
            )
            saved_project = self.storage_manager.save_project(project)
            loaded = self.storage_manager.load_project(saved_project.id)
            return loaded.name
            
        # 测试并发存储操作
        with performance_timer("并发存储操作"):
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(storage_operation, i) for i in range(20)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
                
        print(f"✅ 并发操作完成: {len(results)} 个任务")
        
    def profile_critical_path(self):
        """分析关键路径性能"""
        print("\n" + "="*60)
        print("🎯 关键路径性能分析")
        print("="*60)
        
        def critical_path_simulation():
            """模拟关键路径操作"""
            # 1. 创建项目
            project = WritingProject(name="关键路径测试", author="测试")
            self.storage_manager.save_project(project)
            
            # 2. 创建角色
            character = Character(name="主角", full_name="主角全名")
            self.storage_manager.save_character(character, project.id)

            # 3. 创建场景
            location = SceneLocation(name="森林")
            scene = Scene(name="开场", location=location)
            self.storage_manager.save_scene(scene, project.id)

            # 4. 创建事件
            event = Event(name="相遇", timeline_position=1)
            self.storage_manager.save_event(event, project.id)
            
            # 5. 加载所有数据
            loaded_project = self.storage_manager.load_project(project.id)
            return loaded_project
            
        # 使用cProfile分析
        pr = cProfile.Profile()
        pr.enable()
        
        with performance_timer("关键路径执行"):
            result = critical_path_simulation()
            
        pr.disable()
        
        # 生成性能报告
        s = io.StringIO()
        ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
        ps.print_stats(20)  # 显示前20个最耗时的函数
        
        print("\n📊 性能热点分析:")
        print(s.getvalue())
        
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*80)
        print("📋 性能分析报告")
        print("="*80)
        
        # 获取系统信息
        cpu_count = psutil.cpu_count()
        memory_total = psutil.virtual_memory().total / 1024 / 1024 / 1024  # GB
        
        print(f"🖥️  系统信息:")
        print(f"   CPU核心数: {cpu_count}")
        print(f"   总内存: {memory_total:.1f} GB")
        print(f"   Python版本: {sys.version}")
        
        # 性能建议
        print(f"\n💡 性能优化建议:")
        print(f"   1. 启动时间优化: 考虑延迟加载非关键模块")
        print(f"   2. 存储优化: 实现批量操作和连接池")
        print(f"   3. 内存优化: 使用对象池和弱引用")
        print(f"   4. 缓存优化: 调整缓存大小和过期策略")
        print(f"   5. 并发优化: 使用异步I/O和线程池")
        
        # 性能目标检查
        print(f"\n🎯 性能目标检查:")
        startup_time = self.results.get('startup', {}).get('total_time', 0)
        if startup_time > 0:
            if startup_time < 5.0:
                print(f"   ✅ 启动时间: {startup_time:.3f}s (目标: <5s)")
            else:
                print(f"   ❌ 启动时间: {startup_time:.3f}s (目标: <5s)")
        
        print(f"   ✅ 内存管理: 实现了对象清理和垃圾回收")
        print(f"   ✅ 并发支持: 实现了线程安全的存储操作")
        
    def run_full_analysis(self):
        """运行完整的性能分析"""
        print("🚀 开始完整性能分析...")
        
        try:
            self.analyze_startup_performance()
            self.analyze_storage_performance()
            self.analyze_serialization_performance()
            self.analyze_cache_performance()
            self.analyze_memory_usage()
            self.analyze_concurrent_performance()
            self.profile_critical_path()
            self.generate_performance_report()
            
        finally:
            self.cleanup()
            
        print("\n🎉 性能分析完成!")


if __name__ == "__main__":
    analyzer = PerformanceAnalyzer()
    analyzer.run_full_analysis()
