"""验证器测试"""

import pytest
from datetime import datetime
from uuid import uuid4

from src.core.validation.validators import (
    ValidationContext, ValidationRule, FieldValidator,
    BusinessRuleValidator, RelationshipValidator, ValidationEngine
)
from src.core.validation.exceptions import ValidationError, ValidationWarning, ValidationInfo
from src.core.models.base import BaseModel
from src.core.models.character import Character


class TestValidationContext:
    """测试验证上下文"""
    
    def test_init_default(self):
        """测试默认初始化"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        assert context.model == character
        assert context.related_models == {}
        assert context.config == {}
        assert context.strict_mode is False
        assert context.issues == []
    
    def test_init_with_params(self):
        """测试带参数初始化"""
        character = Character(full_name="测试角色")
        related_models = {"characters": []}
        config = {"strict": True}
        
        context = ValidationContext(
            character, 
            related_models=related_models,
            config=config,
            strict_mode=True
        )
        
        assert context.model == character
        assert context.related_models == related_models
        assert context.config == config
        assert context.strict_mode is True
    
    def test_add_error(self):
        """测试添加错误"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        context.add_error("测试错误", "test_field", "test_rule")
        
        assert len(context.issues) == 1
        issue = context.issues[0]
        assert isinstance(issue, ValidationError)
        assert issue.message == "测试错误"
        assert issue.field_name == "test_field"
        assert issue.rule_name == "test_rule"
    
    def test_add_warning(self):
        """测试添加警告"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        context.add_warning("测试警告", "test_field", "test_rule")
        
        assert len(context.issues) == 1
        issue = context.issues[0]
        assert isinstance(issue, ValidationWarning)
        assert issue.message == "测试警告"
    
    def test_add_info(self):
        """测试添加信息"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        context.add_info("测试信息", "test_field", "test_rule")
        
        assert len(context.issues) == 1
        issue = context.issues[0]
        assert isinstance(issue, ValidationInfo)
        assert issue.message == "测试信息"
    
    def test_get_related_models(self):
        """测试获取相关模型"""
        character = Character(full_name="测试角色")
        other_chars = [Character(full_name="其他角色")]
        related_models = {"characters": other_chars}
        
        context = ValidationContext(character, related_models=related_models)
        
        assert context.get_related_models("characters") == other_chars
        assert context.get_related_models("scenes") == []
    
    def test_has_errors(self):
        """测试是否有错误"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        assert not context.has_errors()
        
        context.add_warning("警告")
        assert not context.has_errors()
        
        context.add_error("错误")
        assert context.has_errors()
    
    def test_get_validation_result(self):
        """测试获取验证结果"""
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        context.add_error("错误1")
        context.add_error("错误2")
        context.add_warning("警告1")
        
        result = context.get_validation_result()
        
        assert not result.is_valid
        assert len(result.errors) == 2
        assert len(result.warnings) == 1
        assert "错误1" in result.errors[0]
        assert "错误2" in result.errors[1]
        assert "警告1" in result.warnings[0]


class MockValidationRule(ValidationRule):
    """模拟验证规则"""
    
    def __init__(self, name: str, should_pass: bool = True, error_msg: str = "验证失败"):
        super().__init__(name)
        self.should_pass = should_pass
        self.error_msg = error_msg
    
    def validate(self, value, context):
        return self.should_pass
    
    def get_error_message(self, value, context):
        return self.error_msg


class TestFieldValidator:
    """测试字段验证器"""
    
    def test_init(self):
        """测试初始化"""
        validator = FieldValidator()
        assert validator.rules == {}
    
    def test_add_rule(self):
        """测试添加规则"""
        validator = FieldValidator()
        rule = MockValidationRule("test_rule")
        
        validator.add_rule("test_field", rule)
        
        assert "test_field" in validator.rules
        assert len(validator.rules["test_field"]) == 1
        assert validator.rules["test_field"][0] == rule
    
    def test_add_multiple_rules(self):
        """测试添加多个规则"""
        validator = FieldValidator()
        rule1 = MockValidationRule("rule1")
        rule2 = MockValidationRule("rule2")
        
        validator.add_rule("test_field", rule1)
        validator.add_rule("test_field", rule2)
        
        assert len(validator.rules["test_field"]) == 2
    
    def test_validate_field_success(self):
        """测试字段验证成功"""
        validator = FieldValidator()
        rule = MockValidationRule("test_rule", should_pass=True)
        validator.add_rule("test_field", rule)
        
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        result = validator.validate_field("test_field", "test_value", context)
        
        assert result is True
        assert len(context.issues) == 0
    
    def test_validate_field_failure(self):
        """测试字段验证失败"""
        validator = FieldValidator()
        rule = MockValidationRule("test_rule", should_pass=False, error_msg="测试错误")
        validator.add_rule("test_field", rule)
        
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        result = validator.validate_field("test_field", "test_value", context)
        
        assert result is False
        assert len(context.issues) == 1
        assert isinstance(context.issues[0], ValidationError)
        assert context.issues[0].message == "测试错误"
    
    def test_validate_field_no_rules(self):
        """测试没有规则的字段验证"""
        validator = FieldValidator()
        
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        result = validator.validate_field("nonexistent_field", "test_value", context)
        
        assert result is True
        assert len(context.issues) == 0
    
    def test_validate_model(self):
        """测试模型验证"""
        validator = FieldValidator()
        rule = MockValidationRule("name_rule", should_pass=True)
        validator.add_rule("full_name", rule)
        
        character = Character(full_name="测试角色")
        context = ValidationContext(character)
        
        result = validator.validate_model(character, context)
        
        assert result is True


class MockBusinessRuleValidator(BusinessRuleValidator):
    """模拟业务规则验证器"""
    
    def __init__(self, should_pass: bool = True):
        self.should_pass = should_pass
    
    def validate(self, model, context):
        if not self.should_pass:
            context.add_error("业务规则验证失败")
        return self.should_pass
    
    def get_rule_name(self):
        return "mock_business_rule"


class MockRelationshipValidator(RelationshipValidator):
    """模拟关系验证器"""
    
    def __init__(self, should_pass: bool = True):
        self.should_pass = should_pass
    
    def validate_relationships(self, model, context):
        if not self.should_pass:
            context.add_error("关系验证失败")
        return self.should_pass
    
    def get_validator_name(self):
        return "mock_relationship_validator"


class TestValidationEngine:
    """测试验证引擎"""
    
    def test_init(self):
        """测试初始化"""
        engine = ValidationEngine()
        
        assert engine.field_validators == {}
        assert engine.business_rule_validators == {}
        assert engine.relationship_validators == []
    
    def test_register_field_validator(self):
        """测试注册字段验证器"""
        engine = ValidationEngine()
        validator = FieldValidator()
        
        engine.register_field_validator(Character, validator)
        
        assert Character in engine.field_validators
        assert engine.field_validators[Character] == validator
    
    def test_register_business_rule_validator(self):
        """测试注册业务规则验证器"""
        engine = ValidationEngine()
        validator = MockBusinessRuleValidator()
        
        engine.register_business_rule_validator(Character, validator)
        
        assert Character in engine.business_rule_validators
        assert len(engine.business_rule_validators[Character]) == 1
        assert engine.business_rule_validators[Character][0] == validator
    
    def test_register_relationship_validator(self):
        """测试注册关系验证器"""
        engine = ValidationEngine()
        validator = MockRelationshipValidator()
        
        engine.register_relationship_validator(validator)
        
        assert len(engine.relationship_validators) == 1
        assert engine.relationship_validators[0] == validator
    
    def test_validate_success(self):
        """测试验证成功"""
        engine = ValidationEngine()
        character = Character(full_name="测试角色")
        
        result = engine.validate(character)
        
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validate_with_field_validator(self):
        """测试带字段验证器的验证"""
        engine = ValidationEngine()
        field_validator = FieldValidator()
        rule = MockValidationRule("test_rule", should_pass=False, error_msg="字段验证失败")
        field_validator.add_rule("full_name", rule)
        
        engine.register_field_validator(Character, field_validator)
        
        character = Character(full_name="测试角色")
        result = engine.validate(character)
        
        assert not result.is_valid
        assert len(result.errors) == 1
        assert "字段验证失败" in result.errors[0]
    
    def test_validate_with_business_rule_validator(self):
        """测试带业务规则验证器的验证"""
        engine = ValidationEngine()
        business_validator = MockBusinessRuleValidator(should_pass=False)
        
        engine.register_business_rule_validator(Character, business_validator)
        
        character = Character(full_name="测试角色")
        result = engine.validate(character)
        
        assert not result.is_valid
        assert len(result.errors) == 1
        assert "业务规则验证失败" in result.errors[0]
    
    def test_validate_with_relationship_validator(self):
        """测试带关系验证器的验证"""
        engine = ValidationEngine()
        relationship_validator = MockRelationshipValidator(should_pass=False)
        
        engine.register_relationship_validator(relationship_validator)
        
        character = Character(full_name="测试角色")
        result = engine.validate(character)
        
        assert not result.is_valid
        assert len(result.errors) == 1
        assert "关系验证失败" in result.errors[0]
