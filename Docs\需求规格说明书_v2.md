# 笔落App需求规格说明书 v2.0

**版本**: 2.0  
**创建日期**: 2025-09-12  
**修订说明**: 基于项目分析报告的全面改进版本  
**状态**: 修订版

## 版本修订说明

### v2.0 主要改进
1. **补充核心功能**: 新增搜索查找、版本控制、导入功能、多项目管理
2. **重新定义模糊概念**: "圣经系统" → "创作模板系统"，概念更清晰
3. **完善非功能需求**: 详细定义性能、安全、可用性、兼容性要求
4. **增强用户体验**: 补充快捷键、拖拽操作、批量处理等交互功能
5. **明确技术约束**: 确定存储策略、AI集成方案、扩展性要求

---

## 1. 项目概述

### 1.1 产品愿景
笔落App是一个专业的AI辅助小说创作平台，旨在通过智能技术降低创作门槛，让作者专注于创意构思，同时确保作品的专业性和完整性。

### 1.2 核心价值主张
- **结构化创作**: 提供三维度故事管理（大纲→主线→章节）
- **AI智能辅助**: 多模型支持，智能内容生成和优化
- **专业工具集**: 角色、场景、事件全方位管理
- **高效协作**: 人机协作，保持作者创作主导权

### 1.3 目标用户
- **新手作者**: 需要引导和模板支持的初学者
- **经验作者**: 需要高效工具和AI辅助的有经验创作者
- **专业作家**: 需要高级功能和定制化支持的专业人士

---

## 2. 功能需求

### 2.1 项目管理功能

#### 2.1.1 项目生命周期管理
- **FR-001**: 创建新项目，支持项目名称、作者、类型、简介等元数据设置
- **FR-002**: 打开现有项目，显示项目列表和基本信息预览
- **FR-003**: 项目保存和自动保存，支持实时保存和定时备份
- **FR-004**: 项目删除和恢复，支持软删除和回收站机制
- **FR-005**: 最近项目列表，快速访问最近使用的项目

#### 2.1.2 多项目管理 ⭐新增
- **FR-006**: 同时管理多个项目，支持项目间快速切换
- **FR-007**: 项目分组和标签管理，便于组织大量项目
- **FR-008**: 项目搜索和过滤，支持按名称、类型、时间等条件查找
- **FR-009**: 项目统计信息，显示字数、进度、创建时间等

#### 2.1.3 导入导出功能
- **FR-010**: 导出项目为多种格式（TXT、PDF、ePub、Word）
- **FR-011**: 导入功能 ⭐新增，支持从Word、TXT、其他写作软件导入
- **FR-012**: 批量导出，支持选择性导出章节或整个项目
- **FR-013**: 导出模板定制，支持自定义导出格式和样式

### 2.2 故事结构管理

#### 2.2.1 三维度结构系统
- **FR-014**: 大纲维度管理，整体故事结构（开端、发展、高潮、结局）
- **FR-015**: 主线维度管理，分卷和主要情节线管理
- **FR-016**: 章节维度管理，具体章节内容和事件管理
- **FR-017**: 维度间联动，修改一个维度自动同步相关维度
- **FR-018**: 可视化进度展示，图形化显示故事发展进度

#### 2.2.2 结构化编辑
- **FR-019**: 拖拽重排，支持章节、事件的拖拽排序
- **FR-020**: 批量操作，支持批量修改、删除、移动
- **FR-021**: 结构模板，提供不同类型小说的结构模板
- **FR-022**: 结构验证，检查故事结构的完整性和逻辑性

### 2.3 内容创作与编辑

#### 2.3.1 富文本编辑器
- **FR-023**: 富文本编辑，支持基本格式（粗体、斜体、标题等）
- **FR-024**: 实时保存，自动保存用户输入内容
- **FR-025**: 快捷键支持 ⭐新增，提供常用编辑快捷键
- **FR-026**: 字数统计，实时显示字数、段落数等统计信息
- **FR-027**: 写作模式，提供专注模式、全屏模式等

#### 2.3.2 版本控制 ⭐新增
- **FR-028**: 自动版本记录，系统自动记录所有修改历史
- **FR-029**: 版本比较，支持任意两个版本的差异对比
- **FR-030**: 版本回滚，支持回滚到任意历史版本
- **FR-031**: 版本分支，支持创建分支尝试不同创作方向
- **FR-032**: 版本标签，支持为重要版本添加标签和备注

### 2.4 搜索和查找功能 ⭐新增

#### 2.4.1 全文搜索
- **FR-033**: 全文搜索，在整个项目中搜索文本内容
- **FR-034**: 高级搜索，支持正则表达式、大小写敏感等选项
- **FR-035**: 搜索结果高亮，在搜索结果中高亮显示匹配内容
- **FR-036**: 搜索历史，保存和管理搜索历史记录

#### 2.4.2 元素搜索
- **FR-037**: 角色搜索，按角色名称、属性等搜索相关内容
- **FR-038**: 场景搜索，按场景名称、地点等搜索相关内容
- **FR-039**: 事件搜索，按事件类型、时间等搜索相关内容
- **FR-040**: 标签搜索，按用户自定义标签搜索内容

### 2.5 故事元素管理

#### 2.5.1 角色管理
- **FR-041**: 角色档案管理，创建和编辑角色基本信息
- **FR-042**: 角色关系网络，管理角色间的复杂关系
- **FR-043**: 角色发展轨迹，跟踪角色在故事中的成长变化
- **FR-044**: 角色出场统计，统计角色在各章节的出场情况
- **FR-045**: 角色一致性检查，检查角色描述的一致性

#### 2.5.2 场景管理
- **FR-046**: 场景库管理，创建和管理故事场景
- **FR-047**: 场景复用，支持场景在多个章节中重复使用
- **FR-048**: 场景氛围设定，定义场景的情感基调和氛围
- **FR-049**: 场景关联，建立场景与角色、事件的关联关系

#### 2.5.3 事件管理
- **FR-050**: 事件时间线，按时间顺序管理故事事件
- **FR-051**: 事件类型分类，支持转折、冲突、解决等事件类型
- **FR-052**: 事件影响分析，分析事件对情节和角色的影响
- **FR-053**: 事件关联网络，建立事件间的因果关系

### 2.6 AI辅助创作

#### 2.6.1 多模型支持
- **FR-054**: 多AI服务商集成（OpenAI、DeepSeek、智谱、文心一言等）
- **FR-055**: 模型切换，支持在不同AI模型间快速切换
- **FR-056**: 模型配置，支持自定义模型参数和设置
- **FR-057**: 使用统计，跟踪AI使用情况和成本

#### 2.6.2 智能内容生成
- **FR-058**: 上下文感知生成，基于故事上下文生成相关内容
- **FR-059**: 多种生成模式（续写、改写、扩写、灵感激发）
- **FR-060**: 角色对话生成，基于角色性格生成符合特征的对话
- **FR-061**: 场景描述生成，基于场景设定生成环境描述
- **FR-062**: 情节发展建议，AI提供情节发展的建议和选项

#### 2.6.3 内容优化
- **FR-063**: 风格统一优化，统一全文的写作风格
- **FR-064**: 去AI化处理，使AI生成内容更自然人性化
- **FR-065**: 逻辑连贯性检查，检查内容的逻辑一致性
- **FR-066**: 语法检查，自动检查和纠正语法错误
- **FR-067**: 可读性优化，提供可读性改进建议

### 2.7 创作模板系统（原"圣经系统"）

#### 2.7.1 模板库管理
- **FR-068**: 内置模板库，提供不同类型小说的创作模板
- **FR-069**: 自定义模板，用户可创建和保存个人模板
- **FR-070**: 模板分享，支持模板的导入导出和分享
- **FR-071**: 模板应用，一键应用模板到新项目

#### 2.7.2 知识库系统
- **FR-072**: 角色原型库，提供常见角色原型和设定
- **FR-073**: 情节模板库，提供经典情节结构和发展模式
- **FR-074**: 场景模板库，提供各种场景的描述模板
- **FR-075**: 写作技巧库，提供写作技巧和指导建议

### 2.8 设置与配置

#### 2.8.1 通用设置
- **FR-076**: 界面主题，支持多种界面主题和色彩方案
- **FR-077**: 字体设置，自定义字体类型和大小
- **FR-078**: 布局配置，自定义界面布局和组件位置
- **FR-079**: 快捷键定制，支持自定义快捷键绑定

#### 2.8.2 AI模型配置
- **FR-080**: API密钥管理，安全存储和管理各服务商API密钥
- **FR-081**: 模型参数设置，配置温度、最大长度等参数
- **FR-082**: 使用限制设置，设置使用配额和成本控制
- **FR-083**: 连接测试，测试AI服务连接状态

#### 2.8.3 数据管理
- **FR-084**: 备份设置，配置自动备份策略和位置
- **FR-085**: 数据导入导出，管理用户数据的导入导出
- **FR-086**: 缓存管理，管理应用缓存和临时文件
- **FR-087**: 数据同步，支持云端数据同步（可选功能）

---

## 3. 非功能需求

### 3.1 性能需求
- **NFR-001**: 应用启动时间 ≤ 3秒
- **NFR-002**: 界面操作响应时间 ≤ 100毫秒
- **NFR-003**: 文件加载时间（10MB项目）≤ 2秒
- **NFR-004**: AI生成响应时间 ≤ 30秒（依赖网络）
- **NFR-005**: 支持同时处理5个以上项目
- **NFR-006**: 内存使用 ≤ 1GB（正常使用情况下）
- **NFR-007**: 支持单个项目最大100MB数据量

### 3.2 可靠性需求
- **NFR-008**: 系统可用性 ≥ 99.5%（年度停机时间 ≤ 43.8小时）
- **NFR-009**: 数据丢失率 ≤ 0.1%（通过自动备份保障）
- **NFR-010**: 自动故障恢复能力，异常退出后自动恢复数据
- **NFR-011**: 完整的数据备份和恢复机制
- **NFR-012**: 详细的操作日志和错误日志记录

### 3.3 安全性需求
- **NFR-013**: 用户数据本地加密存储
- **NFR-014**: API密钥等敏感信息安全存储
- **NFR-015**: 防止常见安全漏洞（XSS、CSRF、注入攻击等）
- **NFR-016**: 内容安全过滤机制
- **NFR-017**: 定期安全更新和漏洞修复

### 3.4 可用性需求
- **NFR-018**: 界面直观易用，新用户30分钟内掌握基本功能
- **NFR-019**: 完整的帮助文档和教程
- **NFR-020**: 键盘快捷键和自定义快捷方式支持
- **NFR-021**: 错误信息清晰明确，提供解决建议
- **NFR-022**: 支持多语言界面（中英文优先）

### 3.5 兼容性需求
- **NFR-023**: 支持Windows 10+和macOS 10.15+操作系统
- **NFR-024**: 支持主流浏览器内核（Chromium、WebKit）
- **NFR-025**: 文件格式向前兼容至少2个主要版本
- **NFR-026**: 支持与常见写作工具的文件交换

### 3.6 可维护性需求
- **NFR-027**: 良好的模块化结构设计
- **NFR-028**: 完整的API文档和开发文档
- **NFR-029**: 支持热更新和模块化升级
- **NFR-030**: 代码测试覆盖率 ≥ 90%
- **NFR-031**: 性能监控和诊断工具

---

## 4. 用户界面需求

### 4.1 主界面
- 背景图片体现"笔落"寓意，营造文学创作氛围
- 新建项目按钮，支持快速创建和模板选择
- 打开项目按钮，显示项目列表和预览
- 最近项目列表，快速访问最近使用的项目
- 设置按钮，进入应用配置界面

### 4.2 创作界面
- **中央编辑区**: 主要内容编辑区域，支持富文本编辑
- **左侧结构面板**: 三维度切换（大纲→主线→章节）
- **右上角色面板**: 显示当前相关角色信息
- **右下功能面板**: AI生成、格式化、保存等功能
- **底部AI交互区**: AI对话和生成控制面板
- **顶部工具栏**: 常用功能快速访问
- **底部状态栏**: 显示项目信息、字数统计、保存状态

### 4.3 设置界面
- **左侧导航**: 设置分类导航（通用、AI、模板、数据）
- **右侧配置区**: 具体设置选项和配置界面
- **底部操作区**: 保存、重置、取消等操作按钮

---

## 5. 技术约束

### 5.1 开发平台
- **开发语言**: Python 3.11+
- **GUI框架**: PyQt6
- **目标平台**: Windows 10/11 桌面应用
- **数据存储**: 本地JSON文件 + 二进制备份

### 5.2 第三方依赖
- **AI服务**: OpenAI、DeepSeek、智谱AI等API
- **文件处理**: python-docx、PyPDF2等
- **网络请求**: requests、aiohttp
- **测试框架**: pytest、pytest-qt

### 5.3 性能约束
- **启动时间**: ≤ 3秒
- **内存使用**: ≤ 1GB
- **文件大小**: 支持最大100MB项目
- **并发项目**: 支持5个以上项目同时打开

---

## 6. 验收标准

### 6.1 功能验收
- 所有功能需求（FR-001 至 FR-087）100%实现
- 核心用户场景端到端测试通过
- AI集成功能稳定可用

### 6.2 性能验收
- 所有性能需求（NFR-001 至 NFR-007）达标
- 压力测试通过（大型项目、长时间使用）
- 内存泄漏测试通过

### 6.3 质量验收
- 代码测试覆盖率 ≥ 90%
- 安全测试通过
- 用户体验测试通过

---

## 7. 项目里程碑

### 7.1 第一阶段（4周）：核心功能
- 项目管理和文件存储
- 基础编辑功能
- 简单AI集成

### 7.2 第二阶段（4周）：高级功能
- 三维度结构管理
- 完整AI功能
- 元素管理系统

### 7.3 第三阶段（4周）：完善优化
- UI完善和优化
- 性能优化
- 测试和发布准备

**总开发周期**: 12周

---

*本文档版本: v2.0*  
*最后更新: 2025-09-12*
