#!/usr/bin/env python3
"""笔落App开发环境设置脚本

此脚本用于自动化设置开发环境，包括：
1. 创建Python虚拟环境
2. 安装依赖包
3. 配置pre-commit钩子
4. 验证环境配置
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """运行命令并返回结果"""
    print(f"执行命令: {command}")
    result = subprocess.run(
        command, 
        shell=True, 
        capture_output=True, 
        text=True,
        check=False
    )
    
    if result.returncode != 0 and check:
        print(f"命令执行失败: {command}")
        print(f"错误输出: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major != 3 or version.minor < 11:
        print(f"错误: 需要Python 3.11+，当前版本: {version.major}.{version.minor}")
        sys.exit(1)
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")


def create_virtual_environment():
    """创建虚拟环境"""
    print("创建Python虚拟环境...")
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("虚拟环境已存在，跳过创建")
        return
    
    run_command(f"{sys.executable} -m venv .venv")
    print("虚拟环境创建成功")


def get_pip_command():
    """获取pip命令路径"""
    system = platform.system()
    if system == "Windows":
        return ".venv\\Scripts\\pip.exe"
    else:
        return ".venv/bin/pip"


def get_python_command():
    """获取Python命令路径"""
    system = platform.system()
    if system == "Windows":
        return ".venv\\Scripts\\python.exe"
    else:
        return ".venv/bin/python"


def install_dependencies():
    """安装依赖包"""
    print("安装项目依赖...")
    pip_cmd = get_pip_command()
    
    # 升级pip
    run_command(f"{pip_cmd} install --upgrade pip")
    
    # 安装主要依赖
    run_command(f"{pip_cmd} install -r requirements.txt")
    
    # 安装开发依赖
    run_command(f"{pip_cmd} install -e .[dev]")
    
    print("依赖安装完成")


def setup_pre_commit():
    """设置pre-commit钩子"""
    print("设置pre-commit钩子...")
    python_cmd = get_python_command()
    
    # 安装pre-commit
    run_command(f"{python_cmd} -m pip install pre-commit")
    
    # 安装钩子
    run_command(f"{python_cmd} -m pre_commit install")
    
    print("pre-commit钩子设置完成")


def verify_installation():
    """验证安装"""
    print("验证环境配置...")
    python_cmd = get_python_command()
    
    # 检查主要包
    packages_to_check = [
        "PyQt6",
        "openai", 
        "pydantic",
        "pytest",
        "black",
        "flake8",
        "mypy"
    ]
    
    for package in packages_to_check:
        result = run_command(f"{python_cmd} -c 'import {package}; print(f\"{package}: OK\")'", check=False)
        if result.returncode != 0:
            print(f"警告: {package} 导入失败")
        else:
            print(result.stdout.strip())
    
    print("环境验证完成")


def create_directories():
    """创建必要的目录"""
    print("创建必要的目录...")
    directories = [
        "data",
        "cache", 
        "backups",
        "logs",
        "temp",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"创建目录: {directory}")


def copy_env_file():
    """复制环境配置文件"""
    print("设置环境配置文件...")
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        env_file.write_text(env_example.read_text(encoding="utf-8"), encoding="utf-8")
        print("已创建 .env 文件，请根据需要修改配置")
    else:
        print(".env 文件已存在或 .env.example 不存在")


def main():
    """主函数"""
    print("=" * 60)
    print("笔落App开发环境设置")
    print("=" * 60)
    
    try:
        # 检查Python版本
        check_python_version()
        
        # 创建虚拟环境
        create_virtual_environment()
        
        # 安装依赖
        install_dependencies()
        
        # 设置pre-commit
        setup_pre_commit()
        
        # 创建目录
        create_directories()
        
        # 复制环境文件
        copy_env_file()
        
        # 验证安装
        verify_installation()
        
        print("\n" + "=" * 60)
        print("开发环境设置完成！")
        print("=" * 60)
        print("\n下一步操作:")
        print("1. 激活虚拟环境:")
        if platform.system() == "Windows":
            print("   .venv\\Scripts\\activate")
        else:
            print("   source .venv/bin/activate")
        print("2. 编辑 .env 文件配置API密钥")
        print("3. 运行测试: pytest tests/")
        print("4. 启动应用: python src/main.py")
        
    except KeyboardInterrupt:
        print("\n用户中断，退出设置")
        sys.exit(1)
    except Exception as e:
        print(f"\n设置过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
