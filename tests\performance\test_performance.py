"""
性能测试模块
"""

import pytest
import time
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from src.core.storage.manager import StorageManager
from src.core.storage.cache import <PERSON><PERSON><PERSON>ache, <PERSON>sk<PERSON>ache, CacheManager
from src.core.storage.serializer import JSONSerializer
from src.core.models.project import WritingProject
from src.core.models.character import Character
from src.core.models.scene import Scene
from src.core.models.event import Event


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def storage_manager(temp_dir):
    """创建存储管理器"""
    return StorageManager(temp_dir)


@pytest.fixture
def large_project():
    """创建大型项目数据"""
    project = WritingProject(
        name="大型测试项目",
        author="性能测试作者",
        description="用于性能测试的大型项目" * 100  # 增加描述长度
    )
    return project


@pytest.fixture
def many_characters():
    """创建大量角色数据"""
    characters = []
    for i in range(100):
        character = Character(
            name=f"角色{i}",
            full_name=f"角色{i}全名",
            description=f"这是角色{i}的详细描述" * 10
        )
        characters.append(character)
    return characters


class TestStoragePerformance:
    """存储性能测试"""
    
    def test_project_save_performance(self, storage_manager, large_project):
        """测试项目保存性能"""
        start_time = time.time()
        
        # 保存项目
        project_path = storage_manager.save_project(large_project)
        
        end_time = time.time()
        save_time = end_time - start_time
        
        # 验证保存时间在合理范围内（< 1秒）
        assert save_time < 1.0, f"项目保存时间过长: {save_time:.3f}秒"
        assert Path(project_path).exists()
        
    def test_project_load_performance(self, storage_manager, large_project):
        """测试项目加载性能"""
        # 先保存项目
        project_path = storage_manager.save_project(large_project)
        
        start_time = time.time()
        
        # 加载项目
        loaded_project = storage_manager.load_project(project_path)
        
        end_time = time.time()
        load_time = end_time - start_time
        
        # 验证加载时间在合理范围内（< 0.5秒）
        assert load_time < 0.5, f"项目加载时间过长: {load_time:.3f}秒"
        assert loaded_project.name == large_project.name
        
    def test_batch_character_save_performance(self, storage_manager, large_project, many_characters):
        """测试批量角色保存性能"""
        # 先保存项目
        storage_manager.save_project(large_project)
        
        start_time = time.time()
        
        # 批量保存角色
        for character in many_characters:
            storage_manager.save_character(large_project.id, character)
            
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / len(many_characters)
        
        # 验证平均保存时间在合理范围内（< 0.01秒/个）
        assert avg_time < 0.01, f"角色平均保存时间过长: {avg_time:.4f}秒/个"
        assert total_time < 5.0, f"批量保存总时间过长: {total_time:.3f}秒"
        
    def test_concurrent_save_performance(self, storage_manager, large_project, many_characters):
        """测试并发保存性能"""
        # 先保存项目
        storage_manager.save_project(large_project)
        
        start_time = time.time()
        
        # 并发保存角色
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for character in many_characters[:20]:  # 限制数量避免过度测试
                future = executor.submit(
                    storage_manager.save_character, 
                    large_project.id, 
                    character
                )
                futures.append(future)
                
            # 等待所有任务完成
            for future in as_completed(futures):
                future.result()
                
        end_time = time.time()
        total_time = end_time - start_time
        
        # 并发保存应该比串行保存快
        assert total_time < 2.0, f"并发保存时间过长: {total_time:.3f}秒"


class TestCachePerformance:
    """缓存性能测试"""
    
    def test_lru_cache_performance(self):
        """测试LRU缓存性能"""
        cache = LRUCache(capacity=1000)
        
        # 测试写入性能
        start_time = time.time()
        for i in range(1000):
            cache.put(f"key_{i}", f"value_{i}")
        write_time = time.time() - start_time
        
        # 测试读取性能
        start_time = time.time()
        for i in range(1000):
            cache.get(f"key_{i}")
        read_time = time.time() - start_time
        
        # 验证性能指标
        assert write_time < 0.1, f"LRU缓存写入时间过长: {write_time:.3f}秒"
        assert read_time < 0.05, f"LRU缓存读取时间过长: {read_time:.3f}秒"
        
    def test_disk_cache_performance(self, temp_dir):
        """测试磁盘缓存性能"""
        cache = DiskCache(temp_dir, max_size=10*1024*1024)  # 10MB
        
        # 测试写入性能
        test_data = "测试数据" * 1000  # 约8KB数据
        start_time = time.time()
        for i in range(100):
            cache.put(f"key_{i}", test_data)
        write_time = time.time() - start_time
        
        # 测试读取性能
        start_time = time.time()
        for i in range(100):
            cache.get(f"key_{i}")
        read_time = time.time() - start_time
        
        # 验证性能指标
        assert write_time < 1.0, f"磁盘缓存写入时间过长: {write_time:.3f}秒"
        assert read_time < 0.5, f"磁盘缓存读取时间过长: {read_time:.3f}秒"
        
    def test_cache_hit_ratio_performance(self, temp_dir):
        """测试缓存命中率性能"""
        cache_manager = CacheManager(
            memory_cache_size=100,
            disk_cache_dir=temp_dir,
            disk_cache_size=1024*1024
        )
        
        # 填充缓存
        for i in range(50):
            cache_manager.put(f"key_{i}", f"value_{i}")
            
        # 测试命中率
        hits = 0
        total_requests = 100
        
        start_time = time.time()
        for i in range(total_requests):
            key = f"key_{i % 50}"  # 重复访问前50个键
            if cache_manager.get(key) is not None:
                hits += 1
        access_time = time.time() - start_time
        
        hit_ratio = hits / total_requests
        
        # 验证命中率和性能
        assert hit_ratio > 0.8, f"缓存命中率过低: {hit_ratio:.2f}"
        assert access_time < 0.1, f"缓存访问时间过长: {access_time:.3f}秒"


class TestSerializationPerformance:
    """序列化性能测试"""
    
    def test_json_serialization_performance(self, large_project, many_characters):
        """测试JSON序列化性能"""
        serializer = JSONSerializer()
        
        # 测试项目序列化性能
        start_time = time.time()
        project_json = serializer.serialize(large_project)
        project_serialize_time = time.time() - start_time
        
        # 测试项目反序列化性能
        start_time = time.time()
        deserialized_project = serializer.deserialize(project_json, WritingProject)
        project_deserialize_time = time.time() - start_time
        
        # 测试批量角色序列化性能
        start_time = time.time()
        for character in many_characters[:20]:  # 限制数量
            serializer.serialize(character)
        batch_serialize_time = time.time() - start_time
        
        # 验证性能指标
        assert project_serialize_time < 0.1, f"项目序列化时间过长: {project_serialize_time:.3f}秒"
        assert project_deserialize_time < 0.1, f"项目反序列化时间过长: {project_deserialize_time:.3f}秒"
        assert batch_serialize_time < 1.0, f"批量序列化时间过长: {batch_serialize_time:.3f}秒"
        
        # 验证数据完整性
        assert deserialized_project.name == large_project.name
        
    def test_large_data_serialization(self):
        """测试大数据序列化性能"""
        serializer = JSONSerializer()
        
        # 创建大量数据
        large_data = {
            "characters": [
                {
                    "name": f"角色{i}",
                    "description": "详细描述" * 100
                }
                for i in range(1000)
            ],
            "scenes": [
                {
                    "name": f"场景{i}",
                    "description": "场景描述" * 50
                }
                for i in range(500)
            ]
        }
        
        start_time = time.time()
        serialized_data = serializer.serialize(large_data)
        serialize_time = time.time() - start_time
        
        start_time = time.time()
        deserialized_data = serializer.deserialize(serialized_data, dict)
        deserialize_time = time.time() - start_time
        
        # 验证性能指标
        assert serialize_time < 2.0, f"大数据序列化时间过长: {serialize_time:.3f}秒"
        assert deserialize_time < 2.0, f"大数据反序列化时间过长: {deserialize_time:.3f}秒"
        
        # 验证数据完整性
        assert len(deserialized_data["characters"]) == 1000
        assert len(deserialized_data["scenes"]) == 500


class TestMemoryUsage:
    """内存使用测试"""
    
    def test_memory_usage_during_batch_operations(self, storage_manager, many_characters):
        """测试批量操作时的内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建项目
        project = WritingProject(name="内存测试项目", author="测试")
        storage_manager.save_project(project)
        
        # 批量保存角色
        for character in many_characters:
            storage_manager.save_character(project.id, character)
            
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 验证内存增长在合理范围内（< 100MB）
        assert memory_increase < 100, f"内存增长过多: {memory_increase:.2f}MB"
        
    def test_cache_memory_management(self, temp_dir):
        """测试缓存内存管理"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大容量缓存
        cache = LRUCache(capacity=10000)
        
        # 填充大量数据
        large_value = "大数据" * 1000  # 约12KB
        for i in range(5000):
            cache.put(f"key_{i}", large_value)
            
        filled_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理缓存
        cache.clear()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 验证内存管理
        memory_increase = filled_memory - initial_memory
        memory_after_clear = final_memory - initial_memory
        
        assert memory_increase > 10, "缓存应该占用一定内存"
        assert memory_after_clear < memory_increase * 0.5, "清理后内存应该显著减少"


class TestConcurrencyPerformance:
    """并发性能测试"""
    
    def test_concurrent_read_performance(self, storage_manager, large_project):
        """测试并发读取性能"""
        # 先保存项目
        project_path = storage_manager.save_project(large_project)
        
        def load_project():
            return storage_manager.load_project(project_path)
            
        start_time = time.time()
        
        # 并发读取
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(load_project) for _ in range(20)]
            results = [future.result() for future in as_completed(futures)]
            
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证并发读取性能
        assert total_time < 2.0, f"并发读取时间过长: {total_time:.3f}秒"
        assert len(results) == 20
        assert all(r.name == large_project.name for r in results)
        
    def test_concurrent_cache_access(self, temp_dir):
        """测试并发缓存访问性能"""
        cache = CacheManager(
            memory_cache_size=1000,
            disk_cache_dir=temp_dir,
            disk_cache_size=10*1024*1024
        )
        
        def cache_operations(thread_id):
            # 每个线程执行读写操作
            for i in range(100):
                key = f"thread_{thread_id}_key_{i}"
                value = f"thread_{thread_id}_value_{i}"
                cache.put(key, value)
                retrieved = cache.get(key)
                assert retrieved == value
                
        start_time = time.time()
        
        # 并发缓存操作
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(cache_operations, i) for i in range(4)]
            for future in as_completed(futures):
                future.result()
                
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证并发缓存性能
        assert total_time < 3.0, f"并发缓存操作时间过长: {total_time:.3f}秒"


@pytest.mark.benchmark
class TestBenchmarks:
    """基准测试"""
    
    def test_storage_benchmark(self, storage_manager, benchmark):
        """存储操作基准测试"""
        project = WritingProject(name="基准测试项目", author="测试")
        
        def save_and_load():
            path = storage_manager.save_project(project)
            return storage_manager.load_project(path)
            
        result = benchmark(save_and_load)
        assert result.name == project.name
        
    def test_serialization_benchmark(self, benchmark):
        """序列化基准测试"""
        serializer = JSONSerializer()
        project = WritingProject(
            name="基准测试项目",
            author="测试",
            description="基准测试描述" * 100
        )
        
        def serialize_deserialize():
            json_data = serializer.serialize(project)
            return serializer.deserialize(json_data, WritingProject)
            
        result = benchmark(serialize_deserialize)
        assert result.name == project.name
