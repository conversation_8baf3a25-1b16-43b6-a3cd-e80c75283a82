#!/usr/bin/env python3
"""
应用程序打包脚本
使用PyInstaller创建独立可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import tempfile
import zipfile
from datetime import datetime

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BUILD_DIR = PROJECT_ROOT / "build"
DIST_DIR = PROJECT_ROOT / "dist"
SPEC_FILE = PROJECT_ROOT / "bamboofall.spec"

# 应用信息
APP_NAME = "笔落"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "AI辅助小说创作工具"
APP_AUTHOR = "BambooFall Team"
APP_COPYRIGHT = f"Copyright © 2025 {APP_AUTHOR}"

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    for dir_path in [BUILD_DIR, DIST_DIR]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"   删除: {dir_path}")
    
    if SPEC_FILE.exists():
        SPEC_FILE.unlink()
        print(f"   删除: {SPEC_FILE}")

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("📝 创建PyInstaller规格文件...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
from pathlib import Path

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[r'{PROJECT_ROOT}'],
    binaries=[],
    datas=[
        ('src/ui/*.py', 'ui'),
        ('src/core', 'core'),
        ('resources', 'resources'),
        ('Docs', 'Docs'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'pydantic',
        'uuid',
        'datetime',
        'json',
        'pathlib',
        'typing',
        'enum',
        'dataclasses',
        'concurrent.futures',
        'threading',
        'queue',
        'tempfile',
        'shutil',
        'zipfile',
        'hashlib',
        'time',
        'psutil',
        'requests',
        'anthropic',
        'openai',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{APP_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icon.ico' if Path('resources/icon.ico').exists() else None,
)
'''
    
    with open(SPEC_FILE, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"   创建: {SPEC_FILE}")

def create_version_info():
    """创建版本信息文件"""
    print("📋 创建版本信息文件...")
    
    version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'{APP_AUTHOR}'),
        StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{APP_NAME}'),
        StringStruct(u'LegalCopyright', u'{APP_COPYRIGHT}'),
        StringStruct(u'OriginalFilename', u'{APP_NAME}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    version_file = PROJECT_ROOT / "version_info.txt"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print(f"   创建: {version_file}")

def create_icon():
    """创建应用图标（如果不存在）"""
    resources_dir = PROJECT_ROOT / "resources"
    icon_file = resources_dir / "icon.ico"
    
    if not icon_file.exists():
        print("🎨 创建默认应用图标...")
        resources_dir.mkdir(exist_ok=True)
        
        # 这里可以添加创建默认图标的代码
        # 暂时跳过，使用系统默认图标
        print("   使用系统默认图标")

def run_pyinstaller():
    """运行PyInstaller"""
    print("🔨 开始打包应用程序...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "--noconfirm", 
        str(SPEC_FILE)
    ]
    
    print(f"   执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        if result.returncode == 0:
            print("   ✅ 打包成功!")
            return True
        else:
            print("   ❌ 打包失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ 打包超时!")
        return False
    except Exception as e:
        print(f"   ❌ 打包异常: {e}")
        return False

def create_installer():
    """创建安装包"""
    print("📦 创建安装包...")
    
    exe_path = DIST_DIR / f"{APP_NAME}.exe"
    if not exe_path.exists():
        print("   ❌ 可执行文件不存在，无法创建安装包")
        return False
    
    # 创建ZIP安装包
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    installer_name = f"{APP_NAME}_v{APP_VERSION}_{timestamp}.zip"
    installer_path = DIST_DIR / installer_name
    
    with zipfile.ZipFile(installer_path, 'w', zipfile.ZIP_DEFLATED) as zf:
        # 添加可执行文件
        zf.write(exe_path, f"{APP_NAME}.exe")
        
        # 添加文档
        docs_dir = PROJECT_ROOT / "Docs"
        if docs_dir.exists():
            for doc_file in docs_dir.glob("*.md"):
                zf.write(doc_file, f"docs/{doc_file.name}")
        
        # 添加许可证文件（如果存在）
        license_file = PROJECT_ROOT / "LICENSE"
        if license_file.exists():
            zf.write(license_file, "LICENSE.txt")
        
        # 添加README
        readme_file = PROJECT_ROOT / "README.md"
        if readme_file.exists():
            zf.write(readme_file, "README.txt")
    
    print(f"   ✅ 安装包创建成功: {installer_path}")
    return True

def get_build_info():
    """获取构建信息"""
    print("📊 构建信息:")
    
    exe_path = DIST_DIR / f"{APP_NAME}.exe"
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"   可执行文件大小: {size_mb:.1f} MB")
        print(f"   文件路径: {exe_path}")
    
    # 统计文件数量
    if DIST_DIR.exists():
        file_count = len(list(DIST_DIR.rglob("*")))
        print(f"   输出文件数量: {file_count}")

def main():
    """主函数"""
    print("🚀 开始构建笔落应用程序")
    print("=" * 50)
    
    try:
        # 1. 清理构建目录
        clean_build_dirs()
        
        # 2. 创建必要文件
        create_version_info()
        create_icon()
        create_spec_file()
        
        # 3. 运行PyInstaller
        if not run_pyinstaller():
            print("❌ 构建失败!")
            return 1
        
        # 4. 创建安装包
        create_installer()
        
        # 5. 显示构建信息
        get_build_info()
        
        print("=" * 50)
        print("🎉 构建完成!")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
