"""基础模型类

定义所有数据模型的基础类和验证机制，包括：
- BaseModel: 所有模型的基类
- ValidationResult: 验证结果类
- ModelValidator: 模型验证器基类
"""

import json
import hashlib
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from uuid import UUID, uuid4

from pydantic import BaseModel as PydanticBaseModel, Field, validator
from pydantic.json import pydantic_encoder


T = TypeVar('T', bound='BaseModel')


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def add_error(self, message: str):
        """添加错误"""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        """添加警告"""
        self.warnings.append(message)
    
    def merge(self, other: 'ValidationResult'):
        """合并验证结果"""
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        if not other.is_valid:
            self.is_valid = False
    
    @property
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0


class BaseModel(PydanticBaseModel):
    """基础模型类
    
    所有数据模型的基类，提供：
    - 基础字段（ID、时间戳等）
    - 序列化/反序列化
    - 验证机制
    - 版本控制
    """
    
    # 基础字段
    id: UUID = Field(default_factory=uuid4, description="唯一标识符")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    version: int = Field(default=1, description="版本号")
    
    # 元数据
    custom_fields: Dict[str, Any] = Field(default_factory=dict, description="自定义字段")
    tags: List[str] = Field(default_factory=list, description="标签")
    
    class Config:
        """Pydantic配置"""
        # 允许使用枚举值
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # 允许任意类型
        arbitrary_types_allowed = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }
    
    def __init__(self, **data):
        """初始化"""
        super().__init__(**data)
        # 确保更新时间
        if 'updated_at' not in data:
            self.updated_at = datetime.now()
    
    @validator('updated_at', pre=True, always=True)
    def set_updated_at(cls, v):
        """设置更新时间"""
        return v or datetime.now()
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
        self.version += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    def to_json(self, **kwargs) -> str:
        """转换为JSON字符串"""
        return self.json(**kwargs)
    
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """从字典创建实例"""
        return cls(**data)
    
    @classmethod
    def from_json(cls: Type[T], json_str: str) -> T:
        """从JSON字符串创建实例"""
        return cls.parse_raw(json_str)
    
    def calculate_checksum(self) -> str:
        """计算校验和"""
        # 排除时间戳和版本号的数据
        data = self.dict(exclude={'updated_at', 'version'})
        json_str = json.dumps(data, sort_keys=True, default=pydantic_encoder)
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    def validate_model(self) -> ValidationResult:
        """验证模型"""
        result = ValidationResult()
        
        # 基础验证
        if not self.id:
            result.add_error("ID不能为空")
        
        if not self.created_at:
            result.add_error("创建时间不能为空")
        
        if not self.updated_at:
            result.add_error("更新时间不能为空")
        
        if self.version < 1:
            result.add_error("版本号必须大于0")
        
        # 调用子类验证
        self._validate_specific(result)
        
        return result
    
    def _validate_specific(self, result: ValidationResult):
        """子类特定验证（由子类重写）"""
        pass
    
    def clone(self: T) -> T:
        """克隆对象"""
        data = self.dict()
        # 生成新的ID和时间戳
        data['id'] = uuid4()
        data['created_at'] = datetime.now()
        data['updated_at'] = datetime.now()
        data['version'] = 1
        return self.__class__(**data)
    
    def merge_custom_fields(self, fields: Dict[str, Any]):
        """合并自定义字段"""
        self.custom_fields.update(fields)
        self.update_timestamp()
    
    def add_tag(self, tag: str):
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)
            self.update_timestamp()
    
    def remove_tag(self, tag: str):
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.update_timestamp()
    
    def has_tag(self, tag: str) -> bool:
        """检查是否有标签"""
        return tag in self.tags


class ModelValidator(ABC):
    """模型验证器基类"""
    
    @abstractmethod
    def validate(self, model: BaseModel) -> ValidationResult:
        """验证模型"""
        pass
    
    @staticmethod
    def validate_required_string(value: Optional[str], field_name: str, 
                                max_length: Optional[int] = None) -> ValidationResult:
        """验证必需字符串字段"""
        result = ValidationResult()
        
        if not value or not value.strip():
            result.add_error(f"{field_name}不能为空")
            return result
        
        if max_length and len(value) > max_length:
            result.add_error(f"{field_name}长度不能超过{max_length}个字符")
        
        return result
    
    @staticmethod
    def validate_optional_string(value: Optional[str], field_name: str,
                                max_length: Optional[int] = None) -> ValidationResult:
        """验证可选字符串字段"""
        result = ValidationResult()
        
        if value and max_length and len(value) > max_length:
            result.add_error(f"{field_name}长度不能超过{max_length}个字符")
        
        return result
    
    @staticmethod
    def validate_range(value: Union[int, float], field_name: str,
                      min_value: Optional[Union[int, float]] = None,
                      max_value: Optional[Union[int, float]] = None) -> ValidationResult:
        """验证数值范围"""
        result = ValidationResult()
        
        if min_value is not None and value < min_value:
            result.add_error(f"{field_name}不能小于{min_value}")
        
        if max_value is not None and value > max_value:
            result.add_error(f"{field_name}不能大于{max_value}")
        
        return result
    
    @staticmethod
    def validate_list_length(value: List[Any], field_name: str,
                           min_length: Optional[int] = None,
                           max_length: Optional[int] = None) -> ValidationResult:
        """验证列表长度"""
        result = ValidationResult()
        
        if min_length is not None and len(value) < min_length:
            result.add_error(f"{field_name}至少需要{min_length}个元素")
        
        if max_length is not None and len(value) > max_length:
            result.add_error(f"{field_name}最多只能有{max_length}个元素")
        
        return result


class VersionedModel(BaseModel):
    """支持版本控制的模型"""
    
    # 版本历史
    version_history: List[Dict[str, Any]] = Field(default_factory=list, description="版本历史")
    
    def create_version_snapshot(self, message: str = ""):
        """创建版本快照"""
        snapshot = {
            'version': self.version,
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'data': self.dict(exclude={'version_history'})
        }
        self.version_history.append(snapshot)
        self.update_timestamp()
    
    def get_version_history(self) -> List[Dict[str, Any]]:
        """获取版本历史"""
        return self.version_history.copy()
    
    def restore_version(self, version: int) -> bool:
        """恢复到指定版本"""
        for snapshot in self.version_history:
            if snapshot['version'] == version:
                # 恢复数据（除了版本历史）
                data = snapshot['data'].copy()
                data['version_history'] = self.version_history
                
                # 更新当前对象
                for key, value in data.items():
                    if hasattr(self, key):
                        setattr(self, key, value)
                
                self.update_timestamp()
                return True
        
        return False
