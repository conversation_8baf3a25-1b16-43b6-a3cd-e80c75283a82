<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">39%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b___init___py.html">src\core\ai\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060___init___py.html">src\core\ai\adapters\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_anthropic_adapter_py.html">src\core\ai\adapters\anthropic_adapter.py</a></td>
                <td>101</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="43 101">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_deepseek_adapter_py.html">src\core\ai\adapters\deepseek_adapter.py</a></td>
                <td>28</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="18 28">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_openai_adapter_py.html">src\core\ai\adapters\openai_adapter.py</a></td>
                <td>103</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="72 103">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_708c621716977060_zhipu_adapter_py.html">src\core\ai\adapters\zhipu_adapter.py</a></td>
                <td>28</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="19 28">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_base_py.html">src\core\ai\base.py</a></td>
                <td>119</td>
                <td>4</td>
                <td>34</td>
                <td class="right" data-ratio="115 119">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2dc49558b138ec9b_manager_py.html">src\core\ai\manager.py</a></td>
                <td>132</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="105 132">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_de954926f2f9d31f___init___py.html">src\core\events\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259___init___py.html">src\core\models\__init__.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_base_py.html">src\core\models\base.py</a></td>
                <td>152</td>
                <td>4</td>
                <td>4</td>
                <td class="right" data-ratio="148 152">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_character_py.html">src\core\models\character.py</a></td>
                <td>229</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="209 229">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_enums_py.html">src\core\models\enums.py</a></td>
                <td>257</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="257 257">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_event_py.html">src\core\models\event.py</a></td>
                <td>194</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="165 194">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_project_py.html">src\core\models\project.py</a></td>
                <td>156</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="147 156">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_scene_py.html">src\core\models\scene.py</a></td>
                <td>166</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="147 166">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2a16132bb8ee9259_story_element_py.html">src\core\models\story_element.py</a></td>
                <td>167</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="84 167">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18___init___py.html">src\core\storage\__init__.py</a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_backup_py.html">src\core\storage\backup.py</a></td>
                <td>176</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="31 176">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_cache_py.html">src\core\storage\cache.py</a></td>
                <td>211</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="190 211">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_file_storage_py.html">src\core\storage\file_storage.py</a></td>
                <td>177</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="153 177">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_manager_py.html">src\core\storage\manager.py</a></td>
                <td>227</td>
                <td>192</td>
                <td>0</td>
                <td class="right" data-ratio="35 227">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_serializer_py.html">src\core\storage\serializer.py</a></td>
                <td>158</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="123 158">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_60458b4be718ff18_version_control_py.html">src\core\storage\version_control.py</a></td>
                <td>149</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="31 149">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15___init___py.html">src\core\validation\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_business_rules_py.html">src\core\validation\business_rules.py</a></td>
                <td>241</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="40 241">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_exceptions_py.html">src\core\validation\exceptions.py</a></td>
                <td>66</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="31 66">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_relationship_validators_py.html">src\core\validation\relationship_validators.py</a></td>
                <td>182</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="33 182">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_rules_py.html">src\core\validation\rules.py</a></td>
                <td>272</td>
                <td>224</td>
                <td>0</td>
                <td class="right" data-ratio="48 272">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb48bbbbe924ff15_validators_py.html">src\core\validation\validators.py</a></td>
                <td>109</td>
                <td>77</td>
                <td>28</td>
                <td class="right" data-ratio="32 109">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td>41</td>
                <td>41</td>
                <td>2</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_ai_assistant_py.html">src\ui\ai_assistant.py</a></td>
                <td>216</td>
                <td>216</td>
                <td>0</td>
                <td class="right" data-ratio="0 216">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_character_manager_py.html">src\ui\character_manager.py</a></td>
                <td>325</td>
                <td>325</td>
                <td>0</td>
                <td class="right" data-ratio="0 325">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_event_manager_py.html">src\ui\event_manager.py</a></td>
                <td>294</td>
                <td>294</td>
                <td>0</td>
                <td class="right" data-ratio="0 294">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_main_window_py.html">src\ui\main_window.py</a></td>
                <td>205</td>
                <td>205</td>
                <td>7</td>
                <td class="right" data-ratio="0 205">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_project_dialog_py.html">src\ui\project_dialog.py</a></td>
                <td>197</td>
                <td>197</td>
                <td>0</td>
                <td class="right" data-ratio="0 197">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_scene_manager_py.html">src\ui\scene_manager.py</a></td>
                <td>306</td>
                <td>306</td>
                <td>0</td>
                <td class="right" data-ratio="0 306">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_settings_dialog_py.html">src\ui\settings_dialog.py</a></td>
                <td>283</td>
                <td>283</td>
                <td>0</td>
                <td class="right" data-ratio="0 283">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_writing_interface_py.html">src\ui\writing_interface.py</a></td>
                <td>241</td>
                <td>241</td>
                <td>0</td>
                <td class="right" data-ratio="0 241">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6156a86a215061be___init___py.html">src\utils\__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>5973</td>
                <td>3655</td>
                <td>75</td>
                <td class="right" data-ratio="2318 5973">39%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_6156a86a215061be___init___py.html"></a>
        <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
