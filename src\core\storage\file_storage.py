"""文件存储管理器

提供文件系统的CRUD操作，包括：
- 项目文件结构管理
- 文件读写操作
- 文件锁定机制
- 原子操作保证
"""

import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import UUID
import json
from datetime import datetime

from .serializer import JSONSerializer


class FileStorageError(Exception):
    """文件存储错误"""
    pass





class FileStorage:
    """文件存储管理器
    
    提供项目文件的存储和管理功能，包括：
    - 项目目录结构管理
    - 文件CRUD操作
    - 原子写入操作
    - 文件锁定机制
    """
    
    def __init__(self, base_path: Union[str, Path]):
        """初始化文件存储管理器

        Args:
            base_path: 基础存储路径
        """
        self.base_path = Path(base_path)
        self.serializer = JSONSerializer()

        # 确保基础目录存在
        self.base_path.mkdir(parents=True, exist_ok=True)
    
    def create_project_structure(self, project_id: UUID) -> Path:
        """创建项目目录结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            项目根目录路径
            
        Raises:
            FileStorageError: 创建失败
        """
        try:
            project_path = self.base_path / "projects" / str(project_id)

            # 检查项目是否已存在
            if project_path.exists():
                raise FileStorageError(f"项目目录已存在: {project_path}")
            
            # 创建目录结构
            directories = [
                project_path,
                project_path / "characters",
                project_path / "scenes", 
                project_path / "events",
                project_path / "chapters",
                project_path / "chapters" / "content",
                project_path / "arcs",
                project_path / "backups",
                project_path / "cache",
                project_path / "exports",
                project_path / "resources",
                project_path / "resources" / "images",
                project_path / "resources" / "documents"
            ]
            
            for directory in directories:
                directory.mkdir(parents=True, exist_ok=True)
            
            # 创建项目配置文件
            config_file = project_path / "project.json"
            config_data = {
                "project_id": str(project_id),
                "created_at": datetime.now().isoformat(),
                "version": "1.0.0",
                "structure_version": "2.0"
            }
            
            self._write_json_file(config_file, config_data)
            
            return project_path
            
        except Exception as e:
            raise FileStorageError(f"创建项目结构失败: {e}") from e
    
    def save_project(self, project: Any, project_path: Optional[Path] = None) -> Path:
        """保存项目数据

        Args:
            project: 项目对象
            project_path: 项目路径（可选）

        Returns:
            保存的文件路径
        """
        if not project_path:
            # 使用项目ID的字符串形式作为目录名，而不是整个对象的字符串表示
            project_path = self.base_path / "projects" / str(project.id)

        file_path = project_path / "project.json"
        return self._save_object(project, file_path)
    
    def load_project(self, project_id: UUID, project_class: type) -> Any:
        """加载项目数据

        Args:
            project_id: 项目ID
            project_class: 项目类

        Returns:
            项目对象
        """
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "project.json"
        return self._load_object(file_path, project_class)
    
    def save_character(self, character: Any, project_id: UUID) -> Path:
        """保存角色数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "characters" / f"{character.id}.json"
        return self._save_object(character, file_path)

    def load_character(self, character_id: UUID, project_id: UUID, character_class: type) -> Any:
        """加载角色数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "characters" / f"{character_id}.json"
        return self._load_object(file_path, character_class)

    def save_scene(self, scene: Any, project_id: UUID) -> Path:
        """保存场景数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "scenes" / f"{scene.id}.json"
        return self._save_object(scene, file_path)

    def load_scene(self, scene_id: UUID, project_id: UUID, scene_class: type) -> Any:
        """加载场景数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "scenes" / f"{scene_id}.json"
        return self._load_object(file_path, scene_class)

    def save_event(self, event: Any, project_id: UUID) -> Path:
        """保存事件数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "events" / f"{event.id}.json"
        return self._save_object(event, file_path)

    def load_event(self, event_id: UUID, project_id: UUID, event_class: type) -> Any:
        """加载事件数据"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "events" / f"{event_id}.json"
        return self._load_object(file_path, event_class)
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """列出所有项目"""
        projects = []

        projects_dir = self.base_path / "projects"
        if not projects_dir.exists():
            return projects

        for item in projects_dir.iterdir():
            if item.is_dir():
                try:
                    # 尝试解析为UUID
                    project_id = UUID(item.name)
                    config_file = item / "project.json"

                    if config_file.exists():
                        config_data = self._read_json_file(config_file)
                        projects.append({
                            'id': str(project_id),
                            'path': str(item),
                            'config': config_data
                        })
                except ValueError:
                    # 不是有效的UUID，跳过
                    continue

        return projects
    
    def list_characters(self, project_id: UUID) -> List[UUID]:
        """列出项目中的所有角色ID"""
        project_path = self.base_path / "projects" / str(project_id)
        characters_path = project_path / "characters"
        
        if not characters_path.exists():
            return []
        
        character_ids = []
        for file_path in characters_path.glob("*.json"):
            try:
                character_id = UUID(file_path.stem)
                character_ids.append(character_id)
            except ValueError:
                continue
        
        return character_ids
    
    def list_scenes(self, project_id: UUID) -> List[UUID]:
        """列出项目中的所有场景ID"""
        project_path = self.base_path / "projects" / str(project_id)
        scenes_path = project_path / "scenes"

        if not scenes_path.exists():
            return []

        scene_ids = []
        for file_path in scenes_path.glob("*.json"):
            try:
                scene_id = UUID(file_path.stem)
                scene_ids.append(scene_id)
            except ValueError:
                continue

        return scene_ids

    def list_events(self, project_id: UUID) -> List[UUID]:
        """列出项目中的所有事件ID"""
        project_path = self.base_path / "projects" / str(project_id)
        events_path = project_path / "events"
        
        if not events_path.exists():
            return []
        
        event_ids = []
        for file_path in events_path.glob("*.json"):
            try:
                event_id = UUID(file_path.stem)
                event_ids.append(event_id)
            except ValueError:
                continue
        
        return event_ids
    
    def delete_project(self, project_id: UUID) -> bool:
        """删除项目"""
        try:
            project_path = self.base_path / "projects" / str(project_id)
            if project_path.exists():
                shutil.rmtree(project_path)
                return True
            return False
        except Exception as e:
            raise FileStorageError(f"删除项目失败: {e}") from e

    def delete_character(self, character_id: UUID, project_id: UUID) -> bool:
        """删除角色"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "characters" / f"{character_id}.json"
        return self._delete_file(file_path)

    def delete_scene(self, scene_id: UUID, project_id: UUID) -> bool:
        """删除场景"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "scenes" / f"{scene_id}.json"
        return self._delete_file(file_path)

    def delete_event(self, event_id: UUID, project_id: UUID) -> bool:
        """删除事件"""
        project_path = self.base_path / "projects" / str(project_id)
        file_path = project_path / "events" / f"{event_id}.json"
        return self._delete_file(file_path)
    
    def _save_object(self, obj: Any, file_path: Path) -> Path:
        """保存对象到文件"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 原子写入
            with self._atomic_write(file_path) as temp_path:
                self.serializer.serialize(obj, temp_path)
            
            return file_path
            
        except Exception as e:
            raise FileStorageError(f"保存对象失败: {e}") from e
    
    def _load_object(self, file_path: Path, target_class: type) -> Any:
        """从文件加载对象"""
        try:
            if not file_path.exists():
                raise FileStorageError(f"文件不存在: {file_path}")
            
            return self.serializer.deserialize(file_path=file_path, target_class=target_class)
            
        except Exception as e:
            raise FileStorageError(f"加载对象失败: {e}") from e
    
    def _delete_file(self, file_path: Path) -> bool:
        """删除文件"""
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return False
        except Exception as e:
            raise FileStorageError(f"删除文件失败: {e}") from e
    
    def _atomic_write(self, file_path: Path):
        """原子写入上下文管理器"""
        class AtomicWrite:
            def __init__(self, target_path: Path):
                self.target_path = target_path
                self.temp_path = None
            
            def __enter__(self):
                # 创建临时文件
                temp_dir = self.target_path.parent
                self.temp_path = temp_dir / f".{self.target_path.name}.tmp"
                return self.temp_path
            
            def __exit__(self, exc_type, _exc_val, _exc_tb):
                if exc_type is None and self.temp_path and self.temp_path.exists():
                    # 成功时移动临时文件到目标位置
                    self.temp_path.replace(self.target_path)
                elif self.temp_path and self.temp_path.exists():
                    # 失败时删除临时文件
                    self.temp_path.unlink()
        
        return AtomicWrite(file_path)
    
    def _write_json_file(self, file_path: Path, data: Dict[str, Any]):
        """写入JSON文件"""
        with self._atomic_write(file_path) as temp_path:
            temp_path.write_text(
                json.dumps(data, indent=2, ensure_ascii=False),
                encoding='utf-8'
            )
    
    def _read_json_file(self, file_path: Path) -> Dict[str, Any]:
        """读取JSON文件"""
        return json.loads(file_path.read_text(encoding='utf-8'))
