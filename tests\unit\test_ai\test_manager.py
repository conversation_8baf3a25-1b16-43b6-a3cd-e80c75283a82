"""
AI服务管理器测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

from src.core.models.project import AIConfiguration
from src.core.ai.base import AIProvider, GenerationType, GenerationContext, AIServiceError
from src.core.ai.manager import AIServiceManager, ContentGenerator


class TestAIServiceManager:
    """测试AI服务管理器"""
    
    @pytest.fixture
    def config(self):
        """创建AI配置"""
        return AIConfiguration(
            default_provider="openai",
            openai_api_key="test-openai-key",
            openai_model="gpt-4",
            anthropic_api_key="test-anthropic-key",
            anthropic_model="claude-3-haiku-20240307",
            max_tokens=1000,
            temperature=0.8,
            timeout=60,
            max_retries=2
        )
    
    @pytest.fixture
    def manager(self, config):
        """创建AI服务管理器"""
        return AIServiceManager(config)
    
    def test_init(self, manager, config):
        """测试初始化"""
        assert manager.config == config
        assert manager._services == {}
        assert manager._prompt_manager is not None
        assert manager._initialized is False
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, manager):
        """测试成功初始化"""
        with patch('src.core.ai.manager.OpenAIAdapter') as mock_openai, \
             patch('src.core.ai.manager.AnthropicAdapter') as mock_anthropic:
            
            # 模拟适配器
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            mock_anthropic_instance = AsyncMock()
            mock_anthropic.return_value = mock_anthropic_instance
            
            await manager.initialize()
            
            assert manager._initialized is True
            assert AIProvider.OPENAI in manager._services
            assert AIProvider.ANTHROPIC in manager._services
            
            # 验证适配器被正确初始化
            mock_openai.assert_called_once_with(
                api_key="test-openai-key",
                model="gpt-4",
                base_url="https://api.openai.com/v1"
            )
            mock_openai_instance.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_with_errors(self, manager):
        """测试初始化时有错误"""
        with patch('src.core.ai.manager.OpenAIAdapter') as mock_openai, \
             patch('src.core.ai.manager.AnthropicAdapter') as mock_anthropic:
            
            # OpenAI初始化成功
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            # Anthropic初始化失败
            mock_anthropic.side_effect = Exception("初始化失败")
            
            await manager.initialize()
            
            assert manager._initialized is True
            assert AIProvider.OPENAI in manager._services
            assert AIProvider.ANTHROPIC not in manager._services
    
    @pytest.mark.asyncio
    async def test_generate_content_success(self, manager):
        """测试成功生成内容"""
        # 模拟服务
        mock_service = AsyncMock()
        mock_result = MagicMock()
        mock_service.generate_content.return_value = mock_result
        manager._services[AIProvider.OPENAI] = mock_service
        manager._initialized = True
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        
        result = await manager.generate_content(
            context=context,
            name="张三",
            gender="男",
            age=25,
            style="现代都市"
        )
        
        assert result == mock_result
        mock_service.generate_content.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_content_with_custom_prompt(self, manager):
        """测试使用自定义提示词生成内容"""
        mock_service = AsyncMock()
        mock_result = MagicMock()
        mock_service.generate_content.return_value = mock_result
        manager._services[AIProvider.OPENAI] = mock_service
        manager._initialized = True
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        custom_prompt = "自定义提示词"
        
        result = await manager.generate_content(
            context=context,
            custom_prompt=custom_prompt
        )
        
        assert result == mock_result
        # 验证使用了自定义提示词
        call_args = mock_service.generate_content.call_args
        assert call_args[0][0] == custom_prompt
    
    @pytest.mark.asyncio
    async def test_generate_content_with_retry(self, manager):
        """测试生成内容重试机制"""
        mock_service = AsyncMock()
        mock_service.generate_content.side_effect = [
            AIServiceError("第一次失败", AIProvider.OPENAI),
            AIServiceError("第二次失败", AIProvider.OPENAI),
            MagicMock()  # 第三次成功
        ]
        mock_service.provider = AIProvider.OPENAI
        manager._services[AIProvider.OPENAI] = mock_service
        manager._initialized = True
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        
        result = await manager.generate_content(
            context=context,
            name="张三",
            gender="男",
            age=25,
            style="现代都市"
        )
        
        # 验证重试了3次
        assert mock_service.generate_content.call_count == 3
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_generate_content_stream(self, manager):
        """测试流式生成内容"""
        mock_service = AsyncMock()
        
        # 模拟流式响应
        async def mock_stream():
            yield MagicMock(content="第一部分")
            yield MagicMock(content="第二部分")
        
        mock_service.generate_content_stream.return_value = mock_stream()
        manager._services[AIProvider.OPENAI] = mock_service
        manager._initialized = True
        
        context = GenerationContext(content_type=GenerationType.CHARACTER_DESCRIPTION)
        
        chunks = []
        async for chunk in manager.generate_content_stream(
            context=context,
            name="张三",
            gender="男",
            age=25,
            style="现代都市"
        ):
            chunks.append(chunk)
        
        assert len(chunks) == 2
        assert chunks[0].content == "第一部分"
        assert chunks[1].content == "第二部分"
    
    @pytest.mark.asyncio
    async def test_validate_service(self, manager):
        """测试验证服务"""
        mock_service = AsyncMock()
        mock_service.validate_api_key.return_value = True
        manager._services[AIProvider.OPENAI] = mock_service
        manager._initialized = True
        
        result = await manager.validate_service(AIProvider.OPENAI)
        assert result is True
        
        # 测试不存在的服务
        result = await manager.validate_service(AIProvider.DEEPSEEK)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_health_check(self, manager):
        """测试健康检查"""
        mock_openai = AsyncMock()
        mock_openai.health_check.return_value = True
        
        mock_anthropic = AsyncMock()
        mock_anthropic.health_check.return_value = False
        
        manager._services[AIProvider.OPENAI] = mock_openai
        manager._services[AIProvider.ANTHROPIC] = mock_anthropic
        manager._initialized = True
        
        results = await manager.health_check()
        
        assert results[AIProvider.OPENAI] is True
        assert results[AIProvider.ANTHROPIC] is False
    
    def test_get_available_providers(self, manager):
        """测试获取可用服务商"""
        manager._services[AIProvider.OPENAI] = MagicMock()
        manager._services[AIProvider.ANTHROPIC] = MagicMock()
        
        providers = manager.get_available_providers()
        assert AIProvider.OPENAI in providers
        assert AIProvider.ANTHROPIC in providers
        assert len(providers) == 2
    
    def test_get_prompt_manager(self, manager):
        """测试获取提示词管理器"""
        prompt_manager = manager.get_prompt_manager()
        assert prompt_manager is not None
        assert prompt_manager == manager._prompt_manager
    
    def test_select_service_preferred(self, manager):
        """测试选择指定服务"""
        mock_openai = MagicMock()
        mock_anthropic = MagicMock()
        manager._services[AIProvider.OPENAI] = mock_openai
        manager._services[AIProvider.ANTHROPIC] = mock_anthropic
        
        service = manager._select_service(AIProvider.ANTHROPIC)
        assert service == mock_anthropic
    
    def test_select_service_default(self, manager):
        """测试选择默认服务"""
        mock_openai = MagicMock()
        manager._services[AIProvider.OPENAI] = mock_openai
        
        service = manager._select_service()
        assert service == mock_openai
    
    def test_select_service_random(self, manager):
        """测试随机选择服务"""
        mock_anthropic = MagicMock()
        manager._services[AIProvider.ANTHROPIC] = mock_anthropic
        # 默认服务不可用，应该随机选择
        
        service = manager._select_service()
        assert service == mock_anthropic
    
    def test_select_fallback_service(self, manager):
        """测试选择故障转移服务"""
        mock_openai = MagicMock()
        mock_anthropic = MagicMock()
        manager._services[AIProvider.OPENAI] = mock_openai
        manager._services[AIProvider.ANTHROPIC] = mock_anthropic
        
        # OpenAI失败，应该选择Anthropic
        service = manager._select_fallback_service(AIProvider.OPENAI)
        assert service == mock_anthropic
        
        # 只有一个服务时，应该返回None
        manager._services = {AIProvider.OPENAI: mock_openai}
        service = manager._select_fallback_service(AIProvider.OPENAI)
        assert service is None


class TestContentGenerator:
    """测试内容生成器"""
    
    @pytest.fixture
    def ai_manager(self):
        """创建模拟AI管理器"""
        return AsyncMock()
    
    @pytest.fixture
    def generator(self, ai_manager):
        """创建内容生成器"""
        return ContentGenerator(ai_manager)
    
    @pytest.mark.asyncio
    async def test_generate_character_description(self, generator, ai_manager):
        """测试生成角色描述"""
        mock_result = MagicMock()
        ai_manager.generate_content.return_value = mock_result
        
        result = await generator.generate_character_description(
            name="张三",
            gender="男",
            age=25,
            style="现代都市"
        )
        
        assert result == mock_result
        ai_manager.generate_content.assert_called_once()
        
        # 验证调用参数
        call_args = ai_manager.generate_content.call_args
        context = call_args[1]['context']
        assert context.content_type == GenerationType.CHARACTER_DESCRIPTION
    
    @pytest.mark.asyncio
    async def test_generate_scene_description(self, generator, ai_manager):
        """测试生成场景描述"""
        mock_result = MagicMock()
        ai_manager.generate_content.return_value = mock_result
        
        result = await generator.generate_scene_description(
            name="咖啡厅",
            location="市中心",
            time="下午",
            style="现代都市"
        )
        
        assert result == mock_result
        
        # 验证调用参数
        call_args = ai_manager.generate_content.call_args
        context = call_args[1]['context']
        assert context.content_type == GenerationType.SCENE_DESCRIPTION
    
    @pytest.mark.asyncio
    async def test_generate_plot_development(self, generator, ai_manager):
        """测试生成情节发展"""
        mock_result = MagicMock()
        ai_manager.generate_content.return_value = mock_result
        
        result = await generator.generate_plot_development(
            current_situation="主角遇到困难",
            goal="解决问题",
            style="现代都市"
        )
        
        assert result == mock_result
        
        # 验证调用参数
        call_args = ai_manager.generate_content.call_args
        context = call_args[1]['context']
        assert context.content_type == GenerationType.PLOT_DEVELOPMENT
    
    @pytest.mark.asyncio
    async def test_generate_dialogue(self, generator, ai_manager):
        """测试生成对话"""
        mock_result = MagicMock()
        ai_manager.generate_content.return_value = mock_result
        
        result = await generator.generate_dialogue(
            characters="张三、李四",
            scene="咖啡厅",
            situation="初次见面",
            style="现代都市"
        )
        
        assert result == mock_result
        
        # 验证调用参数
        call_args = ai_manager.generate_content.call_args
        context = call_args[1]['context']
        assert context.content_type == GenerationType.DIALOGUE
    
    @pytest.mark.asyncio
    async def test_optimize_content(self, generator, ai_manager):
        """测试优化内容"""
        mock_result = MagicMock()
        ai_manager.generate_content.return_value = mock_result
        
        result = await generator.optimize_content(
            content="原始内容",
            requirements="提升文字质量"
        )
        
        assert result == mock_result
        
        # 验证调用参数
        call_args = ai_manager.generate_content.call_args
        context = call_args[1]['context']
        assert context.content_type == GenerationType.CONTENT_OPTIMIZATION
        assert context.existing_content == "原始内容"
