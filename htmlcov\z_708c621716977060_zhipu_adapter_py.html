<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src\core\ai\adapters\zhipu_adapter.py: 68%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src\core\ai\adapters\zhipu_adapter.py</b>:
            <span class="pc_cov">68%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">28 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">19<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">9<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_708c621716977060_openai_adapter_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_2dc49558b138ec9b_base_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">&#26234;&#35889;AI&#26381;&#21153;&#36866;&#37197;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">&#23454;&#29616;&#26234;&#35889;AI API&#30340;&#20855;&#20307;&#36866;&#37197;&#36923;&#36753;&#12290;&#26234;&#35889;AI API&#20860;&#23481;OpenAI&#26684;&#24335;&#65292;&#22240;&#27492;&#32487;&#25215;OpenAI&#36866;&#37197;&#22120;&#12290;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="op">.</span><span class="nam">base</span> <span class="key">import</span> <span class="nam">AIProvider</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="op">.</span><span class="nam">openai_adapter</span> <span class="key">import</span> <span class="nam">OpenAIAdapter</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">class</span> <span class="nam">ZhipuAdapter</span><span class="op">(</span><span class="nam">OpenAIAdapter</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="str">"""&#26234;&#35889;AI&#26381;&#21153;&#36866;&#37197;&#22120;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">api_key</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">model</span><span class="op">:</span> <span class="nam">str</span> <span class="op">=</span> <span class="str">"glm-4"</span><span class="op">,</span> <span class="nam">base_url</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">        <span class="com"># &#26234;&#35889;AI&#20351;&#29992;&#33258;&#24049;&#30340;provider&#31867;&#22411;&#65292;&#20294;&#32487;&#25215;OpenAI&#30340;&#23454;&#29616;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">        <span class="nam">super</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">__init__</span><span class="op">(</span><span class="nam">api_key</span><span class="op">,</span> <span class="nam">model</span><span class="op">,</span> <span class="nam">base_url</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span> <span class="op">=</span> <span class="nam">AIProvider</span><span class="op">.</span><span class="nam">ZHIPU</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_default_base_url</span> <span class="op">=</span> <span class="str">"https://open.bigmodel.cn/api/paas/v4"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">get_available_models</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">List</span><span class="op">[</span><span class="nam">str</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">        <span class="str">"""&#33719;&#21462;&#21487;&#29992;&#27169;&#22411;&#21015;&#34920;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">        <span class="com"># &#26234;&#35889;AI&#30340;&#24050;&#30693;&#27169;&#22411;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">        <span class="key">return</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">            <span class="str">"glm-4"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">            <span class="str">"glm-4-0520"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">            <span class="str">"glm-4-air"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">            <span class="str">"glm-4-airx"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">            <span class="str">"glm-4-flash"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">            <span class="str">"glm-4v"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">            <span class="str">"glm-3-turbo"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">    <span class="key">def</span> <span class="nam">_build_system_prompt</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">context</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="str">"""&#26500;&#24314;&#31995;&#32479;&#25552;&#31034;&#35789;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">base_prompt</span> <span class="op">=</span> <span class="str">"&#20320;&#26159;&#26234;&#35889;AI&#24320;&#21457;&#30340;GLM&#22823;&#27169;&#22411;&#65292;&#19987;&#38376;&#21327;&#21161;&#23567;&#35828;&#21019;&#20316;&#12290;&#35831;&#29992;&#20013;&#25991;&#22238;&#31572;&#65292;&#21457;&#25381;&#20320;&#30340;&#21019;&#20316;&#33021;&#21147;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="key">if</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"character_description"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#29983;&#21160;&#12289;&#31435;&#20307;&#30340;&#35282;&#33394;&#25551;&#36848;&#65292;&#31361;&#20986;&#20013;&#22269;&#25991;&#21270;&#29305;&#33394;&#21644;&#20154;&#29289;&#28145;&#24230;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"scene_description"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#23500;&#26377;&#30011;&#38754;&#24863;&#30340;&#22330;&#26223;&#25551;&#36848;&#65292;&#27880;&#37325;&#25991;&#23398;&#24615;&#21644;&#33402;&#26415;&#24863;&#26579;&#21147;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"plot_development"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#24341;&#20154;&#20837;&#32988;&#30340;&#24773;&#33410;&#21457;&#23637;&#65292;&#20307;&#29616;&#28145;&#21402;&#30340;&#25991;&#23398;&#21151;&#24213;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"dialogue"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#21019;&#20316;&#33258;&#28982;&#12289;&#31526;&#21512;&#35282;&#33394;&#24615;&#26684;&#30340;&#23545;&#35805;&#65292;&#20307;&#29616;&#35821;&#35328;&#30340;&#39749;&#21147;&#21644;&#25991;&#21270;&#20869;&#28085;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="key">elif</span> <span class="nam">context</span><span class="op">.</span><span class="nam">content_type</span><span class="op">.</span><span class="nam">value</span> <span class="op">==</span> <span class="str">"content_optimization"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span> <span class="op">+</span> <span class="str">"&#35831;&#19987;&#27880;&#20110;&#20248;&#21270;&#21644;&#28070;&#33394;&#25991;&#26412;&#20869;&#23481;&#65292;&#25552;&#21319;&#25991;&#23398;&#21697;&#36136;&#21644;&#34920;&#36798;&#25928;&#26524;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">            <span class="key">return</span> <span class="nam">base_prompt</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="key">def</span> <span class="nam">_handle_error</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">error</span><span class="op">:</span> <span class="nam">Exception</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">        <span class="str">"""&#22788;&#29702;&#38169;&#35823;&#65292;&#26356;&#26032;&#38169;&#35823;&#28040;&#24687;&#20013;&#30340;&#26381;&#21153;&#21830;&#21517;&#31216;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="nam">ai_error</span> <span class="op">=</span> <span class="nam">super</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">_handle_error</span><span class="op">(</span><span class="nam">error</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="com"># &#23558;&#38169;&#35823;&#28040;&#24687;&#20013;&#30340;OpenAI&#26367;&#25442;&#20026;&#26234;&#35889;AI</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">        <span class="nam">ai_error</span><span class="op">.</span><span class="nam">args</span> <span class="op">=</span> <span class="op">(</span><span class="nam">ai_error</span><span class="op">.</span><span class="nam">args</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">.</span><span class="nam">replace</span><span class="op">(</span><span class="str">"OpenAI"</span><span class="op">,</span> <span class="str">"&#26234;&#35889;AI"</span><span class="op">)</span><span class="op">,</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="nam">ai_error</span><span class="op">.</span><span class="nam">provider</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">provider</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="key">return</span> <span class="nam">ai_error</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_708c621716977060_openai_adapter_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_2dc49558b138ec9b_base_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.6">coverage.py v7.10.6</a>,
            created at 2025-09-13 20:43 +0800
        </p>
    </div>
</footer>
</body>
</html>
