1.主界面：软件启动时进入主界面，主界面包含新建按钮，打开按钮，设置按钮；
  原型图：见img/主界面.png
  主要元素: 新建按钮，点击打开新建项目界面；
           打开按钮，点击打开选择已存在项目界面，并进入创作界面；
           设置按钮，点击打开设置界面；

2.新建项目界面：创建项目的入口，需要输入项目名、作者、项目类型、简介等信息，并点击新建按钮，新建项目。
  原型图：见img/新建项目界面.png
  主要元素：项目名文本框和输入框，用来输入项目名；
          作者文本框和输入框，用来输入作者名；
          类型文本框和下拉选择框，通过选择圣经系统中包含的项目类型来进行选择；
          简介文本框和输入框，用来输入项目简介；
          新建按钮，点击在指定目录下新建项目，并进入创作界面；
          返回按钮，点击返回主界面；

3.打开项目界面：选择已存在项目，并进入创作界面。
  原型图：见img/打开项目界面.png
  主要元素：主界面显示已存在的项目，可以点击选中，如果不存在则为空；
          打开按钮，点击打开已选择的项目，进入创作界面；
          取消按钮，点击取消选择，返回主界面；

4.创作界面：软件最重要的界面，用户可以在此界面进行创作，调用AI功能进行合作创作，完成作品的创作，优化和保存。
  原型图：见img/创作界面.png
  主要元素：
        故事阶段显示区域：位于左侧，通过下方的三个一体的按钮进行切换，切换到不同的维度来查看故事的进度，维度逐步细化。其中大纲维度为整个故事的全部进度，包含故事从开始到结束的各个阶段，以及各个阶段之间的顺序；主线维度为当前故事分卷的主要进度，包含故事不同卷的主要进度，以及各个卷之间的顺序；章节维度为当前故事分卷的具体进度，包含故事具体的发生事件推进；通过这部分的内容，创作者可以对故事进行规划，并制定故事进度，把握住当前的故事进度。每个阶段可以进行点击，弹出一个小窗口显示当前维度下当前阶段的内容简介。

        小说编辑/AI辅助生成区域：位于中间上方，创作界面占比最大的区域。主要用来进行AI内容的生成，用户可以输入内容，也可以操作章节生成功能使用AI生成内容。注意只有这两种方式可以在当前区域输入内容。

        AI交互区域：位于中间下方，笔落项目的灵魂，AI交互区域主要用来进行AI的交互，用户可以输入一些指令来进行AI的交互，AI会进行反馈。另外可以通过部分功能调用AI进行一些交互指令得生成。例如可以通过灵感生成来获取一些灵感，再使用灵感来作为提示，让AI生成一些内容。这部分还要分为
        内容输入区域，负责接收用户或者AI功能的输入，并显示给用户；模型选择区域，根据设置中的配置显示一个可用模型的下拉框，供创作者选择；发送按钮，点击发送按钮，将用户输入的内容发送给AI，并等待AI的响应；


        主要人物区域：位于右侧上方，显示当前故事中的主要人物，且显示的人物是根据故事阶段中选择的维度进行筛选，例如，如果选择了大纲维度，则显示的是故事从开始到结束的主要的任务，主角和重要配角；如果选择了主线维度，则显示的是当前故事分卷的主要进度中出现的主要人物；如果选择了章节维度，则显示的是当前章节下显示的主要人物。人物可以通过点击进行查看，弹出一个小窗口显示人物的简介，以及人物性格、能力、成长轨迹等。

        独立功能区域：位于右侧下方，主要是的一些笔落的专属功能。灵感生成，点击后可以根据当前内容生成一个符合故事发展的灵感。章节生成，点击后弹出一个窗口，在窗口内输入确定的章节概要，确认后生成完整的章节内容。格式化，点击后根据提供的模版进行章节内容的格式化。保存，点击后将当前章节内容保存到一个文件中，保存成功会弹出一个提示框。

5.设置界面：为笔落项目的配置进行设置，左侧包含五个按钮，通过点击三个设置相关按钮在右侧切换不同的设置界面，另外还包含保存和退出按钮。
  原型图：见img/设置界面.png
  主要元素：常规设置，包含一些软件的常见设置，例如背景等，需要进行讨论。
          AI设置，主要包含为不同的AI大模型提供商提供不同的设置，例如OpenAI、DeepSeek、ChatGPT、智谱等厂商的设置，包括API密钥输入和模型选择，测试输入的模型是否可用，保存当前的设置。完成后会在创作界面的AI交互区域的模型选择区域显示当前配置成功的模型。
          圣经系统设置，包含为不同类型的小说配置不同的提示词和专属知识库，在创建项目的时候选择类型会自动将圣经内不同的类型的提示词和知识库导入到当前项目内，为小说创作生成提供支持。
          保存设置按钮，设置界面完成配置后需要点击按钮进行保存到设置相关的配置文件内。
          退出按钮，点击按钮将退出设置界面，并返回主界面。
