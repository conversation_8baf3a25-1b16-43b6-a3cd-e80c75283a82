# -*- mode: python ; coding: utf-8 -*-
from pathlib import Path

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[r'E:\project\bamboofall_py_3'],
    binaries=[],
    datas=[
        ('src/ui/*.py', 'ui'),
        ('src/core', 'core'),
        ('resources', 'resources'),
        ('Docs', 'Docs'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'pydantic',
        'uuid',
        'datetime',
        'json',
        'pathlib',
        'typing',
        'enum',
        'dataclasses',
        'concurrent.futures',
        'threading',
        'queue',
        'tempfile',
        'shutil',
        'zipfile',
        'hashlib',
        'time',
        'psutil',
        'requests',
        'anthropic',
        'openai',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='笔落',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='resources/icon.ico' if Path('resources/icon.ico').exists() else None,
)
